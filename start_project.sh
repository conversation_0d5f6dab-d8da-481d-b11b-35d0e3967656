#!/bin/bash

# 启动后端 FastAPI 服务
echo "启动后端 FastAPI 服务..."
cd api
pip install -r requirements.txt -i https://mirrors.aliyun.com/pypi/simple/
echo "更新数据库 schema..."
python3 update_database.py
uvicorn main:app --reload --port 8000 &
BACKEND_PID=$!
cd ..

# 启动前端 Vite 服务
echo "启动前端 Vite 服务..."
cd ui
npm install
npm run dev &
FRONTEND_PID=$!
cd ..

echo "后端服务 PID: $BACKEND_PID"
echo "前端服务 PID: $FRONTEND_PID"
echo "项目启动完成！后端运行在 http://localhost:8000，前端运行在 http://localhost:5173"
echo "可以使用 Ctrl+C 终止脚本，或者使用 'kill $BACKEND_PID $FRONTEND_PID' 命令终止服务"