-- 创建用户表
CREATE TABLE users (
    id INTEGER NOT NULL, 
    username VA<PERSON>HAR, 
    password VARCHAR, 
    name VARCHAR NOT NULL, 
    qywx_id VARCHAR, 
    is_admin BOOLEAN, 
    is_active BOOLEAN, 
    created_at DATETIME, 
    PRIMARY KEY (id)
);

-- 创建用户表索引
CREATE UNIQUE INDEX ix_users_username ON users (username);
CREATE INDEX ix_users_id ON users (id);

-- 创建项目表
CREATE TABLE projects (
    id INTEGER NOT NULL, 
    name VARCHAR NOT NULL, 
    git_repo_url VARCHAR NOT NULL, 
    description TEXT, 
    creator_id INTEGER NOT NULL, 
    created_at DATETIME, 
    PRIMARY KEY (id), 
    FOREIGN KEY(creator_id) REFERENCES users (id)
);

-- 创建项目表索引
CREATE INDEX ix_projects_id ON projects (id);

-- 创建项目开发人员关联表
CREATE TABLE project_developers (
    project_id INTEGER NOT NULL, 
    user_id INTEGER NOT NULL, 
    join_date DATETIME, 
    PRIMARY KEY (project_id, user_id), 
    FOREIGN KEY(project_id) REFERENCES projects (id), 
    FOREIGN KEY(user_id) REFERENCES users (id)
);

-- 创建项目测试人员关联表
CREATE TABLE project_testers (
    project_id INTEGER NOT NULL, 
    user_id INTEGER NOT NULL, 
    join_date DATETIME, 
    PRIMARY KEY (project_id, user_id), 
    FOREIGN KEY(project_id) REFERENCES projects (id), 
    FOREIGN KEY(user_id) REFERENCES users (id)
);

-- 创建需求表
CREATE TABLE requirements (
    id INTEGER NOT NULL, 
    project_id INTEGER NOT NULL, 
    requirement_code VARCHAR(10) NOT NULL, 
    title VARCHAR(100) NOT NULL, 
    content TEXT NOT NULL, 
    type VARCHAR(11) NOT NULL, 
    priority VARCHAR(2) NOT NULL, 
    status VARCHAR(10) NOT NULL, 
    start_date DATETIME NOT NULL, 
    end_date DATETIME NOT NULL, 
    submitter_id INTEGER NOT NULL, 
    submit_time DATETIME NOT NULL, 
    update_time DATETIME, 
    git_branch VARCHAR(200), 
    PRIMARY KEY (id), 
    FOREIGN KEY(project_id) REFERENCES projects (id), 
    UNIQUE (requirement_code), 
    FOREIGN KEY(submitter_id) REFERENCES users (id)
);

-- 创建需求分配表
CREATE TABLE requirement_assignments (
    id INTEGER NOT NULL, 
    requirement_id INTEGER NOT NULL, 
    user_id INTEGER NOT NULL, 
    assign_time DATETIME NOT NULL, 
    PRIMARY KEY (id), 
    FOREIGN KEY(requirement_id) REFERENCES requirements (id), 
    FOREIGN KEY(user_id) REFERENCES users (id)
);

-- 创建需求历史记录表
CREATE TABLE requirement_histories (
    id INTEGER NOT NULL, 
    requirement_id INTEGER NOT NULL, 
    current_status_code VARCHAR(20) NOT NULL, 
    user_id INTEGER NOT NULL, 
    action_time DATETIME NOT NULL, 
    action_code VARCHAR(20) NOT NULL, 
    remark TEXT, 
    PRIMARY KEY (id), 
    FOREIGN KEY(requirement_id) REFERENCES requirements (id), 
    FOREIGN KEY(user_id) REFERENCES users (id)
);

-- 创建需求分支表
CREATE TABLE requirement_branches (
    id INTEGER NOT NULL, 
    requirement_id INTEGER NOT NULL, 
    branch_name VARCHAR(100) NOT NULL, 
    is_main BOOLEAN NOT NULL, 
    PRIMARY KEY (id), 
    FOREIGN KEY(requirement_id) REFERENCES requirements (id)
);

-- 创建需求拉取请求表
CREATE TABLE requirement_pull_requests (
    id INTEGER NOT NULL, 
    requirement_id INTEGER NOT NULL, 
    project_id INTEGER NOT NULL, 
    source_branch VARCHAR(200) NOT NULL, 
    target_branch VARCHAR(100) NOT NULL, 
    pr_iid INTEGER NOT NULL, 
    pr_url VARCHAR(500) NOT NULL, 
    status VARCHAR(6) NOT NULL, 
    create_time DATETIME NOT NULL, 
    update_time DATETIME, 
    PRIMARY KEY (id), 
    FOREIGN KEY(requirement_id) REFERENCES requirements (id), 
    FOREIGN KEY(project_id) REFERENCES projects (id)
);

-- 创建测试功能模块表
CREATE TABLE test_function_modules (
    id INTEGER NOT NULL, 
    project_id INTEGER NOT NULL, 
    name VARCHAR(100) NOT NULL, 
    created_at DATETIME NOT NULL, 
    updated_at DATETIME, 
    PRIMARY KEY (id), 
    FOREIGN KEY(project_id) REFERENCES projects (id)
);

-- 创建测试用例表
CREATE TABLE test_cases (
    id INTEGER NOT NULL, 
    project_id INTEGER NOT NULL, 
    module_id INTEGER NOT NULL, 
    test_number VARCHAR(50) NOT NULL, 
    test_name VARCHAR(100) NOT NULL, 
    test_type VARCHAR(10) NOT NULL, 
    precondition TEXT, 
    test_steps TEXT NOT NULL, 
    expected_result TEXT NOT NULL, 
    related_code TEXT NOT NULL, 
    test_status VARCHAR(4) NOT NULL, 
    creator_id INTEGER NOT NULL, 
    created_at DATETIME NOT NULL, 
    updater_id INTEGER, 
    updated_at DATETIME,
    test_side VARCHAR(8) NOT NULL, 
    PRIMARY KEY (id), 
    FOREIGN KEY(project_id) REFERENCES projects (id), 
    FOREIGN KEY(module_id) REFERENCES test_function_modules (id), 
    FOREIGN KEY(creator_id) REFERENCES users (id), 
    FOREIGN KEY(updater_id) REFERENCES users (id)
); 