# 使用官方的基础镜像，这里选择ubuntu作为例子
FROM ubuntu:22.04

# 设置环境变量，避免交互式配置
ENV DEBIAN_FRONTEND=noninteractive
ENV PATH="/usr/local/bin:${PATH}"
ENV PYTHONPATH="/app:/app/api:${PYTHONPATH}"

# 替换为国内的软件源（以阿里云为例）
RUN sed -i '<EMAIL>@mirrors.aliyun.com@g' /etc/apt/sources.list \
    && sed -i '<EMAIL>@mirrors.aliyun.com@g' /etc/apt/sources.list

# 更新软件包列表并安装必要的依赖
RUN apt-get update \
    && apt-get install -y wget software-properties-common curl nginx python3-pip git \
    build-essential libssl-dev libffi-dev python3-dev

# 安装Python 3.11
RUN add-apt-repository ppa:deadsnakes/ppa \
    && apt-get update \
    && apt-get install -y python3.11 python3.11-distutils python3.11-dev \
    && wget https://bootstrap.pypa.io/get-pip.py  \
    && python3.11 get-pip.py -i https://mirrors.aliyun.com/pypi/simple/ --trusted-host mirrors.aliyun.com \
    && rm get-pip.py

# 创建软链接确保 python 命令可用
RUN ln -sf /usr/bin/python3.11 /usr/bin/python

# 配置pip使用国内镜像源
RUN mkdir -p /root/.pip && \
    echo "[global]" > /root/.pip/pip.conf && \
    echo "index-url = https://mirrors.aliyun.com/pypi/simple/" >> /root/.pip/pip.conf && \
    echo "trusted-host = mirrors.aliyun.com" >> /root/.pip/pip.conf

# 安装Node.js 20+（修复冲突问题，只安装 nodejs，不单独安装 npm）
RUN curl -fsSL https://deb.nodesource.com/setup_20.x | bash - \
    && apt-get install -y nodejs

# 确保 npm 在 PATH 中
RUN which npm || echo "npm not found" && \
    echo "Node version: $(node -v)" && \
    echo "NPM version: $(npm -v || echo 'not found')"

# 配置npm使用国内镜像源
RUN npm config set registry https://registry.npmmirror.com

# 创建app目录
WORKDIR /app

# 复制项目文件到容器中
# 首先复制后端项目
COPY ./api /app/api

# 安装 Python 后端依赖
WORKDIR /app/api
# 确保安装所有依赖，并显示安装过程
RUN pip install --upgrade pip && \
    pip install --no-cache-dir -i https://mirrors.aliyun.com/pypi/simple/ --trusted-host mirrors.aliyun.com wheel setuptools && \
    pip install --no-cache-dir -i https://mirrors.aliyun.com/pypi/simple/ --trusted-host mirrors.aliyun.com -r requirements.txt

# 复制前端项目
COPY ./ui /app/ui
COPY ./nginx.conf /etc/nginx/sites-available/default

# 安装前端依赖并构建
WORKDIR /app/ui
# 使用 npm ci 代替 npm install，确保使用 package-lock.json 中的确切版本
RUN npm ci || npm install --registry=https://registry.npmmirror.com && \
    npm run build

# 清理默认的nginx站点内容
RUN rm -rf /var/www/html/* \
    && cp -r /app/ui/dist/* /var/www/html/

# 备份默认的nginx配置
RUN cp /etc/nginx/sites-available/default /etc/nginx/sites-available/default.bak

# 暴露80端口和8000端口
EXPOSE 80 8000

# 复制启动脚本
COPY ./start_project.sh /app/
RUN chmod +x /app/start_project.sh

# 创建数据目录
RUN mkdir -p /app/api/data

# 创建Docker专用启动脚本
RUN echo '#!/bin/bash\n\
echo "启动 Nginx..."\n\
nginx\n\
echo "启动 Python 后端..."\n\
cd /app/api\n\
\n\
# 确保数据目录存在\n\
mkdir -p /app/api/data\n\
\n\
# 首次运行时初始化数据库\n\
DB_FILE="./site.db"\n\
if [ ! -f "$DB_FILE" ]; then\n\
    echo "数据库文件不存在，执行初始化..."\n\
    # 尝试初始化数据库\n\
    if [ -f "./init_database.py" ]; then\n\
        python init_database.py || echo "数据库初始化失败，尝试更新数据库"\n\
    else\n\
        echo "init_database.py 不存在，跳过初始化"\n\
    fi\n\
    \n\
    # 尝试更新数据库\n\
    if [ -f "./update_database.py" ]; then\n\
        python update_database.py || echo "数据库更新失败，但继续启动服务"\n\
    else\n\
        echo "update_database.py 不存在，跳过更新"\n\
    fi\n\
else\n\
    echo "数据库文件已存在，执行更新..."\n\
    if [ -f "./update_database.py" ]; then\n\
        python update_database.py || echo "数据库更新失败，但继续启动服务"\n\
    else\n\
        echo "update_database.py 不存在，跳过更新"\n\
    fi\n\
fi\n\
\n\
# 启动主应用\n\
python main.py\n\
' > /app/docker_start.sh && chmod +x /app/docker_start.sh

# 启动命令：使用Docker专用启动脚本
CMD ["/app/docker_start.sh"]