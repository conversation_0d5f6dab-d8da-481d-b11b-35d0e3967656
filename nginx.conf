server {
    listen 80;
    server_name localhost;

    # 静态资源目录
    root /var/www/html;
    index index.html;

    # 静态文件缓存设置
    location ~* \.(jpg|jpeg|png|gif|ico|css|js)$ {
        expires 1d;
    }

    # 前端路由支持
    location / {
        try_files $uri $uri/ /index.html;
    }

    # API 请求代理到 Python 后端
    location /api/ {
        proxy_pass http://127.0.0.1:8000/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 日志配置
    access_log /var/log/nginx/access.log;
    error_log /var/log/nginx/error.log;
}