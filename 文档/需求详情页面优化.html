<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <title>项目管理系统 - 需求详情</title>
  <style>
    * { margin: 0; padding: 0; box-sizing: border-box; }
    body { 
      font-family: "Microsoft YaHei", -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Arial, sans-serif; 
      background-color: #f5f7fa; 
      color: #222;
      min-height: 100vh;
    }
    
    .layout {
      display: flex;
      min-height: 100vh;
    }
    
    /* 侧边栏样式 */
    .sidebar {
      width: 220px;
      background-color: #fff;
      border-right: 1px solid #e5e6eb;
      box-shadow: 2px 0 8px rgba(0,0,0,0.03);
      position: fixed;
      height: 100vh;
      z-index: 100;
    }
    
    .sidebar-header {
      height: 60px;
      border-bottom: 1px solid #e5e6eb;
      display: flex;
      align-items: center;
      padding: 0 20px;
    }
    
    .logo {
      font-size: 18px;
      font-weight: bold;
      color: #1877f2;
    }
    
    .menu {
      padding: 20px 0;
    }
    
    .menu-title {
      font-size: 13px;
      color: #999;
      padding: 0 20px;
      margin-bottom: 8px;
    }
    
    .menu-item {
      height: 46px;
      display: flex;
      align-items: center;
      padding: 0 20px;
      color: #666;
      font-size: 15px;
      margin-bottom: 2px;
      cursor: pointer;
      position: relative;
      transition: all 0.3s;
    }
    
    .menu-item:hover {
      color: #1877f2;
      background-color: rgba(24, 119, 242, 0.04);
    }
    
    .menu-item.active {
      color: #1877f2;
      background-color: rgba(24, 119, 242, 0.08);
      font-weight: 500;
    }
    
    .menu-item.active::before {
      content: '';
      position: absolute;
      left: 0;
      top: 12px;
      bottom: 12px;
      width: 3px;
      background-color: #1877f2;
      border-radius: 0 2px 2px 0;
    }
    
    .menu-icon {
      width: 20px;
      height: 20px;
      margin-right: 8px;
      background-color: currentColor;
      opacity: 0.6;
      mask-size: cover;
      -webkit-mask-size: cover;
    }
    
    .icon-home {
      mask-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"/><polyline points="9 22 9 12 15 12 15 22"/></svg>');
    }
    
    .icon-clipboard {
      mask-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"></path><rect x="8" y="2" width="8" height="4" rx="1" ry="1"></rect></svg>');
    }
    
    .icon-folder {
      mask-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5l2 3h9a2 2 0 0 1 2 2z"></path></svg>');
    }
    
    /* 主内容区域 */
    .main {
      flex: 1;
      margin-left: 220px;
    }
    
    .header {
      height: 60px;
      background-color: #fff;
      border-bottom: 1px solid #e5e6eb;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 24px;
      position: sticky;
      top: 0;
      z-index: 99;
    }
    
    .page-title {
      font-size: 18px;
      font-weight: 500;
      color: #222;
    }
    
    .user-info {
      display: flex;
      align-items: center;
    }
    
    .avatar {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      background-color: #1877f2;
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
      font-weight: 500;
    }
    
    .username {
      margin-left: 8px;
      font-size: 14px;
      color: #666;
    }
    
    .content {
      padding: 24px;
    }
    
    /* 卡片样式 */
    .card {
      background-color: #fff;
      border-radius: 10px;
      box-shadow: 0 2px 12px rgba(0,0,0,0.04);
      padding: 24px;
      margin-bottom: 24px;
    }
    
    .card-title {
      font-size: 18px;
      font-weight: 500;
      color: #222;
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 1px solid #e5e6eb;
    }
    
    /* 按钮样式 */
    .btn {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      height: 40px;
      padding: 0 24px;
      border-radius: 6px;
      font-size: 15px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s;
      border: none;
    }
    
    .btn-primary {
      background-color: #1877f2;
      color: white;
    }
    
    .btn-primary:hover {
      background-color: #0d6efd;
    }
    
    .btn-text {
      background-color: transparent;
      color: #1877f2;
    }
    
    .btn-text:hover {
      background-color: rgba(24, 119, 242, 0.04);
    }
    
    .btn-small {
      height: 32px;
      padding: 0 12px;
      font-size: 14px;
    }
    
    /* 标签样式 */
    .badge {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      height: 24px;
      padding: 0 10px;
      border-radius: 12px;
      font-size: 13px;
      font-weight: 500;
    }
    
    .badge-blue {
      background-color: rgba(24, 119, 242, 0.1);
      color: #1877f2;
    }
    
    .badge-green {
      background-color: rgba(82, 196, 26, 0.1);
      color: #52c41a;
    }
    
    .badge-orange {
      background-color: rgba(245, 166, 35, 0.1);
      color: #f5a623;
    }
    
    .badge-red {
      background-color: rgba(255, 77, 79, 0.1);
      color: #ff4d4f;
    }
    
    /* 需求详情样式 */
    .requirement-header {
      background: linear-gradient(135deg, #1877f2, #36cfc9);
      color: white;
      margin: -24px -24px 24px -24px;
      padding: 24px;
      border-radius: 10px 10px 0 0;
    }
    
    .requirement-title {
      font-size: 24px;
      font-weight: bold;
      margin-bottom: 8px;
    }
    
    .requirement-id {
      font-size: 14px;
      opacity: 0.9;
    }
    
    .info-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 24px;
      margin-bottom: 24px;
    }
    
    .info-item {
      display: flex;
      flex-direction: column;
    }
    
    .info-label {
      font-size: 14px;
      color: #666;
      margin-bottom: 4px;
    }
    
    .info-value {
      font-size: 15px;
      color: #222;
      font-weight: 500;
    }
    
    .status-high {
      color: #ff4d4f;
      font-weight: bold;
    }
    
    .people-list {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
    }
    
    .person-tag {
      padding: 4px 8px;
      background-color: #f5f7fa;
      border-radius: 4px;
      font-size: 13px;
      color: #666;
    }
    
    .content-section {
      margin-bottom: 24px;
    }
    
    .section-title {
      font-size: 16px;
      font-weight: 500;
      color: #222;
      margin-bottom: 12px;
    }
    
    .content-text {
      font-size: 14px;
      color: #666;
      line-height: 1.6;
    }
    
    .issue-list {
      list-style: none;
      padding: 0;
    }
    
    .issue-item {
      margin-bottom: 12px;
      padding: 12px;
      background-color: #f5f7fa;
      border-radius: 6px;
      border-left: 3px solid #1877f2;
    }
    
    .issue-title {
      font-weight: 500;
      color: #222;
      margin-bottom: 4px;
    }
    
    .issue-desc {
      font-size: 13px;
      color: #666;
    }
    
    /* 历史记录样式 */
    .timeline {
      position: relative;
      padding-left: 24px;
    }
    
    .timeline::before {
      content: '';
      position: absolute;
      left: 8px;
      top: 0;
      bottom: 0;
      width: 2px;
      background-color: #e5e6eb;
    }
    
    .timeline-item {
      position: relative;
      margin-bottom: 16px;
      padding-bottom: 16px;
    }
    
    .timeline-item:last-child {
      margin-bottom: 0;
      padding-bottom: 0;
    }
    
    .timeline-item::before {
      content: '';
      position: absolute;
      left: -20px;
      top: 4px;
      width: 12px;
      height: 12px;
      border-radius: 50%;
      background-color: #52c41a;
      border: 2px solid #fff;
      box-shadow: 0 0 0 2px #52c41a;
    }
    
    .timeline-header {
      display: flex;
      align-items: center;
      margin-bottom: 4px;
    }
    
    .timeline-user {
      font-weight: 500;
      color: #222;
      margin-right: 8px;
    }
    
    .timeline-action {
      font-size: 14px;
      color: #666;
      margin-right: 8px;
    }
    
    .timeline-time {
      font-size: 13px;
      color: #999;
    }
    
    .card-actions {
      display: flex;
      justify-content: flex-end;
      gap: 12px;
      margin-top: 24px;
      padding-top: 16px;
      border-top: 1px solid #e5e6eb;
    }
  </style>
</head>
<body>
  <div class="layout">
    <!-- 侧边栏 -->
    <div class="sidebar">
      <div class="sidebar-header">
        <div class="logo">项目管理系统</div>
      </div>
      <div class="menu">
        <div class="menu-title">通用</div>
        <div class="menu-item">
          <div class="menu-icon icon-home"></div>
          首页
        </div>
        <div class="menu-item">
          <div class="menu-icon icon-clipboard"></div>
          待办事项
        </div>
        
        <div class="menu-title">工作</div>
        <div class="menu-item active">
          <div class="menu-icon icon-folder"></div>
          项目管理
        </div>
      </div>
    </div>

    <!-- 主内容区域 -->
    <div class="main">
      <!-- 顶部导航 -->
      <div class="header">
        <div class="page-title">需求详情</div>
        <div class="user-info">
          <div class="avatar">管</div>
          <div class="username">管理员</div>
        </div>
      </div>

      <!-- 内容区域 -->
      <div class="content">
        <!-- 需求基本信息卡片 -->
        <div class="card">
          <div class="requirement-header">
            <div class="requirement-title">指标预警功能存在问题</div>
            <div class="requirement-id">需求编号：B0219 | 项目：vs-starfish</div>
          </div>
          
          <div class="info-grid">
            <div class="info-item">
              <div class="info-label">类型</div>
              <div class="info-value">
                <span class="badge badge-orange">修复漏洞</span>
              </div>
            </div>
            <div class="info-item">
              <div class="info-label">优先级</div>
              <div class="info-value status-high">P0</div>
            </div>
            <div class="info-item">
              <div class="info-label">状态</div>
              <div class="info-value">
                <span class="badge badge-blue">验证中</span>
              </div>
            </div>
            <div class="info-item">
              <div class="info-label">计划开始时间</div>
              <div class="info-value">2025-05-22</div>
            </div>
            <div class="info-item">
              <div class="info-label">计划完成时间</div>
              <div class="info-value">2025-06-21</div>
            </div>
            <div class="info-item">
              <div class="info-label">提交时间</div>
              <div class="info-value">2025-05-22 01:12:21</div>
            </div>
            <div class="info-item">
              <div class="info-label">更新时间</div>
              <div class="info-value">2025-05-22 08:10:00</div>
            </div>
          </div>

          <div class="info-grid">
            <div class="info-item">
              <div class="info-label">提交人</div>
              <div class="info-value">陶文亮</div>
            </div>
            <div class="info-item">
              <div class="info-label">开发人员</div>
              <div class="people-list">
                <span class="person-tag">黄绍铭</span>
                <span class="person-tag">顾智聪</span>
                <span class="person-tag">李佳辉</span>
                <span class="person-tag">焦梓航</span>
              </div>
            </div>
          </div>

          <div class="info-grid">
            <div class="info-item">
              <div class="info-label">主目标分支</div>
              <div class="info-value">
                <span class="badge badge-green">dev/drc-bank</span>
              </div>
            </div>
            <div class="info-item">
              <div class="info-label">Git分支</div>
              <div class="info-value">
                <span class="badge badge-blue">bugfix/B0219-fix-metric-alert-issue</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 需求内容卡片 -->
        <div class="card">
          <div class="card-title">需求内容</div>
          
          <div class="content-section">
            <div class="section-title">问题描述</div>
            <div class="content-text">通知方式和预警频率都是必填项。</div>
          </div>

          <div class="content-section">
            <div class="section-title">具体问题</div>
            <ul class="issue-list">
              <li class="issue-item">
                <div class="issue-title">新建预警/编辑预警的表单宽度</div>
                <div class="issue-desc">通知方式第一个默认选项是"无"</div>
              </li>
              <li class="issue-item">
                <div class="issue-title">编辑预警没法修改预警规则</div>
                <div class="issue-desc">预警频率第一个默认选项是"每日"</div>
              </li>
              <li class="issue-item">
                <div class="issue-title">查看预警缺少字段</div>
                <div class="issue-desc">查看预警缺少字段</div>
              </li>
            </ul>
          </div>
        </div>

        <!-- 历史记录卡片 -->
        <div class="card">
          <div class="card-title">历史记录</div>
          
          <div class="timeline">
            <div class="timeline-item">
              <div class="timeline-header">
                <div class="timeline-user">林泽芳</div>
                <div class="timeline-action">通过测试</div>
                <span class="badge badge-green">验证中</span>
                <div class="timeline-time">2025-05-22 08:10:00</div>
              </div>
            </div>
            
            <div class="timeline-item">
              <div class="timeline-header">
                <div class="timeline-user">李佳辉</div>
                <div class="timeline-action">提交测试</div>
                <span class="badge badge-orange">测试中</span>
                <div class="timeline-time">2025-05-22 07:19:16</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 操作按钮区域 -->
        <div class="card">
          <div class="card-actions">
            <button class="btn btn-text">返回列表</button>
            <button class="btn btn-text">编辑需求</button>
            <button class="btn btn-primary">更新状态</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</body>
</html> 