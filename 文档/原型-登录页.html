<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <title>项目管理系统 - 登录</title>
  <style>
    * { margin: 0; padding: 0; box-sizing: border-box; }
    body { 
      font-family: "Microsoft YaHei", -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Arial, sans-serif; 
      background-color: #f5f7fa; 
      color: #222;
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100vh;
    }
    .login-container {
      display: flex;
      width: 900px;
      height: 500px;
      box-shadow: 0 8px 24px rgba(0,0,0,0.08);
      border-radius: 12px;
      overflow: hidden;
    }
    .login-banner {
      flex: 1;
      background: linear-gradient(135deg, #1877f2, #36cfc9);
      padding: 40px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      color: white;
    }
    .banner-logo {
      font-size: 24px;
      font-weight: bold;
    }
    .banner-content {
      margin-top: -80px;
    }
    .banner-title {
      font-size: 32px;
      margin-bottom: 16px;
    }
    .banner-desc {
      font-size: 16px;
      opacity: 0.9;
      line-height: 1.6;
    }
    .banner-footer {
      font-size: 14px;
      opacity: 0.7;
    }
    .login-form-container {
      flex: 1;
      background: #fff;
      padding: 40px;
      display: flex;
      flex-direction: column;
      justify-content: center;
    }
    .login-title {
      font-size: 24px;
      font-weight: bold;
      margin-bottom: 40px;
      color: #222;
    }
    .login-form {
      display: flex;
      flex-direction: column;
    }
    .form-item {
      margin-bottom: 24px;
    }
    .form-label {
      display: block;
      margin-bottom: 8px;
      font-size: 14px;
      color: #666;
    }
    .form-input {
      width: 100%;
      height: 40px;
      border: 1px solid #e5e6eb;
      border-radius: 6px;
      padding: 0 16px;
      font-size: 15px;
      color: #222;
      transition: all 0.3s;
    }
    .form-input:focus {
      outline: none;
      border-color: #1877f2;
      box-shadow: 0 0 0 2px rgba(24, 119, 242, 0.1);
    }
    .login-btn {
      height: 44px;
      background: #1877f2;
      color: white;
      border: none;
      border-radius: 6px;
      font-size: 16px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s;
      margin-top: 8px;
    }
    .login-btn:hover {
      background: #0d6efd;
    }
    .login-footer {
      display: flex;
      justify-content: space-between;
      margin-top: 16px;
      font-size: 14px;
    }
    .login-footer a {
      color: #1877f2;
      text-decoration: none;
    }
    .login-footer a:hover {
      text-decoration: underline;
    }
  </style>
</head>
<body>
  <div class="login-container">
    <div class="login-banner">
      <div class="banner-logo">项目管理系统</div>
      <div class="banner-content">
        <h1 class="banner-title">高效管理项目流程</h1>
        <p class="banner-desc">集成了项目、需求、测试用例、PR、用户与权限等核心管理功能，支持与GitLab代码仓库联动，适用于需求全流程追踪、协作开发与质量保障。</p>
      </div>
      <div class="banner-footer">© 2023 项目管理系统</div>
    </div>
    <div class="login-form-container">
      <h2 class="login-title">账号登录</h2>
      <form class="login-form">
        <div class="form-item">
          <label class="form-label">用户名</label>
          <input type="text" class="form-input" placeholder="请输入用户名" />
        </div>
        <div class="form-item">
          <label class="form-label">密码</label>
          <input type="password" class="form-input" placeholder="请输入密码" />
        </div>
        <button class="login-btn">登录</button>
        <div class="login-footer">
          <a href="#">忘记密码</a>
          <span style="color: #999">请联系管理员申请账号</span>
        </div>
      </form>
    </div>
  </div>
</body>
</html> 