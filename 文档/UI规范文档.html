<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <title>项目管理系统 - UI设计规范</title>
  <style>
    * { margin: 0; padding: 0; box-sizing: border-box; }
    body { 
      font-family: "Microsoft YaHei", -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Arial, sans-serif; 
      background-color: #f5f7fa; 
      color: #222;
      line-height: 1.5;
      padding: 40px;
    }
    .container {
      max-width: 1200px;
      margin: 0 auto;
      background-color: #fff;
      border-radius: 12px;
      box-shadow: 0 4px 16px rgba(0,0,0,0.05);
      padding: 40px;
    }
    h1 {
      font-size: 28px;
      font-weight: bold;
      color: #1877f2;
      margin-bottom: 24px;
      text-align: center;
    }
    h2 {
      font-size: 20px;
      font-weight: 500;
      color: #222;
      margin: 40px 0 16px;
      padding-bottom: 8px;
      border-bottom: 1px solid #e5e6eb;
    }
    h3 {
      font-size: 16px;
      font-weight: 500;
      color: #222;
      margin: 24px 0 12px;
    }
    p {
      margin-bottom: 16px;
      color: #666;
    }
    .color-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
      gap: 20px;
      margin: 16px 0;
    }
    .color-item {
      border-radius: 6px;
      overflow: hidden;
      box-shadow: 0 2px 8px rgba(0,0,0,0.05);
    }
    .color-preview {
      height: 100px;
    }
    .color-info {
      padding: 12px;
      background-color: #fff;
      font-size: 14px;
    }
    .color-name {
      font-weight: 500;
      margin-bottom: 4px;
    }
    .color-value {
      color: #666;
    }
    .example-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      gap: 24px;
      margin: 20px 0;
    }
    .example-item {
      border: 1px solid #e5e6eb;
      border-radius: 6px;
      overflow: hidden;
    }
    .example-preview {
      padding: 20px;
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: #fafafa;
      min-height: 120px;
    }
    .example-code {
      padding: 12px;
      background-color: #f5f7fa;
      font-family: Monaco, Consolas, 'Courier New', monospace;
      font-size: 13px;
      white-space: pre-wrap;
      color: #444;
      border-top: 1px solid #e5e6eb;
    }
    .spacing-block {
      background-color: rgba(24, 119, 242, 0.1);
      border: 1px dashed #1877f2;
      color: #1877f2;
      text-align: center;
      padding: 8px;
      font-size: 14px;
      border-radius: 4px;
    }
    .typography-example {
      margin: 24px 0;
    }
    code {
      font-family: Monaco, Consolas, 'Courier New', monospace;
      background-color: #f5f7fa;
      padding: 2px 6px;
      border-radius: 4px;
      font-size: 14px;
      color: #444;
    }
    table {
      width: 100%;
      border-collapse: collapse;
      margin: 24px 0;
    }
    table th, table td {
      border: 1px solid #e5e6eb;
      padding: 12px 16px;
      text-align: left;
    }
    table th {
      background-color: #fafafa;
      font-weight: 500;
      color: #666;
    }
    .btn {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      height: 40px;
      padding: 0 24px;
      border-radius: 6px;
      font-size: 15px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s;
      border: none;
    }
    .btn-primary {
      background-color: #1877f2;
      color: white;
    }
    .btn-text {
      background-color: transparent;
      color: #1877f2;
    }
    .btn-small {
      height: 32px;
      padding: 0 12px;
      font-size: 14px;
    }
    .badge {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      height: 24px;
      padding: 0 10px;
      border-radius: 12px;
      font-size: 13px;
      font-weight: 500;
    }
    .badge-blue {
      background-color: rgba(24, 119, 242, 0.1);
      color: #1877f2;
    }
    .form-control {
      width: 100%;
      height: 40px;
      border: 1px solid #e5e6eb;
      border-radius: 6px;
      padding: 0 16px;
      font-size: 14px;
      color: #222;
      transition: all 0.3s;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>项目管理系统 UI设计规范</h1>
    
    <h2>1. 颜色系统</h2>
    <p>项目使用统一的色彩体系，保持视觉一致性。</p>
    
    <h3>1.1 主要颜色</h3>
    <div class="color-grid">
      <div class="color-item">
        <div class="color-preview" style="background-color: #1877f2;"></div>
        <div class="color-info">
          <div class="color-name">品牌蓝 / 主色</div>
          <div class="color-value">#1877f2</div>
        </div>
      </div>
      <div class="color-item">
        <div class="color-preview" style="background-color: #0d6efd;"></div>
        <div class="color-info">
          <div class="color-name">深蓝色 / 悬停状态</div>
          <div class="color-value">#0d6efd</div>
        </div>
      </div>
      <div class="color-item">
        <div class="color-preview" style="background: linear-gradient(135deg, #1877f2, #36cfc9);"></div>
        <div class="color-info">
          <div class="color-name">渐变蓝 / 登录页背景</div>
          <div class="color-value">linear-gradient(135deg, #1877f2, #36cfc9)</div>
        </div>
      </div>
    </div>
    
    <h3>1.2 文本颜色</h3>
    <div class="color-grid">
      <div class="color-item">
        <div class="color-preview" style="background-color: #222;"></div>
        <div class="color-info">
          <div class="color-name">主要文本</div>
          <div class="color-value">#222</div>
        </div>
      </div>
      <div class="color-item">
        <div class="color-preview" style="background-color: #666;"></div>
        <div class="color-info">
          <div class="color-name">次要文本</div>
          <div class="color-value">#666</div>
        </div>
      </div>
      <div class="color-item">
        <div class="color-preview" style="background-color: #999;"></div>
        <div class="color-info">
          <div class="color-name">提示文本</div>
          <div class="color-value">#999</div>
        </div>
      </div>
    </div>
    
    <h3>1.3 辅助颜色</h3>
    <div class="color-grid">
      <div class="color-item">
        <div class="color-preview" style="background-color: #52c41a;"></div>
        <div class="color-info">
          <div class="color-name">成功绿</div>
          <div class="color-value">#52c41a</div>
        </div>
      </div>
      <div class="color-item">
        <div class="color-preview" style="background-color: #f5a623;"></div>
        <div class="color-info">
          <div class="color-name">警告黄</div>
          <div class="color-value">#f5a623</div>
        </div>
      </div>
      <div class="color-item">
        <div class="color-preview" style="background-color: #ff4d4f;"></div>
        <div class="color-info">
          <div class="color-name">错误红</div>
          <div class="color-value">#ff4d4f</div>
        </div>
      </div>
    </div>
    
    <h3>1.4 背景色与边框</h3>
    <div class="color-grid">
      <div class="color-item">
        <div class="color-preview" style="background-color: #f5f7fa;"></div>
        <div class="color-info">
          <div class="color-name">页面背景色</div>
          <div class="color-value">#f5f7fa</div>
        </div>
      </div>
      <div class="color-item">
        <div class="color-preview" style="background-color: #fafafa;"></div>
        <div class="color-info">
          <div class="color-name">表头背景色</div>
          <div class="color-value">#fafafa</div>
        </div>
      </div>
      <div class="color-item">
        <div class="color-preview" style="background-color: #e5e6eb;"></div>
        <div class="color-info">
          <div class="color-name">边框颜色</div>
          <div class="color-value">#e5e6eb</div>
        </div>
      </div>
    </div>
    
    <h2>2. 版式与字体</h2>
    <p>系统使用无衬线字体，采用清晰易读的层次结构。</p>
    
    <h3>2.1 字体家族</h3>
    <p>
      <code>font-family: "Microsoft YaHei", -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Arial, sans-serif;</code>
    </p>
    
    <h3>2.2 字体大小</h3>
    <table>
      <tr>
        <th>用途</th>
        <th>大小</th>
        <th>粗细</th>
        <th>示例</th>
      </tr>
      <tr>
        <td>页面大标题</td>
        <td>28px</td>
        <td>bold</td>
        <td><span style="font-size: 28px; font-weight: bold;">欢迎使用项目管理系统</span></td>
      </tr>
      <tr>
        <td>模块标题</td>
        <td>24px</td>
        <td>bold</td>
        <td><span style="font-size: 24px; font-weight: bold;">账号登录</span></td>
      </tr>
      <tr>
        <td>二级标题</td>
        <td>18px</td>
        <td>500</td>
        <td><span style="font-size: 18px; font-weight: 500;">页面标题</span></td>
      </tr>
      <tr>
        <td>卡片标题</td>
        <td>18px</td>
        <td>500</td>
        <td><span style="font-size: 18px; font-weight: 500;">项目管理</span></td>
      </tr>
      <tr>
        <td>正文内容</td>
        <td>15px</td>
        <td>normal</td>
        <td><span style="font-size: 15px;">菜单项文本</span></td>
      </tr>
      <tr>
        <td>次要文本</td>
        <td>14px</td>
        <td>normal</td>
        <td><span style="font-size: 14px;">表单标签、用户名</span></td>
      </tr>
      <tr>
        <td>辅助文本</td>
        <td>13px</td>
        <td>normal</td>
        <td><span style="font-size: 13px;">菜单分类、表单提示</span></td>
      </tr>
    </table>
    
    <h2>3. 间距系统</h2>
    <p>使用一致的间距标准，提供统一的布局节奏。</p>
    
    <h3>3.1 基础间距</h3>
    <div class="example-grid">
      <div class="example-item">
        <div class="example-preview">
          <div style="display: flex; align-items: center;">
            <div class="spacing-block">内容</div>
            <div style="width: 8px;"></div>
            <div class="spacing-block">内容</div>
          </div>
        </div>
        <div class="example-code">小间距：8px</div>
      </div>
      <div class="example-item">
        <div class="example-preview">
          <div style="display: flex; align-items: center;">
            <div class="spacing-block">内容</div>
            <div style="width: 16px;"></div>
            <div class="spacing-block">内容</div>
          </div>
        </div>
        <div class="example-code">中间距：16px</div>
      </div>
      <div class="example-item">
        <div class="example-preview">
          <div style="display: flex; align-items: center;">
            <div class="spacing-block">内容</div>
            <div style="width: 24px;"></div>
            <div class="spacing-block">内容</div>
          </div>
        </div>
        <div class="example-code">大间距：24px</div>
      </div>
    </div>
    
    <h3>3.2 内边距规范</h3>
    <div class="example-grid">
      <div class="example-item">
        <div class="example-preview">
          <div style="padding: 20px; border: 1px dashed #1877f2; background-color: rgba(24, 119, 242, 0.1); width: 100%;">
            <div style="background-color: white; border-radius: 4px; padding: 8px; text-align: center;">内容</div>
          </div>
        </div>
        <div class="example-code">内容区域：20px</div>
      </div>
      <div class="example-item">
        <div class="example-preview">
          <div style="padding: 24px; border: 1px dashed #1877f2; background-color: rgba(24, 119, 242, 0.1); width: 100%;">
            <div style="background-color: white; border-radius: 4px; padding: 8px; text-align: center;">内容</div>
          </div>
        </div>
        <div class="example-code">卡片内边距：24px</div>
      </div>
      <div class="example-item">
        <div class="example-preview">
          <div style="padding: 40px; border: 1px dashed #1877f2; background-color: rgba(24, 119, 242, 0.1); width: 100%;">
            <div style="background-color: white; border-radius: 4px; padding: 8px; text-align: center;">内容</div>
          </div>
        </div>
        <div class="example-code">欢迎卡片：40px</div>
      </div>
    </div>
    
    <h2>4. 布局系统</h2>
    <p>系统使用固定的布局结构，确保各页面风格统一。</p>
    
    <h3>4.1 全局布局</h3>
    <ul>
      <li>侧边栏宽度：220px，固定在左侧</li>
      <li>内容区域：自适应宽度，左边距为220px</li>
      <li>顶部导航高度：60px</li>
      <li>内容区域内边距：24px</li>
    </ul>
    
    <h3>4.2 网格系统</h3>
    <p>使用弹性布局（Flexbox）和网格布局（Grid）：</p>
    <ul>
      <li>Card Grid 使用 <code>grid-template-columns: repeat(3, 1fr);</code>，间隔为 24px</li>
      <li>表单、列表使用弹性布局，纵向排列</li>
    </ul>
    
    <h2>5. 组件规范</h2>
    <p>系统中使用的标准组件，保持统一的视觉风格。</p>
    
    <h3>5.1 按钮</h3>
    <div class="example-grid">
      <div class="example-item">
        <div class="example-preview">
          <button class="btn btn-primary">主要按钮</button>
        </div>
        <div class="example-code">主要按钮
- 高度：40px
- 内边距：0 24px
- 背景色：#1877f2
- 文字：15px, 白色
- 圆角：6px</div>
      </div>
      <div class="example-item">
        <div class="example-preview">
          <button class="btn btn-text">文本按钮</button>
        </div>
        <div class="example-code">文本按钮
- 高度：40px
- 内边距：0 24px
- 背景色：透明
- 文字：15px, #1877f2
- 圆角：6px</div>
      </div>
      <div class="example-item">
        <div class="example-preview">
          <button class="btn btn-small btn-text">小型按钮</button>
        </div>
        <div class="example-code">小型按钮
- 高度：32px
- 内边距：0 12px
- 文字：14px
- 其余同上述按钮</div>
      </div>
    </div>
    
    <h3>5.2 输入框</h3>
    <div class="example-grid">
      <div class="example-item">
        <div class="example-preview">
          <input type="text" class="form-control" placeholder="请输入内容" />
        </div>
        <div class="example-code">输入框
- 高度：40px
- 内边距：0 16px
- 边框：1px solid #e5e6eb
- 圆角：6px
- 文字：14px
- 聚焦状态边框色：#1877f2</div>
      </div>
    </div>
    
    <h3>5.3 标签</h3>
    <div class="example-grid">
      <div class="example-item">
        <div class="example-preview">
          <span class="badge badge-blue">标签</span>
        </div>
        <div class="example-code">标签
- 高度：24px
- 内边距：0 10px
- 圆角：12px
- 文字：13px, 500粗
- 蓝色背景：rgba(24, 119, 242, 0.1)
- 蓝色文字：#1877f2</div>
      </div>
    </div>
    
    <h3>5.4 卡片</h3>
    <p>系统中的内容卡片统一使用以下样式：</p>
    <ul>
      <li>背景色：白色 (#fff)</li>
      <li>圆角：10px</li>
      <li>阴影：0 2px 12px rgba(0,0,0,0.04)</li>
      <li>内边距：24px（标准）或 40px（欢迎卡片）</li>
    </ul>
    
    <h3>5.5 表格</h3>
    <p>表格样式规范：</p>
    <ul>
      <li>边框：1px solid #e5e6eb</li>
      <li>表头背景色：#fafafa</li>
      <li>单元格内边距：16px 24px</li>
      <li>表头文字：#666, 500粗</li>
    </ul>
    
    <h3>5.6 对话框</h3>
    <p>模态对话框样式：</p>
    <ul>
      <li>宽度：520px</li>
      <li>背景色：白色</li>
      <li>圆角：10px</li>
      <li>阴影：0 4px 24px rgba(0,0,0,0.1)</li>
      <li>标题区高度：固定，带下边框</li>
      <li>底部操作区：带上边框，右对齐按钮</li>
    </ul>
    
    <h3>5.7 菜单与导航</h3>
    <p>侧边栏菜单规范：</p>
    <ul>
      <li>菜单项高度：46px</li>
      <li>菜单项内边距：0 20px</li>
      <li>菜单分类：13px, #999</li>
      <li>图标尺寸：20px × 20px</li>
      <li>活动菜单项：带左侧3px蓝色条，背景色rgba(24, 119, 242, 0.08)</li>
    </ul>
    
    <h2>6. 图标系统</h2>
    <p>系统使用线性风格的SVG图标，保持统一的视觉语言。</p>
    <ul>
      <li>主要图标大小：24px × 24px（源尺寸）</li>
      <li>菜单图标显示大小：20px × 20px</li>
      <li>按钮内图标大小：16px × 16px</li>
      <li>线条粗细：2px</li>
      <li>图标颜色：继承文本颜色</li>
    </ul>
    
    <h2>7. 响应与交互</h2>
    <p>交互反馈和动效设计：</p>
    <ul>
      <li>过渡动画：<code>transition: all 0.3s;</code></li>
      <li>悬停效果：透明度、背景色或阴影的细微变化</li>
      <li>卡片悬停效果：轻微上移并增加阴影</li>
      <li>按钮悬停效果：背景色加深</li>
      <li>输入框聚焦效果：边框颜色变化并添加轻微阴影</li>
    </ul>
    
    <h2>8. 设计原则</h2>
    <ul>
      <li><strong>一致性</strong>：保持视觉元素、交互方式的一致</li>
      <li><strong>清晰层次</strong>：明确的视觉层次和信息优先级</li>
      <li><strong>简洁实用</strong>：减少视觉噪音，突出关键功能</li>
      <li><strong>用户友好</strong>：提供清晰的反馈和引导</li>
    </ul>

    <h2>9. 系统布局规范</h2>
    <p>详细规范系统的整体布局以及各功能区域的标准尺寸和边距。</p>

    <h3>9.1 页面框架结构</h3>
    <ul>
      <li><strong>整体布局</strong>：左侧固定宽度侧边栏 + 右侧自适应内容区</li>
      <li><strong>最小宽度</strong>：1200px（低于此宽度出现水平滚动条）</li>
      <li><strong>最大宽度</strong>：宽度自适应，内容区无最大宽度限制</li>
      <li><strong>高度</strong>：最小高度100vh，确保在内容少时也能填满视口</li>
    </ul>

    <h3>9.2 侧边栏区域规范</h3>
    <ul>
      <li><strong>宽度</strong>：220px（固定）</li>
      <li><strong>位置</strong>：固定定位，高度100vh</li>
      <li><strong>背景色</strong>：白色 (#fff)</li>
      <li><strong>阴影</strong>：2px 0 8px rgba(0,0,0,0.03)</li>
      <li><strong>边框</strong>：右侧1px solid #e5e6eb</li>
      <li><strong>z-index</strong>：100（确保在最上层）</li>
    </ul>

    <h4>9.2.1 侧边栏头部</h4>
    <ul>
      <li><strong>高度</strong>：60px</li>
      <li><strong>内边距</strong>：0 20px</li>
      <li><strong>边框</strong>：底部1px solid #e5e6eb</li>
      <li><strong>Logo文字</strong>：18px，粗体，品牌蓝 #1877f2</li>
    </ul>

    <h4>9.2.2 侧边栏菜单</h4>
    <ul>
      <li><strong>整体内边距</strong>：20px 0</li>
      <li><strong>菜单分类</strong>：上间距20px（第一个除外），下间距8px，字体13px，#999</li>
      <li><strong>菜单项高度</strong>：46px</li>
      <li><strong>菜单项内边距</strong>：0 20px</li>
      <li><strong>菜单项间距</strong>：2px</li>
      <li><strong>菜单项文字</strong>：15px，正常状态 #666</li>
      <li><strong>菜单图标</strong>：尺寸20px，右边距8px，透明度0.6</li>
      <li><strong>活动状态</strong>：左侧3px蓝色竖条，背景色rgba(24, 119, 242, 0.08)，文字加粗500，文字颜色 #1877f2</li>
      <li><strong>悬停状态</strong>：背景色rgba(24, 119, 242, 0.04)，文字颜色 #1877f2</li>
    </ul>

    <h3>9.3 主内容区域规范</h3>
    <ul>
      <li><strong>左边距</strong>：220px（与侧边栏宽度一致）</li>
      <li><strong>背景色</strong>：#f5f7fa</li>
      <li><strong>最小高度</strong>：100vh</li>
    </ul>

    <h4>9.3.1 顶部导航栏</h4>
    <ul>
      <li><strong>高度</strong>：60px</li>
      <li><strong>内边距</strong>：0 24px</li>
      <li><strong>背景色</strong>：白色 (#fff)</li>
      <li><strong>边框</strong>：底部1px solid #e5e6eb</li>
      <li><strong>定位</strong>：sticky，top: 0，确保滚动时固定在顶部</li>
      <li><strong>z-index</strong>：99</li>
      <li><strong>页面标题</strong>：18px，500粗，#222</li>
      <li><strong>布局</strong>：两端对齐，flex布局 justify-content: space-between</li>
    </ul>

    <h4>9.3.2 用户信息区</h4>
    <ul>
      <li><strong>位置</strong>：顶部导航栏右侧</li>
      <li><strong>头像尺寸</strong>：32px x 32px</li>
      <li><strong>头像样式</strong>：圆形，背景色品牌蓝 #1877f2，白色文字</li>
      <li><strong>用户名</strong>：14px，#666，左边距8px</li>
    </ul>

    <h4>9.3.3 内容区域</h4>
    <ul>
      <li><strong>内边距</strong>：24px</li>
      <li><strong>卡片间距</strong>：24px（上下间距）</li>
    </ul>

    <h3>9.4 页面类型规范</h3>
    
    <h4>9.4.1 首页布局</h4>
    <ul>
      <li><strong>欢迎卡片</strong>：背景白色，圆角10px，阴影 0 2px 12px rgba(0,0,0,0.04)，内边距40px</li>
      <li><strong>欢迎标题</strong>：28px，粗体，居中，下边距16px</li>
      <li><strong>欢迎描述</strong>：16px，#666，行高1.6，最大宽度600px，居中，下边距32px</li>
      <li><strong>功能卡片网格</strong>：三栏布局，间隔24px，上边距40px</li>
      <li><strong>功能卡片</strong>：背景白色，圆角10px，内边距24px，居中对齐</li>
      <li><strong>卡片图标区</strong>：48px x 48px，圆角12px，图标24px居中，下边距16px</li>
      <li><strong>卡片标题</strong>：18px，500粗，#222，下边距8px</li>
      <li><strong>卡片描述</strong>：14px，#666，行高1.5</li>
    </ul>

    <h4>9.4.2 列表页布局</h4>
    <ul>
      <li><strong>工具栏</strong>：两端对齐，下边距24px</li>
      <li><strong>搜索框</strong>：宽度320px，高度40px，左对齐</li>
      <li><strong>操作按钮</strong>：右对齐，主按钮</li>
      <li><strong>表格卡片</strong>：背景白色，圆角10px，阴影 0 2px 12px rgba(0,0,0,0.04)</li>
      <li><strong>分页</strong>：居中，上边距24px</li>
    </ul>

    <h4>9.4.3 表单页与弹窗</h4>
    <ul>
      <li><strong>表单组间距</strong>：24px</li>
      <li><strong>标签样式</strong>：14px，#666，下边距8px</li>
      <li><strong>输入框高度</strong>：40px（单行）</li>
      <li><strong>文本域高度</strong>：80px（多行）</li>
      <li><strong>表单提示</strong>：13px，#999上边距4px</li>
      <li><strong>操作区域</strong>：按钮右对齐，间距12px</li>
    </ul>

    <h4>9.4.4 登录页布局</h4>
    <ul>
      <li><strong>整体布局</strong>：居中布局 display: flex; justify-content: center; align-items: center;</li>
      <li><strong>登录容器</strong>：宽度900px，高度500px，两栏布局，圆角12px，阴影 0 8px 24px rgba(0,0,0,0.08)</li>
      <li><strong>左侧Banner</strong>：渐变背景，内边距40px，flex: 1</li>
      <li><strong>右侧表单区</strong>：背景白色，内边距40px，flex: 1</li>
      <li><strong>登录标题</strong>：24px，粗体，下边距40px</li>
      <li><strong>表单项间距</strong>：24px</li>
      <li><strong>按钮区</strong>：上边距8px</li>
      <li><strong>登录页脚</strong>：上边距16px，两端对齐，14px</li>
    </ul>

    <h3>9.5 元素间距与对齐规范</h3>
    <ul>
      <li><strong>水平对齐原则</strong>：左对齐（除特殊说明）</li>
      <li><strong>垂直间距</strong>：遵循间距系统，主要使用8px，16px，24px，40px等间距</li>
      <li><strong>相似元素对齐</strong>：页面中相似元素应在垂直或水平方向保持对齐</li>
      <li><strong>内容分组</strong>：相关内容应使用适当间距分组，增强可读性</li>
      <li><strong>对称性</strong>：页面两侧保持对称的内边距，增强视觉平衡</li>
    </ul>

    <h3>9.6 响应式布局规范</h3>
    <p>当前版本主要针对桌面设备，保持以下断点规则：</p>
    <ul>
      <li><strong>最小宽度</strong>：1200px</li>
      <li><strong>小屏适配</strong>：宽度低于1366px时，侧边栏可考虑收缩为图标模式</li>
      <li><strong>内容适应</strong>：卡片网格在小屏下可调整为2列或1列</li>
      <li><strong>表格滚动</strong>：窄屏下表格允许横向滚动</li>
    </ul>

    <h2>10. CSS代码片段库</h2>
    <p>为确保样式一致性，以下提供核心CSS代码片段，可直接复用于新页面开发。</p>

    <h3>10.1 全局重置与基础样式</h3>
    <div class="example-code" style="max-height: 300px; overflow-y: auto;">
```css
* { margin: 0; padding: 0; box-sizing: border-box; }
body { 
  font-family: "Microsoft YaHei", -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Arial, sans-serif; 
  background-color: #f5f7fa; 
  color: #222;
  min-height: 100vh;
}
.layout {
  display: flex;
  min-height: 100vh;
}
```
    </div>

    <h3>10.2 侧边栏样式</h3>
    <div class="example-code" style="max-height: 300px; overflow-y: auto;">
```css
.sidebar {
  width: 220px;
  background-color: #fff;
  border-right: 1px solid #e5e6eb;
  box-shadow: 2px 0 8px rgba(0,0,0,0.03);
  position: fixed;
  height: 100vh;
  z-index: 100;
}
.sidebar-header {
  height: 60px;
  border-bottom: 1px solid #e5e6eb;
  display: flex;
  align-items: center;
  padding: 0 20px;
}
.logo {
  font-size: 18px;
  font-weight: bold;
  color: #1877f2;
}
.menu {
  padding: 20px 0;
}
.menu-title {
  font-size: 13px;
  color: #999;
  padding: 0 20px;
  margin-bottom: 8px;
}
.menu-item {
  height: 46px;
  display: flex;
  align-items: center;
  padding: 0 20px;
  color: #666;
  font-size: 15px;
  margin-bottom: 2px;
  cursor: pointer;
  position: relative;
  transition: all 0.3s;
}
.menu-item:hover {
  color: #1877f2;
  background-color: rgba(24, 119, 242, 0.04);
}
.menu-item.active {
  color: #1877f2;
  background-color: rgba(24, 119, 242, 0.08);
  font-weight: 500;
}
.menu-item.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 12px;
  bottom: 12px;
  width: 3px;
  background-color: #1877f2;
  border-radius: 0 2px 2px 0;
}
.menu-icon {
  width: 20px;
  height: 20px;
  margin-right: 8px;
  background-color: currentColor;
  opacity: 0.6;
  mask-size: cover;
  -webkit-mask-size: cover;
}
```
    </div>

    <h3>10.3 主内容区样式</h3>
    <div class="example-code" style="max-height: 300px; overflow-y: auto;">
```css
.main {
  flex: 1;
  margin-left: 220px;
}
.header {
  height: 60px;
  background-color: #fff;
  border-bottom: 1px solid #e5e6eb;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  position: sticky;
  top: 0;
  z-index: 99;
}
.page-title {
  font-size: 18px;
  font-weight: 500;
  color: #222;
}
.content {
  padding: 24px;
}
```
    </div>

    <h3>10.4 组件样式</h3>
    <div class="example-code" style="max-height: 300px; overflow-y: auto;">
```css
/* 按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: 40px;
  padding: 0 24px;
  border-radius: 6px;
  font-size: 15px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
  border: none;
}
.btn-primary {
  background-color: #1877f2;
  color: white;
}
.btn-primary:hover {
  background-color: #0d6efd;
}
.btn-text {
  background-color: transparent;
  color: #1877f2;
}
.btn-text:hover {
  background-color: rgba(24, 119, 242, 0.04);
}
.btn-small {
  height: 32px;
  padding: 0 12px;
  font-size: 14px;
}

/* 卡片样式 */
.card {
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.04);
  padding: 24px;
}
.card-title {
  font-size: 18px;
  font-weight: 500;
  color: #222;
  margin-bottom: 16px;
}

/* 表单样式 */
.form-group {
  margin-bottom: 24px;
}
.form-label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  color: #666;
}
.form-control {
  width: 100%;
  height: 40px;
  border: 1px solid #e5e6eb;
  border-radius: 6px;
  padding: 0 16px;
  font-size: 14px;
  color: #222;
  transition: all 0.3s;
}
.form-control:focus {
  outline: none;
  border-color: #1877f2;
  box-shadow: 0 0 0 2px rgba(24, 119, 242, 0.1);
}
textarea.form-control {
  height: 80px;
  padding: 12px 16px;
  resize: none;
}
.form-helper {
  margin-top: 4px;
  font-size: 13px;
  color: #999;
}

/* 表格样式 */
.table-card {
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.04);
  overflow: hidden;
}
.table {
  width: 100%;
  border-collapse: collapse;
}
.table th, .table td {
  padding: 16px 24px;
  text-align: left;
  border-bottom: 1px solid #e5e6eb;
}
.table th {
  font-weight: 500;
  color: #666;
  background-color: #fafafa;
}
.table td {
  color: #222;
}
.table tr:last-child td {
  border-bottom: none;
}
.table tr:hover {
  background-color: #f5f7fa;
}

/* 标签样式 */
.badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: 24px;
  padding: 0 10px;
  border-radius: 12px;
  font-size: 13px;
  font-weight: 500;
}
.badge-blue {
  background-color: rgba(24, 119, 242, 0.1);
  color: #1877f2;
}
.badge-green {
  background-color: rgba(82, 196, 26, 0.1);
  color: #52c41a;
}
.badge-orange {
  background-color: rgba(245, 166, 35, 0.1);
  color: #f5a623;
}
```
    </div>

    <h2>11. 标准HTML组件模板</h2>
    <p>以下是系统中常用组件的HTML模板，可直接复制使用。</p>

    <h3>11.1 页面基础结构</h3>
    <div class="example-code" style="max-height: 300px; overflow-y: auto;">
```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <title>项目管理系统 - 页面标题</title>
  <style>
    /* 粘贴全局样式和组件样式 */
  </style>
</head>
<body>
  <div class="layout">
    <div class="sidebar">
      <div class="sidebar-header">
        <div class="logo">项目管理系统</div>
      </div>
      <div class="menu">
        <div class="menu-title">通用</div>
        <div class="menu-item">
          <div class="menu-icon" style="mask-image: url('图标SVG');"></div>
          首页
        </div>
        <!-- 更多菜单项 -->
      </div>
    </div>
    <div class="main">
      <div class="header">
        <div class="page-title">页面标题</div>
        <div class="user-info">
          <div class="avatar">管</div>
          <div class="username">管理员</div>
        </div>
      </div>
      <div class="content">
        <!-- 页面内容 -->
      </div>
    </div>
  </div>
</body>
</html>
```
    </div>

    <h3>11.2 表单模板</h3>
    <div class="example-code" style="max-height: 300px; overflow-y: auto;">
```html
<div class="card">
  <h3 class="card-title">表单标题</h3>
  <form>
    <div class="form-group">
      <label class="form-label">输入框标签</label>
      <input type="text" class="form-control" placeholder="请输入内容" />
      <div class="form-helper">辅助说明文字</div>
    </div>
    <div class="form-group">
      <label class="form-label">下拉选择</label>
      <select class="form-control">
        <option>选项一</option>
        <option>选项二</option>
      </select>
    </div>
    <div class="form-group">
      <label class="form-label">文本域</label>
      <textarea class="form-control" placeholder="请输入内容"></textarea>
    </div>
    <div style="display: flex; justify-content: flex-end; gap: 12px;">
      <button type="button" class="btn btn-text">取消</button>
      <button type="submit" class="btn btn-primary">提交</button>
    </div>
  </form>
</div>
```
    </div>

    <h3>11.3 表格模板</h3>
    <div class="example-code" style="max-height: 300px; overflow-y: auto;">
```html
<div class="table-card">
  <table class="table">
    <thead>
      <tr>
        <th>列标题1</th>
        <th>列标题2</th>
        <th>列标题3</th>
        <th>操作</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td>数据1</td>
        <td>
          <span class="badge badge-blue">标签文本</span>
        </td>
        <td>数据3</td>
        <td>
          <div class="table-actions">
            <button class="btn btn-small btn-text">详情</button>
            <button class="btn btn-small btn-text">编辑</button>
            <button class="btn btn-small btn-text" style="color: #ff4d4f;">删除</button>
          </div>
        </td>
      </tr>
      <!-- 更多行 -->
    </tbody>
  </table>
</div>
```
    </div>

    <h3>11.4 弹窗模板</h3>
    <p>注意：以下代码仅作示例展示，请复制到实际项目中使用，不会在当前文档中自动执行。</p>
    <div class="example-code" style="max-height: 300px; overflow-y: auto;">
```html
<!-- 弹窗HTML结构 -->
<div class="modal-overlay" style="display: none;">
  <div class="modal">
    <div class="modal-header">
      <div class="modal-title">弹窗标题</div>
      <div class="modal-close" onclick="document.querySelector('.modal-overlay').style.display='none';">
        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg>
      </div>
    </div>
    <div class="modal-body">
      <!-- 弹窗内容 -->
    </div>
    <div class="modal-footer">
      <button class="btn btn-text" onclick="document.querySelector('.modal-overlay').style.display='none';">取消</button>
      <button class="btn btn-primary">确定</button>
    </div>
  </div>
</div>

<!-- 示例：打开弹窗的按钮 -->
<button class="btn btn-primary" onclick="document.querySelector('.modal-overlay').style.display='flex';">
  打开弹窗
</button>

<style>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0,0,0,0.5);
  display: none; /* 默认隐藏 */
  align-items: center;
  justify-content: center;
  z-index: 1000;
}
.modal {
  width: 520px;
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 4px 24px rgba(0,0,0,0.1);
  overflow: hidden;
}
.modal-header {
  padding: 16px 24px;
  border-bottom: 1px solid #e5e6eb;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.modal-title {
  font-size: 18px;
  font-weight: 500;
  color: #222;
}
.modal-close {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s;
  color: #999;
}
.modal-close:hover {
  background-color: rgba(0,0,0,0.04);
  color: #666;
}
.modal-body {
  padding: 24px;
}
.modal-footer {
  padding: 16px 24px;
  border-top: 1px solid #e5e6eb;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 12px;
}
</style>

<!-- JavaScript控制弹窗示例 -->
<script>
// 打开弹窗
function openModal() {
  document.querySelector('.modal-overlay').style.display = 'flex';
}

// 关闭弹窗
function closeModal() {
  document.querySelector('.modal-overlay').style.display = 'none';
}

// 示例：点击遮罩层关闭弹窗
document.querySelector('.modal-overlay').addEventListener('click', function(e) {
  if (e.target === this) {
    closeModal();
  }
});
</script>
```
    </div>

    <h2>12. SVG图标列表</h2>
    <p>系统使用的所有图标均采用内联SVG格式，确保一致的线条风格。为方便复用，以下提供主要图标的SVG代码。</p>

    <div class="example-grid">
      <div class="example-item">
        <div class="example-preview">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"/><polyline points="9 22 9 12 15 12 15 22"/></svg>
        </div>
        <div class="example-code">首页图标
```html
<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"/><polyline points="9 22 9 12 15 12 15 22"/></svg>
```

使用方式：
```css
.menu-icon {
  mask-image: url('data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 width=%2224%22 height=%2224%22 viewBox=%220 0 24 24%22 fill=%22none%22 stroke=%22currentColor%22 stroke-width=%222%22 stroke-linecap=%22round%22 stroke-linejoin=%22round%22><path d=%22m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z%22/><polyline points=%229 22 9 12 15 12 15 22%22/></svg>');
}
```</div>
      </div>

      <div class="example-item">
        <div class="example-preview">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"></path><rect x="8" y="2" width="8" height="4" rx="1" ry="1"></rect></svg>
        </div>
        <div class="example-code">待办事项图标
```html
<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"></path><rect x="8" y="2" width="8" height="4" rx="1" ry="1"></rect></svg>
```

使用方式：
```css
.menu-icon {
  mask-image: url('data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 width=%2224%22 height=%2224%22 viewBox=%220 0 24 24%22 fill=%22none%22 stroke=%22currentColor%22 stroke-width=%222%22 stroke-linecap=%22round%22 stroke-linejoin=%22round%22><path d=%22M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2%22></path><rect x=%228%22 y=%222%22 width=%228%22 height=%224%22 rx=%221%22 ry=%221%22></rect></svg>');
}
```</div>
      </div>

      <div class="example-item">
        <div class="example-preview">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5l2 3h9a2 2 0 0 1 2 2z"></path></svg>
        </div>
        <div class="example-code">项目管理图标
```html
<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5l2 3h9a2 2 0 0 1 2 2z"></path></svg>
```

使用方式：
```css
.menu-icon {
  mask-image: url('data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 width=%2224%22 height=%2224%22 viewBox=%220 0 24 24%22 fill=%22none%22 stroke=%22currentColor%22 stroke-width=%222%22 stroke-linecap=%22round%22 stroke-linejoin=%22round%22><path d=%22M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5l2 3h9a2 2 0 0 1 2 2z%22></path></svg>');
}
```</div>
      </div>
    </div>

    <h2>13. 交互状态规范</h2>
    <p>为确保界面交互体验一致，以下定义了各种元素的交互状态样式。</p>

    <h3>13.1 按钮状态</h3>
    <ul>
      <li><strong>默认状态</strong>：基础样式，边框和背景色符合按钮类型</li>
      <li><strong>悬停状态</strong>：主按钮背景色加深至 #0d6efd，文本按钮添加轻微背景色 rgba(24, 119, 242, 0.04)</li>
      <li><strong>点击状态</strong>：按钮轻微缩小效果，transform: scale(0.98)</li>
      <li><strong>禁用状态</strong>：透明度降低至 0.6，cursor: not-allowed，不响应交互事件</li>
    </ul>

    <h3>13.2 输入框状态</h3>
    <ul>
      <li><strong>默认状态</strong>：边框颜色 #e5e6eb</li>
      <li><strong>聚焦状态</strong>：边框颜色 #1877f2，添加浅蓝色阴影 0 0 0 2px rgba(24, 119, 242, 0.1)</li>
      <li><strong>校验错误</strong>：边框颜色 #ff4d4f，添加浅红色阴影 0 0 0 2px rgba(255, 77, 79, 0.1)</li>
      <li><strong>禁用状态</strong>：背景色 #f5f7fa，透明度降低至 0.6，不可编辑</li>
    </ul>

    <h3>13.3 卡片与容器状态</h3>
    <ul>
      <li><strong>默认状态</strong>：阴影 0 2px 12px rgba(0,0,0,0.04)</li>
      <li><strong>悬停状态</strong>（适用于可交互卡片）：轻微上移 transform: translateY(-4px)，阴影增强 0 6px 20px rgba(0,0,0,0.08)</li>
      <li><strong>活动状态</strong>：可添加轻微的边框或背景色标识</li>
    </ul>

    <h3>13.4 菜单与导航状态</h3>
    <ul>
      <li><strong>默认状态</strong>：文字颜色 #666</li>
      <li><strong>悬停状态</strong>：文字颜色 #1877f2，背景色 rgba(24, 119, 242, 0.04)</li>
      <li><strong>活动状态</strong>：文字颜色 #1877f2，背景色 rgba(24, 119, 242, 0.08)，左侧蓝色指示条，文字加粗</li>
    </ul>

    <h2>14. 真实数据示例</h2>
    <p>为确保在设计时可以使用符合真实场景的数据，以下提供各类数据示例。</p>

    <h3>14.1 用户信息</h3>
    <ul>
      <li>用户名：管理员、张三、李四、王五、赵六、钱七</li>
      <li>角色：管理员、开发人员、测试人员、产品经理</li>
      <li>头像文本：使用姓氏首字</li>
    </ul>

    <h3>14.2 项目数据</h3>
    <ul>
      <li>项目名称：用户中心、消息服务、支付系统、订单管理、商品管理</li>
      <li>代码仓库：user-center、message-service、payment-system、order-manager</li>
      <li>项目描述：集成用户身份认证、权限管理等功能的中心化服务</li>
    </ul>

    <h3>14.3 时间与状态</h3>
    <ul>
      <li>日期格式：2023-06-15 （年-月-日）</li>
      <li>时间格式：12:30:45 （时:分:秒）</li>
      <li>状态示例：进行中、已完成、已关闭、待审核</li>
    </ul>

    <h2>15. 自动生成HTML代码指南</h2>
    <p>当需要创建新页面时，可遵循以下步骤快速生成符合规范的HTML代码：</p>

    <ol>
      <li>复制基础页面结构（第11.1节）作为起点</li>
      <li>根据页面类型（9.4节），选择适当的页面布局规范</li>
      <li>添加核心CSS样式（10节）</li>
      <li>根据页面需求，从组件模板（11节）选择所需组件</li>
      <li>根据状态规范（13节）调整组件交互效果</li>
      <li>使用图标库（12节）添加所需图标</li>
      <li>使用真实数据示例（14节）填充内容</li>
    </ol>

    <p>遵循以上步骤，可确保新生成的页面与现有设计保持完全一致的风格和规范。</p>

  </div>
</body>
</html>