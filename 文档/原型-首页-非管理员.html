<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <title>项目管理系统 - 工作台</title>
  <style>
    * { margin: 0; padding: 0; box-sizing: border-box; }
    body { 
      font-family: "Microsoft YaHei", -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Arial, sans-serif; 
      background-color: #f5f7fa; 
      color: #222;
      min-height: 100vh;
    }
    .layout {
      display: flex;
      min-height: 100vh;
    }
    .sidebar {
      width: 220px;
      background-color: #fff;
      border-right: 1px solid #e5e6eb;
      box-shadow: 2px 0 8px rgba(0,0,0,0.03);
      position: fixed;
      height: 100vh;
      z-index: 100;
    }
    .sidebar-header {
      height: 60px;
      border-bottom: 1px solid #e5e6eb;
      display: flex;
      align-items: center;
      padding: 0 20px;
    }
    .logo {
      font-size: 18px;
      font-weight: bold;
      color: #1877f2;
    }
    .menu {
      padding: 20px 0;
    }
    .menu-title {
      font-size: 13px;
      color: #999;
      padding: 0 20px;
      margin-bottom: 8px;
    }
    .menu-item {
      height: 46px;
      display: flex;
      align-items: center;
      padding: 0 20px;
      color: #666;
      font-size: 15px;
      margin-bottom: 2px;
      cursor: pointer;
      position: relative;
      transition: all 0.3s;
    }
    .menu-item:hover {
      color: #1877f2;
      background-color: rgba(24, 119, 242, 0.04);
    }
    .menu-item.active {
      color: #1877f2;
      background-color: rgba(24, 119, 242, 0.08);
      font-weight: 500;
    }
    .menu-item.active::before {
      content: '';
      position: absolute;
      left: 0;
      top: 12px;
      bottom: 12px;
      width: 3px;
      background-color: #1877f2;
      border-radius: 0 2px 2px 0;
    }
    .menu-icon {
      width: 20px;
      height: 20px;
      margin-right: 8px;
      background-color: currentColor;
      opacity: 0.6;
      mask-size: cover;
      -webkit-mask-size: cover;
    }
    .main {
      flex: 1;
      margin-left: 220px;
    }
    .header {
      height: 60px;
      background-color: #fff;
      border-bottom: 1px solid #e5e6eb;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 24px;
      position: sticky;
      top: 0;
      z-index: 99;
    }
    .page-title {
      font-size: 18px;
      font-weight: 500;
      color: #222;
    }
    .user-info {
      display: flex;
      align-items: center;
    }
    .avatar {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      background-color: #1877f2;
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 500;
      margin-right: 8px;
    }
    .username {
      font-size: 14px;
      color: #666;
    }
    .role-badge-header {
      margin-left: 8px;
      font-size: 12px;
      background-color: rgba(24, 119, 242, 0.1);
      color: #1877f2;
      padding: 2px 6px;
      border-radius: 4px;
    }
    .content {
      padding: 24px;
    }
    .page-section-title {
        font-size: 20px;
        font-weight: 500;
        color: #222;
        margin-bottom: 16px;
        padding-bottom: 8px;
        border-bottom: 1px solid #eee;
    }
    .filter-row {
      display: flex;
      gap: 16px;
      margin-bottom: 24px;
    }
    .filter-select {
      height: 36px;
      padding: 0 12px;
      border: 1px solid #e5e6eb;
      border-radius: 6px;
      background-color: #fff;
      color: #666;
      font-size: 14px;
      width: 180px;
    }
    .tabs {
      display: flex;
      border-bottom: 1px solid #e5e6eb;
      margin-bottom: 24px;
    }
    .tab {
      padding: 0 20px;
      height: 40px;
      display: flex;
      align-items: center;
      font-size: 15px;
      color: #666;
      position: relative;
      cursor: pointer;
    }
    .tab.active {
      color: #1877f2;
      font-weight: 500;
    }
    .tab.active::after {
      content: '';
      position: absolute;
      left: 20px;
      right: 20px;
      bottom: -1px;
      height: 2px;
      background-color: #1877f2;
      border-radius: 2px 2px 0 0;
    }
    .tab-badge {
      min-width: 18px;
      height: 18px;
      background-color: #1877f2;
      border-radius: 9px;
      color: white;
      font-size: 12px;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      padding: 0 6px;
      margin-left: 8px;
    }
    .task-card {
      background-color: #fff;
      border-radius: 10px;
      box-shadow: 0 2px 12px rgba(0,0,0,0.04);
      margin-bottom: 16px;
      padding: 20px;
      transition: all 0.3s;
    }
    .task-card:hover {
      box-shadow: 0 4px 16px rgba(0,0,0,0.08);
    }
    .task-header {
      display: flex;
      justify-content: space-between;
      margin-bottom: 12px; /* Reduced margin */
    }
    .task-title-section {
        display: flex;
        align-items: center;
        margin-bottom: 4px;
    }
    .task-title {
      font-size: 16px;
      font-weight: 500;
      color: #222;
    }
    .task-role-badge {
      font-size: 11px;
      background-color: rgba(24, 119, 242, 0.08); /* Default for dev */
      color: #1877f2;
      padding: 2px 8px;
      border-radius: 10px;
      margin-left: 8px;
      font-weight: normal;
    }
    .task-role-badge.test {
      background-color: rgba(82, 196, 26, 0.08); /* Green for test */
      color: #52c41a;
    }
    .task-project {
      font-size: 13px;
      color: #666;
      margin-top: 4px;
    }
    .task-meta {
      display: flex;
      align-items: flex-end;
      flex-direction: column;
    }
    .badge {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      height: 24px;
      padding: 0 10px;
      border-radius: 12px;
      font-size: 13px;
      font-weight: 500;
    }
    .badge-blue { background-color: rgba(24, 119, 242, 0.1); color: #1877f2; }
    .badge-green { background-color: rgba(82, 196, 26, 0.1); color: #52c41a; }
    .badge-orange { background-color: rgba(245, 166, 35, 0.1); color: #f5a623; }
    .badge-red { background-color: rgba(255, 77, 79, 0.1); color: #ff4d4f; }
    
    .task-desc {
      font-size: 14px;
      color: #666;
      margin-bottom: 16px;
      line-height: 1.5;
    }
    .task-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-top: 16px;
      border-top: 1px solid #e5e6eb;
    }
    .task-date {
      font-size: 13px;
      color: #999;
      display: flex;
      align-items: center;
    }
    .task-date-icon {
      width: 16px;
      height: 16px;
      margin-right: 4px;
      opacity: 0.5;
    }
    .btn {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      height: 32px;
      padding: 0 12px;
      border-radius: 6px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s;
      border: none;
    }
    .btn-primary { background-color: #1877f2; color: white; }
    .btn-primary:hover { background-color: #0d6efd; }
    .btn-outline { background-color: transparent; color: #666; border: 1px solid #e5e6eb; }
    .btn-outline:hover { border-color: #1877f2; color: #1877f2; }
    .btn-success { background-color: #52c41a; color: white; }
    .btn-success:hover { background-color: #49ad18; }
    .btn-danger { background-color: #ff4d4f; color: white; }
    .btn-danger:hover { background-color: #ff3436; }
    .task-actions { display: flex; gap: 8px; }

    .meta-info {
      display: flex;
      align-items: center;
      font-size: 13px;
      color: #666;
      margin-top: 8px;
    }
    .meta-icon {
      width: 16px;
      height: 16px;
      margin-right: 4px;
      opacity: 0.7;
    }
    .meta-text {
      background-color: #f0f2f5;
      padding: 2px 6px;
      border-radius: 4px;
      font-family: monospace;
      margin-left: 4px;
    }
    .quick-actions-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 16px;
      margin-bottom: 24px;
    }
    .action-card {
      background-color: #fff;
      border-radius: 10px;
      box-shadow: 0 2px 12px rgba(0,0,0,0.04);
      padding: 20px;
      text-align: center;
      transition: all 0.3s;
      cursor: pointer;
    }
    .action-card:hover {
      transform: translateY(-3px);
      box-shadow: 0 5px 15px rgba(0,0,0,0.07);
    }
    .action-icon {
      width: 36px;
      height: 36px;
      border-radius: 8px;
      background-color: rgba(24, 119, 242, 0.1);
      color: #1877f2;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto 12px;
    }
    .action-title {
      font-size: 16px;
      font-weight: 500;
      color: #222;
      margin-bottom: 4px;
    }
    .action-desc {
      font-size: 13px;
      color: #666;
      line-height: 1.4;
    }
  </style>
</head>
<body>
  <div class="layout">
    <div class="sidebar">
      <div class="sidebar-header">
        <div class="logo">项目管理系统</div>
      </div>
      <div class="menu">
        <div class="menu-title">通用</div>
        <div class="menu-item active">
          <div class="menu-icon" style="mask-image: url('data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 width=%2224%22 height=%2224%22 viewBox=%220 0 24 24%22 fill=%22none%22 stroke=%22currentColor%22 stroke-width=%222%22 stroke-linecap=%22round%22 stroke-linejoin=%22round%22><path d=%22m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z%22/><polyline points=%229 22 9 12 15 12 15 22%22/></svg>');"></div>
          首页
        </div>
        <div class="menu-title">项目</div>
        <div class="menu-item">
          <div class="menu-icon" style="mask-image: url('data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 width=%2224%22 height=%2224%22 viewBox=%220 0 24 24%22 fill=%22none%22 stroke=%22currentColor%22 stroke-width=%222%22 stroke-linecap=%22round%22 stroke-linejoin=%22round%22><path d=%22M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5l2 3h9a2 2 0 0 1 2 2z%22></path></svg>');"></div>
          项目管理
        </div>
        <div class="menu-item">
          <div class="menu-icon" style="mask-image: url('data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 width=%2224%22 height=%2224%22 viewBox=%220 0 24 24%22 fill=%22none%22 stroke=%22currentColor%22 stroke-width=%222%22 stroke-linecap=%22round%22 stroke-linejoin=%22round%22><path d=%22M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z%22></path><polyline points=%2214 2 14 8 20 8%22></polyline><line x1=%2216%22 y1=%2213%22 x2=%228%22 y2=%2213%22></line><line x1=%2216%22 y1=%2217%22 x2=%228%22 y2=%2217%22></line><polyline points=%2210 9 9 9 8 9%22></polyline></svg>');"></div>
          需求管理
        </div>
        <div class="menu-item">
          <div class="menu-icon" style="mask-image: url('data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 width=%2224%22 height=%2224%22 viewBox=%220 0 24 24%22 fill=%22none%22 stroke=%22currentColor%22 stroke-width=%222%22 stroke-linecap=%22round%22 stroke-linejoin=%22round%22><polyline points=%2221 8 21 21 3 21 3 8%22></polyline><rect x=%221%22 y=%223%22 width=%2222%22 height=%225%22></rect><line x1=%2210%22 y1=%2212%22 x2=%2214%22 y2=%2212%22></line></svg>');"></div>
          合并请求
        </div>
        <div class="menu-title">测试</div>
        <div class="menu-item">
          <div class="menu-icon" style="mask-image: url('data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 width=%2224%22 height=%2224%22 viewBox=%220 0 24 24%22 fill=%22none%22 stroke=%22currentColor%22 stroke-width=%222%22 stroke-linecap=%22round%22 stroke-linejoin=%22round%22><path d=%22M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z%22></path><polyline points=%2214 2 14 8 20 8%22></polyline><path d=%22M16 13.58A2 2 0 0 0 15.3 12a2 2 0 0 0 .7-1.58A2 2 0 0 0 14 8.5h-6v8h6a2 2 0 0 0 2-1.92A2 2 0 0 0 16 13.58Z%22></path></svg>');"></div>
          测试用例
        </div>
      </div>
    </div>
    <div class="main">
      <div class="header">
        <div class="page-title">我的工作台</div>
        <div class="user-info">
          <div class="avatar">李</div>
          <div class="username">李四</div>
          <div class="role-badge-header">开发/测试</div> <!-- 示例：用户为双重角色 -->
          <!-- <div class="role-badge-header">开发</div> --> <!-- 示例：用户为开发 -->
          <!-- <div class="role-badge-header">测试</div> --> <!-- 示例：用户为测试 -->
        </div>
      </div>
      <div class="content">
        <div class="page-section-title">我的任务</div>
        
        <div class="tabs">
          <div class="tab active">全部 <span class="tab-badge">5</span></div>
          <div class="tab">开发相关 <span class="tab-badge">3</span></div>
          <div class="tab">测试相关 <span class="tab-badge">2</span></div>
          <div class="tab">待认领</div>
        </div>
        
        <!-- 开发任务示例 -->
        <div class="task-card">
          <div class="task-header">
            <div>
              <div class="task-title-section">
                 <div class="task-title">用户中心-登录功能优化</div>
                 <div class="task-role-badge">开发任务</div>
              </div>
              <div class="task-project">项目：用户中心</div>
              <div class="meta-info">
                <svg class="meta-icon" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="6" y1="3" x2="6" y2="15"></line><circle cx="18" cy="6" r="3"></circle><circle cx="6" cy="18" r="3"></circle><path d="M18 9a9 9 0 0 1-9 9"></path></svg>
                分支：<span class="meta-text">feature/login-opt</span>
              </div>
            </div>
            <div class="task-meta">
              <span class="badge badge-blue">开发中</span>
              <div style="margin-top: 8px; font-size: 13px; color: #999;">截止日期：2023-06-20</div>
            </div>
          </div>
          <div class="task-desc">
            优化用户登录功能，包括：1. 添加记住密码选项；2. 支持手机号一键登录；3. 优化登录失败提示；4. 处理异常登录行为拦截。
          </div>
          <div class="task-footer">
            <div class="task-date">
              <svg class="task-date-icon" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><polyline points="12 6 12 12 16 14"></polyline></svg>
              认领于 2023-05-01
            </div>
            <div class="task-actions">
              <button class="btn btn-primary">提交测试</button>
            </div>
          </div>
        </div>
        
        <!-- 测试任务示例 -->
        <div class="task-card">
          <div class="task-header">
            <div>
              <div class="task-title-section">
                <div class="task-title">支付系统-支付宝回调接口开发</div>
                <div class="task-role-badge test">测试任务</div>
              </div>
              <div class="task-project">项目：支付系统</div>
              <div class="meta-info">
                <svg class="meta-icon" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path><circle cx="12" cy="7" r="4"></circle></svg>
                开发者：张小明
              </div>
               <div class="meta-info">
                <svg class="meta-icon" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="6" y1="3" x2="6" y2="15"></line><circle cx="18" cy="6" r="3"></circle><circle cx="6" cy="18" r="3"></circle><path d="M18 9a9 9 0 0 1-9 9"></path></svg>
                分支：<span class="meta-text">feature/payment-callback</span>
              </div>
            </div>
            <div class="task-meta">
              <span class="badge badge-orange">待测试</span>
              <div style="margin-top: 8px; font-size: 13px; color: #999;">提交时间：2023-06-15</div>
            </div>
          </div>
          <div class="task-desc">
            实现支付宝支付回调接口，处理支付成功/失败的业务逻辑，更新订单状态，发送通知等。需处理签名验证、重复通知、异常情况等。
          </div>
          <div class="task-footer">
            <div class="task-date">
              <svg class="task-date-icon" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><polyline points="12 6 12 12 16 14"></polyline></svg>
              开发完成于 2023-06-15
            </div>
            <div class="task-actions">
              <button class="btn btn-danger">驳回</button>
              <button class="btn btn-success">通过测试</button>
            </div>
          </div>
        </div>

      </div>
    </div>
  </div>
</body>
</html> 