<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <title>项目管理系统 - 需求列表</title>
  <style>
    * { margin: 0; padding: 0; box-sizing: border-box; }
    body { 
      font-family: "Microsoft YaHei", -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Arial, sans-serif; 
      background-color: #f5f7fa; 
      color: #222;
      min-height: 100vh;
    }
    .layout {
      display: flex;
      min-height: 100vh;
    }
    .sidebar {
      width: 220px;
      background-color: #fff;
      border-right: 1px solid #e5e6eb;
      box-shadow: 2px 0 8px rgba(0,0,0,0.03);
      position: fixed;
      height: 100vh;
      z-index: 100;
    }
    .sidebar-header {
      height: 60px;
      border-bottom: 1px solid #e5e6eb;
      display: flex;
      align-items: center;
      padding: 0 20px;
    }
    .logo {
      font-size: 18px;
      font-weight: bold;
      color: #1877f2;
    }
    .menu {
      padding: 20px 0;
    }
    .menu-title {
      font-size: 13px;
      color: #999;
      padding: 0 20px;
      margin-bottom: 8px;
    }
    .menu-item {
      height: 46px;
      display: flex;
      align-items: center;
      padding: 0 20px;
      color: #666;
      font-size: 15px;
      margin-bottom: 2px;
      cursor: pointer;
      position: relative;
      transition: all 0.3s;
    }
    .menu-item:hover {
      color: #1877f2;
      background-color: rgba(24, 119, 242, 0.04);
    }
    .menu-item.active {
      color: #1877f2;
      background-color: rgba(24, 119, 242, 0.08);
      font-weight: 500;
    }
    .menu-item.active::before {
      content: '';
      position: absolute;
      left: 0;
      top: 12px;
      bottom: 12px;
      width: 3px;
      background-color: #1877f2;
      border-radius: 0 2px 2px 0;
    }
    .menu-icon {
      width: 20px;
      height: 20px;
      margin-right: 8px;
      background-color: currentColor;
      opacity: 0.6;
      mask-size: cover;
      -webkit-mask-size: cover;
    }
    .main {
      flex: 1;
      margin-left: 220px;
    }
    .header {
      height: 60px;
      background-color: #fff;
      border-bottom: 1px solid #e5e6eb;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 24px;
      position: sticky;
      top: 0;
      z-index: 99;
    }
    .page-title {
      font-size: 18px;
      font-weight: 500;
      color: #222;
    }
    .user-info {
      display: flex;
      align-items: center;
    }
    .avatar {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      background-color: #1877f2;
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 500;
      margin-right: 8px;
    }
    .username {
      font-size: 14px;
      color: #666;
    }
    .content {
      padding: 24px;
    }

    /* 工具栏样式 */
    .toolbar {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24px;
    }
    .toolbar-left {
      display: flex;
      align-items: center;
      gap: 16px;
    }
    .stats-info {
      font-size: 15px;
      color: #666;
    }
    .stats-number {
      color: #1877f2;
      font-weight: 500;
    }

    /* 搜索筛选区域 */
    .filter-card {
      background-color: #fff;
      border-radius: 10px;
      box-shadow: 0 2px 12px rgba(0,0,0,0.04);
      padding: 24px;
      margin-bottom: 24px;
    }
    .filter-title {
      font-size: 16px;
      font-weight: 500;
      color: #222;
      margin-bottom: 16px;
    }
    .filter-form {
      display: flex;
      flex-wrap: wrap;
      gap: 16px;
      align-items: end;
    }
    .filter-group {
      display: flex;
      flex-direction: column;
    }
    .filter-label {
      font-size: 14px;
      color: #666;
      margin-bottom: 8px;
    }
    .filter-control {
      width: 160px;
      height: 40px;
      border: 1px solid #e5e6eb;
      border-radius: 6px;
      padding: 0 16px;
      font-size: 14px;
      color: #222;
      transition: all 0.3s;
    }
    .filter-control:focus {
      outline: none;
      border-color: #1877f2;
      box-shadow: 0 0 0 2px rgba(24, 119, 242, 0.1);
    }
    .filter-actions {
      display: flex;
      gap: 12px;
    }

    /* 按钮样式 */
    .btn {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      height: 40px;
      padding: 0 24px;
      border-radius: 6px;
      font-size: 15px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s;
      border: none;
    }
    .btn-primary {
      background-color: #1877f2;
      color: white;
    }
    .btn-primary:hover {
      background-color: #0d6efd;
    }
    .btn-text {
      background-color: transparent;
      color: #1877f2;
      border: 1px solid #e5e6eb;
    }
    .btn-text:hover {
      border-color: #1877f2;
      background-color: rgba(24, 119, 242, 0.04);
    }
    .btn-icon {
      width: 16px;
      height: 16px;
      margin-right: 6px;
    }

    /* 需求卡片网格 */
    .requirements-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
      gap: 24px;
      margin-bottom: 24px;
    }

    /* 需求卡片样式 */
    .requirement-card {
      background-color: #fff;
      border-radius: 10px;
      box-shadow: 0 2px 12px rgba(0,0,0,0.04);
      overflow: hidden;
      transition: all 0.3s;
      cursor: pointer;
      border: 1px solid #e5e6eb;
    }
    .requirement-card:hover {
      transform: translateY(-4px);
      box-shadow: 0 6px 20px rgba(0,0,0,0.08);
    }

    /* 卡片头部 */
    .card-header {
      height: 80px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 16px 20px;
      position: relative;
    }
    .card-header.type-bug {
      background: linear-gradient(135deg, #f5a623, #ffd666);
    }
    .card-header.type-hotbug {
      background: linear-gradient(135deg, #ff4d4f, #ff7875);
    }
    .card-header.type-feature {
      background: linear-gradient(135deg, #1877f2, #36cfc9);
    }
    .requirement-id {
      font-size: 20px;
      font-weight: bold;
      color: white;
      background-color: rgba(255, 255, 255, 0.2);
      padding: 8px 16px;
      border-radius: 8px;
      backdrop-filter: blur(10px);
    }
    .project-name {
      font-size: 14px;
      color: rgba(255, 255, 255, 0.9);
      font-weight: 500;
    }

    /* 卡片主体 */
    .card-body {
      padding: 20px;
    }
    .requirement-title {
      font-size: 16px;
      font-weight: 500;
      color: #222;
      margin-bottom: 12px;
      line-height: 1.4;
      height: 44px;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
    }

    /* 卡片元信息 */
    .card-meta {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      margin-bottom: 16px;
    }
    .card-meta .badge {
      font-size: 12px;
      height: 22px;
      padding: 0 8px;
    }

    /* 优先级标签 */
    .priority-tag {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      width: 28px;
      height: 22px;
      border-radius: 4px;
      font-size: 12px;
      font-weight: 500;
    }
    .priority-p0 {
      background-color: rgba(255, 77, 79, 0.1);
      color: #ff4d4f;
    }
    .priority-p1 {
      background-color: rgba(245, 166, 35, 0.1);
      color: #f5a623;
    }
    .priority-p2 {
      background-color: rgba(82, 196, 26, 0.1);
      color: #52c41a;
    }

    /* 标签样式 */
    .badge {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      height: 24px;
      padding: 0 10px;
      border-radius: 12px;
      font-size: 13px;
      font-weight: 500;
    }
    .badge-blue {
      background-color: rgba(24, 119, 242, 0.1);
      color: #1877f2;
    }
    .badge-green {
      background-color: rgba(82, 196, 26, 0.1);
      color: #52c41a;
    }
    .badge-orange {
      background-color: rgba(245, 166, 35, 0.1);
      color: #f5a623;
    }
    .badge-red {
      background-color: rgba(255, 77, 79, 0.1);
      color: #ff4d4f;
    }
    .badge-gray {
      background-color: rgba(144, 147, 153, 0.1);
      color: #909399;
    }
    .badge-purple {
      background-color: rgba(146, 84, 222, 0.1);
      color: #9254de;
    }

    /* 时间信息 */
    .card-time {
      display: flex;
      justify-content: space-between;
      margin-bottom: 16px;
      font-size: 13px;
      color: #999;
    }
    .time-item {
      display: flex;
      align-items: center;
      gap: 4px;
    }
    .time-icon {
      width: 14px;
      height: 14px;
      opacity: 0.6;
    }

    /* 角色信息 */
    .card-roles {
      display: flex;
      gap: 4px;
      margin-bottom: 16px;
    }
    .role-tag {
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      font-weight: 500;
    }
    .role-developer {
      background-color: rgba(24, 119, 242, 0.1);
      color: #1877f2;
    }
    .role-tester {
      background-color: rgba(82, 196, 26, 0.1);
      color: #52c41a;
    }

    /* 卡片底部操作区 */
    .card-actions {
      display: flex;
      gap: 8px;
      flex-wrap: wrap;
    }
    .action-btn {
      padding: 6px 12px;
      font-size: 13px;
      border-radius: 4px;
      border: 1px solid;
      cursor: pointer;
      transition: all 0.3s;
      background-color: transparent;
    }
    .action-view {
      color: #1877f2;
      border-color: #1877f2;
    }
    .action-claim {
      color: #52c41a;
      border-color: #52c41a;
    }
    .action-submit {
      color: #f5a623;
      border-color: #f5a623;
    }
    .action-withdraw {
      color: #909399;
      border-color: #909399;
    }
    .action-approve {
      color: #52c41a;
      border-color: #52c41a;
    }
    .action-reject {
      color: #ff4d4f;
      border-color: #ff4d4f;
    }
    .action-btn:hover {
      opacity: 0.8;
      transform: scale(1.02);
    }

    /* 分页 */
    .pagination-container {
      display: flex;
      justify-content: space-between;
      align-items: center;
      background-color: #fff;
      border-radius: 10px;
      box-shadow: 0 2px 12px rgba(0,0,0,0.04);
      padding: 20px 24px;
    }
    .pagination-info {
      font-size: 14px;
      color: #666;
    }
    .pagination-controls {
      display: flex;
      align-items: center;
      gap: 8px;
    }
    .pagination-btn {
      width: 32px;
      height: 32px;
      border: 1px solid #e5e6eb;
      border-radius: 6px;
      background-color: white;
      color: #666;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
      transition: all 0.3s;
    }
    .pagination-btn:hover {
      border-color: #1877f2;
      color: #1877f2;
    }
    .pagination-btn.active {
      background-color: #1877f2;
      border-color: #1877f2;
      color: white;
    }
    .pagination-btn.disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
    .page-size-selector {
      margin-left: 16px;
      font-size: 14px;
      color: #666;
    }
    .page-size-select {
      margin: 0 8px;
      padding: 4px 8px;
      border: 1px solid #e5e6eb;
      border-radius: 4px;
    }

    /* 空状态 */
    .empty-state {
      padding: 60px 0;
      text-align: center;
      color: #999;
    }
    .empty-icon {
      font-size: 48px;
      margin-bottom: 16px;
      opacity: 0.3;
    }
    .empty-text {
      font-size: 16px;
      margin-bottom: 8px;
    }
    .empty-desc {
      font-size: 14px;
      color: #ccc;
    }

    /* 弹窗样式 */
    .modal-overlay {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: rgba(0,0,0,0.5);
      display: none;
      align-items: center;
      justify-content: center;
      z-index: 1000;
    }
    .modal {
      width: 520px;
      background-color: #fff;
      border-radius: 10px;
      box-shadow: 0 4px 24px rgba(0,0,0,0.1);
      overflow: hidden;
    }
    .modal-header {
      padding: 16px 24px;
      border-bottom: 1px solid #e5e6eb;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    .modal-title {
      font-size: 18px;
      font-weight: 500;
      color: #222;
    }
    .modal-close {
      width: 32px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      cursor: pointer;
      transition: all 0.3s;
      color: #999;
    }
    .modal-close:hover {
      background-color: rgba(0,0,0,0.04);
      color: #666;
    }
    .modal-body {
      padding: 24px;
    }
    .modal-footer {
      padding: 16px 24px;
      border-top: 1px solid #e5e6eb;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      gap: 12px;
    }

    /* 表单样式 */
    .form-group {
      margin-bottom: 24px;
    }
    .form-label {
      display: block;
      margin-bottom: 8px;
      font-size: 14px;
      color: #666;
    }
    .form-control {
      width: 100%;
      height: 40px;
      border: 1px solid #e5e6eb;
      border-radius: 6px;
      padding: 0 16px;
      font-size: 14px;
      color: #222;
      transition: all 0.3s;
    }
    .form-control:focus {
      outline: none;
      border-color: #1877f2;
      box-shadow: 0 0 0 2px rgba(24, 119, 242, 0.1);
    }
    textarea.form-control {
      height: 80px;
      padding: 12px 16px;
      resize: none;
    }

    /* 响应式设计 */
    @media (max-width: 1366px) {
      .filter-form {
        gap: 12px;
      }
      .filter-control {
        width: 140px;
      }
      .requirements-grid {
        grid-template-columns: repeat(auto-fill, minmax(340px, 1fr));
        gap: 20px;
      }
    }

    @media (max-width: 1200px) {
      .filter-form {
        flex-direction: column;
        align-items: stretch;
      }
      .filter-group {
        width: 100%;
      }
      .filter-control {
        width: 100%;
      }
      .filter-actions {
        justify-content: flex-end;
      }
      .requirements-grid {
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 16px;
      }
    }

    @media (max-width: 768px) {
      .requirements-grid {
        grid-template-columns: 1fr;
      }
    }
  </style>
</head>
<body>
  <div class="layout">
    <!-- 侧边栏 -->
    <div class="sidebar">
      <div class="sidebar-header">
        <div class="logo">项目管理系统</div>
      </div>
      <div class="menu">
        <div class="menu-title">通用</div>
        <div class="menu-item">
          <div class="menu-icon" style="mask-image: url('data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 width=%2224%22 height=%2224%22 viewBox=%220 0 24 24%22 fill=%22none%22 stroke=%22currentColor%22 stroke-width=%222%22 stroke-linecap=%22round%22 stroke-linejoin=%22round%22><path d=%22m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z%22/><polyline points=%229 22 9 12 15 12 15 22%22/></svg>');"></div>
          首页
        </div>
        <div class="menu-item">
          <div class="menu-icon" style="mask-image: url('data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 width=%2224%22 height=%2224%22 viewBox=%220 0 24 24%22 fill=%22none%22 stroke=%22currentColor%22 stroke-width=%222%22 stroke-linecap=%22round%22 stroke-linejoin=%22round%22><path d=%22M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2%22></path><rect x=%228%22 y=%222%22 width=%228%22 height=%224%22 rx=%221%22 ry=%221%22></rect></svg>');"></div>
          待办事项
        </div>
        <div class="menu-title">项目</div>
        <div class="menu-item">
          <div class="menu-icon" style="mask-image: url('data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 width=%2224%22 height=%2224%22 viewBox=%220 0 24 24%22 fill=%22none%22 stroke=%22currentColor%22 stroke-width=%222%22 stroke-linecap=%22round%22 stroke-linejoin=%22round%22><path d=%22M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2 2V5a2 2 0 0 1 2-2h5l2 3h9a2 2 0 0 1 2 2z%22></path></svg>');"></div>
          项目管理
        </div>
        <div class="menu-item active">
          <div class="menu-icon" style="mask-image: url('data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 width=%2224%22 height=%2224%22 viewBox=%220 0 24 24%22 fill=%22none%22 stroke=%22currentColor%22 stroke-width=%222%22 stroke-linecap=%22round%22 stroke-linejoin=%22round%22><path d=%22M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z%22></path><polyline points=%2214 2 14 8 20 8%22></polyline><line x1=%2216%22 y1=%2213%22 x2=%228%22 y2=%2213%22></line><line x1=%2216%22 y1=%2217%22 x2=%228%22 y2=%2217%22></line><polyline points=%2210 9 9 9 8 9%22></polyline></svg>');"></div>
          需求管理
        </div>
        <div class="menu-item">
          <div class="menu-icon" style="mask-image: url('data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 width=%2224%22 height=%2224%22 viewBox=%220 0 24 24%22 fill=%22none%22 stroke=%22currentColor%22 stroke-width=%222%22 stroke-linecap=%22round%22 stroke-linejoin=%22round%22><polyline points=%2221 8 21 21 3 21 3 8%22></polyline><rect x=%221%22 y=%223%22 width=%2222%22 height=%225%22></rect><line x1=%2210%22 y1=%2212%22 x2=%2214%22 y2=%2212%22></line></svg>');"></div>
          合并请求
        </div>
        <div class="menu-title">测试</div>
        <div class="menu-item">
          <div class="menu-icon" style="mask-image: url('data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 width=%2224%22 height=%2224%22 viewBox=%220 0 24 24%22 fill=%22none%22 stroke=%22currentColor%22 stroke-width=%222%22 stroke-linecap=%22round%22 stroke-linejoin=%22round%22><path d=%22M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z%22></path><polyline points=%2214 2 14 8 20 8%22></polyline><path d=%22M16 13.58A2 2 0 0 0 15.3 12a2 2 0 0 0 .7-1.58A2 2 0 0 0 14 8.5h-6v8h6a2 2 0 0 0 2-1.92A2 2 0 0 0 16 13.58Z%22></path></svg>');"></div>
          测试用例
        </div>
        <div class="menu-title">系统</div>
        <div class="menu-item">
          <div class="menu-icon" style="mask-image: url('data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 width=%2224%22 height=%2224%22 viewBox=%220 0 24 24%22 fill=%22none%22 stroke=%22currentColor%22 stroke-width=%222%22 stroke-linecap=%22round%22 stroke-linejoin=%22round%22><path d=%22M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2%22></path><circle cx=%2212%22 cy=%227%22 r=%224%22></circle></svg>');"></div>
          用户管理
        </div>
      </div>
    </div>

    <!-- 主内容区 -->
    <div class="main">
      <!-- 页面头部 -->
      <div class="header">
        <div class="page-title">需求管理</div>
        <div class="user-info">
          <div class="avatar">管</div>
          <div class="username">管理员</div>
        </div>
      </div>

      <!-- 页面内容 -->
      <div class="content">
        <!-- 工具栏 -->
        <div class="toolbar">
          <div class="toolbar-left">
            <div class="stats-info">
              共 <span class="stats-number">24</span> 条需求
            </div>
          </div>
          <div>
            <button class="btn btn-primary" onclick="openBugReportModal()">
              <svg class="btn-icon" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="12" y1="5" x2="12" y2="19"></line><line x1="5" y1="12" x2="19" y2="12"></line></svg>
              上报漏洞
            </button>
          </div>
        </div>

        <!-- 搜索筛选区域 -->
        <div class="filter-card">
          <div class="filter-title">筛选条件</div>
          <div class="filter-form">
            <div class="filter-group">
              <label class="filter-label">需求类型</label>
              <select class="filter-control">
                <option value="">全部类型</option>
                <option value="Bug Fixed">修复漏洞</option>
                <option value="Hot Bug Fixed">紧急修复漏洞</option>
                <option value="New Feature">新功能</option>
              </select>
            </div>
            <div class="filter-group">
              <label class="filter-label">优先级</label>
              <select class="filter-control">
                <option value="">全部优先级</option>
                <option value="P0">P0</option>
                <option value="P1">P1</option>
                <option value="P2">P2</option>
              </select>
            </div>
            <div class="filter-group">
              <label class="filter-label">状态</label>
              <select class="filter-control">
                <option value="">全部状态</option>
                <option value="PENDING">待处理</option>
                <option value="DEVELOPING">开发中</option>
                <option value="TESTING">测试中</option>
                <option value="VALIDATING">验证中</option>
                <option value="COMPLETED">已完成</option>
              </select>
            </div>
            <div class="filter-group">
              <label class="filter-label">关键词</label>
              <input type="text" class="filter-control" placeholder="需求编号/名称">
            </div>
            <div class="filter-actions">
              <button class="btn btn-text" onclick="resetFilters()">重置</button>
              <button class="btn btn-primary" onclick="applyFilters()">搜索</button>
            </div>
          </div>
        </div>

        <!-- 需求卡片网格 -->
        <div class="requirements-grid">
          <!-- 需求卡片1 - 修复漏洞 -->
          <div class="requirement-card">
            <div class="card-header type-bug">
              <div class="requirement-id">B0219</div>
              <div class="project-name">用户中心</div>
            </div>
            <div class="card-body">
              <div class="requirement-title">指标预警功能存在问题</div>
              <div class="card-meta">
                <span class="priority-tag priority-p0">P0</span>
                <span class="badge badge-orange">修复漏洞</span>
                <span class="badge badge-purple">验证中</span>
              </div>
              <div class="card-time">
                <div class="time-item">
                  <svg class="time-icon" xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><polyline points="12 6 12 12 16 14"></polyline></svg>
                  开始：2025-05-22
                </div>
                <div class="time-item">
                  <svg class="time-icon" xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><polyline points="12 6 12 12 16 14"></polyline></svg>
                  截止：2025-06-21
                </div>
              </div>
              <div class="card-roles">
                <span class="role-tag role-developer">开发人员</span>
              </div>
              <div class="card-actions">
                <button class="action-btn action-view" onclick="viewRequirement()">查看</button>
                <button class="action-btn action-withdraw" onclick="withdrawValidation()">撤回验证</button>
              </div>
            </div>
          </div>

          <!-- 需求卡片2 - 新功能 -->
          <div class="requirement-card">
            <div class="card-header type-feature">
              <div class="requirement-id">F0132</div>
              <div class="project-name">消息服务</div>
            </div>
            <div class="card-body">
              <div class="requirement-title">新增消息推送模板管理功能</div>
              <div class="card-meta">
                <span class="priority-tag priority-p1">P1</span>
                <span class="badge badge-blue">新功能</span>
                <span class="badge badge-orange">测试中</span>
              </div>
              <div class="card-time">
                <div class="time-item">
                  <svg class="time-icon" xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><polyline points="12 6 12 12 16 14"></polyline></svg>
                  开始：2025-05-20
                </div>
                <div class="time-item">
                  <svg class="time-icon" xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><polyline points="12 6 12 12 16 14"></polyline></svg>
                  截止：2025-06-15
                </div>
              </div>
              <div class="card-roles">
                <span class="role-tag role-tester">测试人员</span>
              </div>
              <div class="card-actions">
                <button class="action-btn action-view" onclick="viewRequirement()">查看</button>
                <button class="action-btn action-approve" onclick="approveTest()">通过</button>
                <button class="action-btn action-reject" onclick="rejectTest()">驳回</button>
              </div>
            </div>
          </div>

          <!-- 需求卡片3 - 修复漏洞 -->
          <div class="requirement-card">
            <div class="card-header type-bug">
              <div class="requirement-id">B0118</div>
              <div class="project-name">支付系统</div>
            </div>
            <div class="card-body">
              <div class="requirement-title">支付接口超时处理优化</div>
              <div class="card-meta">
                <span class="priority-tag priority-p1">P1</span>
                <span class="badge badge-orange">修复漏洞</span>
                <span class="badge badge-blue">开发中</span>
              </div>
              <div class="card-time">
                <div class="time-item">
                  <svg class="time-icon" xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><polyline points="12 6 12 12 16 14"></polyline></svg>
                  开始：2025-05-18
                </div>
                <div class="time-item">
                  <svg class="time-icon" xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><polyline points="12 6 12 12 16 14"></polyline></svg>
                  截止：2025-06-10
                </div>
              </div>
              <div class="card-roles">
                <span class="role-tag role-developer">开发人员</span>
              </div>
              <div class="card-actions">
                <button class="action-btn action-view" onclick="viewRequirement()">查看</button>
                <button class="action-btn action-submit" onclick="submitToTest()">提交测试</button>
              </div>
            </div>
          </div>

          <!-- 需求卡片4 - 新功能 -->
          <div class="requirement-card">
            <div class="card-header type-feature">
              <div class="requirement-id">F0089</div>
              <div class="project-name">订单管理</div>
            </div>
            <div class="card-body">
              <div class="requirement-title">订单状态实时更新功能</div>
              <div class="card-meta">
                <span class="priority-tag priority-p2">P2</span>
                <span class="badge badge-blue">新功能</span>
                <span class="badge badge-gray">待处理</span>
              </div>
              <div class="card-time">
                <div class="time-item">
                  <svg class="time-icon" xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><polyline points="12 6 12 12 16 14"></polyline></svg>
                  开始：2025-05-25
                </div>
                <div class="time-item">
                  <svg class="time-icon" xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><polyline points="12 6 12 12 16 14"></polyline></svg>
                  截止：2025-06-30
                </div>
              </div>
              <div class="card-roles">
                <span class="role-tag role-developer">开发人员</span>
              </div>
              <div class="card-actions">
                <button class="action-btn action-view" onclick="viewRequirement()">查看</button>
                <button class="action-btn action-claim" onclick="claimRequirement()">认领</button>
              </div>
            </div>
          </div>

          <!-- 需求卡片5 - 新功能 -->
          <div class="requirement-card">
            <div class="card-header type-feature">
              <div class="requirement-id">F0067</div>
              <div class="project-name">商品管理</div>
            </div>
            <div class="card-body">
              <div class="requirement-title">商品批量导入功能优化</div>
              <div class="card-meta">
                <span class="priority-tag priority-p2">P2</span>
                <span class="badge badge-blue">新功能</span>
                <span class="badge badge-green">已完成</span>
              </div>
              <div class="card-time">
                <div class="time-item">
                  <svg class="time-icon" xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><polyline points="12 6 12 12 16 14"></polyline></svg>
                  开始：2025-05-10
                </div>
                <div class="time-item">
                  <svg class="time-icon" xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><polyline points="12 6 12 12 16 14"></polyline></svg>
                  截止：2025-05-30
                </div>
              </div>
              <div class="card-roles">
                <span class="role-tag role-developer">开发人员</span>
                <span class="role-tag role-tester">测试人员</span>
              </div>
              <div class="card-actions">
                <button class="action-btn action-view" onclick="viewRequirement()">查看</button>
              </div>
            </div>
          </div>

          <!-- 需求卡片6 - 紧急修复漏洞 -->
          <div class="requirement-card">
            <div class="card-header type-hotbug">
              <div class="requirement-id">H0001</div>
              <div class="project-name">支付系统</div>
            </div>
            <div class="card-body">
              <div class="requirement-title">紧急修复支付安全漏洞</div>
              <div class="card-meta">
                <span class="priority-tag priority-p0">P0</span>
                <span class="badge badge-red">紧急修复漏洞</span>
                <span class="badge badge-blue">开发中</span>
              </div>
              <div class="card-time">
                <div class="time-item">
                  <svg class="time-icon" xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><polyline points="12 6 12 12 16 14"></polyline></svg>
                  开始：2025-05-24
                </div>
                <div class="time-item">
                  <svg class="time-icon" xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><polyline points="12 6 12 12 16 14"></polyline></svg>
                  截止：2025-05-26
                </div>
              </div>
              <div class="card-roles">
                <span class="role-tag role-developer">开发人员</span>
              </div>
              <div class="card-actions">
                <button class="action-btn action-view" onclick="viewRequirement()">查看</button>
                <button class="action-btn action-submit" onclick="submitToTest()">提交测试</button>
              </div>
            </div>
          </div>
        </div>

        <!-- 分页 -->
        <div class="pagination-container">
          <div class="pagination-info">
            显示第 1-6 条，共 24 条记录
          </div>
          <div class="pagination-controls">
            <button class="pagination-btn disabled">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="15 18 9 12 15 6"></polyline></svg>
            </button>
            <button class="pagination-btn active">1</button>
            <button class="pagination-btn">2</button>
            <button class="pagination-btn">3</button>
            <button class="pagination-btn">4</button>
            <button class="pagination-btn">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="9 18 15 12 9 6"></polyline></svg>
            </button>
            <div class="page-size-selector">
              每页
              <select class="page-size-select">
                <option value="6">6</option>
                <option value="12">12</option>
                <option value="24">24</option>
                <option value="48">48</option>
              </select>
              条
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 驳回理由弹窗 -->
  <div class="modal-overlay" id="rejectModal">
    <div class="modal">
      <div class="modal-header">
        <div class="modal-title">驳回测试</div>
        <div class="modal-close" onclick="closeModal('rejectModal')">
          <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg>
        </div>
      </div>
      <div class="modal-body">
        <div class="form-group">
          <label class="form-label">驳回理由 *</label>
          <textarea class="form-control" placeholder="请输入驳回理由"></textarea>
        </div>
      </div>
      <div class="modal-footer">
        <button class="btn btn-text" onclick="closeModal('rejectModal')">取消</button>
        <button class="btn btn-primary" onclick="submitReject()">确定</button>
      </div>
    </div>
  </div>

  <!-- 上报漏洞弹窗 -->
  <div class="modal-overlay" id="bugReportModal">
    <div class="modal" style="width: 680px;">
      <div class="modal-header">
        <div class="modal-title">上报漏洞</div>
        <div class="modal-close" onclick="closeModal('bugReportModal')">
          <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg>
        </div>
      </div>
      <div class="modal-body">
        <div class="form-group">
          <label class="form-label">漏洞项目 *</label>
          <select class="form-control">
            <option value="">请选择项目</option>
            <option value="1">用户中心</option>
            <option value="2">消息服务</option>
            <option value="3">支付系统</option>
            <option value="4">订单管理</option>
            <option value="5">商品管理</option>
          </select>
        </div>
        <div class="form-group">
          <label class="form-label">漏洞标题 *</label>
          <input type="text" class="form-control" placeholder="请输入漏洞标题">
        </div>
        <div class="form-group">
          <label class="form-label">漏洞类型 *</label>
          <div style="display: flex; gap: 24px; margin-top: 8px;">
            <label style="display: flex; align-items: center; font-size: 14px; color: #666;">
              <input type="radio" name="bugType" value="Bug Fixed" checked style="margin-right: 8px;">
              修复漏洞
            </label>
            <label style="display: flex; align-items: center; font-size: 14px; color: #666;">
              <input type="radio" name="bugType" value="Hot Bug Fixed" style="margin-right: 8px;">
              紧急修复漏洞
            </label>
          </div>
        </div>
        <div class="form-group">
          <label class="form-label">漏洞内容 *</label>
          <textarea class="form-control" style="height: 120px;" placeholder="请详细描述漏洞问题，包括复现步骤、影响范围等"></textarea>
        </div>
        <div class="form-group">
          <label class="form-label">开发人员 *</label>
          <select class="form-control" multiple style="height: 80px;">
            <option value="1">黄绍铭</option>
            <option value="2">顾智聪</option>
            <option value="3">李佳辉</option>
            <option value="4">焦梓航</option>
          </select>
        </div>
        <div style="display: flex; gap: 16px;">
          <div class="form-group" style="flex: 1;">
            <label class="form-label">主目标分支 *</label>
            <select class="form-control">
              <option value="">请选择主目标分支</option>
              <option value="dev/drc-bank">dev/drc-bank</option>
              <option value="dev/main">dev/main</option>
              <option value="test">test</option>
            </select>
          </div>
          <div class="form-group" style="flex: 1;">
            <label class="form-label">其他目标分支</label>
            <select class="form-control" multiple style="height: 40px;">
              <option value="dev/main">dev/main</option>
              <option value="test">test</option>
              <option value="prod">prod</option>
            </select>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button class="btn btn-text" onclick="closeModal('bugReportModal')">取消</button>
        <button class="btn btn-primary" onclick="submitBugReport()">提交</button>
      </div>
    </div>
  </div>

  <script>
    // 弹窗控制
    function openModal(modalId) {
      document.getElementById(modalId).style.display = 'flex';
    }

    function closeModal(modalId) {
      document.getElementById(modalId).style.display = 'none';
    }

    // 具体功能按钮
    function openBugReportModal() {
      openModal('bugReportModal');
    }

    function viewRequirement() {
      alert('查看需求详情');
    }

    function claimRequirement() {
      if (confirm('确定要认领该需求吗？')) {
        alert('需求已认领，状态已更新为开发中');
      }
    }

    function submitToTest() {
      if (confirm('确定要将该需求提交测试吗？')) {
        alert('需求状态已更新为测试中');
      }
    }

    function approveTest() {
      if (confirm('确定要通过测试吗？')) {
        alert('需求状态已更新为验证中');
      }
    }

    function rejectTest() {
      openModal('rejectModal');
    }

    function withdrawValidation() {
      if (confirm('确定要撤回验证吗？')) {
        alert('需求已撤回，状态已更新为测试中');
      }
    }

    function submitReject() {
      alert('需求已驳回，状态已更新为开发中');
      closeModal('rejectModal');
    }

    function submitBugReport() {
      alert('漏洞报告提交成功');
      closeModal('bugReportModal');
    }

    function applyFilters() {
      alert('执行搜索操作');
    }

    function resetFilters() {
      // 重置所有筛选条件
      const selects = document.querySelectorAll('.filter-control');
      selects.forEach(select => {
        if (select.tagName === 'SELECT') {
          select.selectedIndex = 0;
        } else {
          select.value = '';
        }
      });
    }

    // 点击遮罩层关闭弹窗
    document.addEventListener('click', function(e) {
      if (e.target.classList.contains('modal-overlay')) {
        e.target.style.display = 'none';
      }
    });
  </script>
</body>
</html> 