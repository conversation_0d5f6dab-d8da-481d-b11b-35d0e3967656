<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <title>项目管理系统 - 用户管理</title>
  <style>
    * { margin: 0; padding: 0; box-sizing: border-box; }
    body { 
      font-family: "Microsoft YaHei", -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Arial, sans-serif; 
      background-color: #f5f7fa; 
      color: #222;
      min-height: 100vh;
    }
    .layout {
      display: flex;
      min-height: 100vh;
    }
    .sidebar {
      width: 220px;
      background-color: #fff;
      border-right: 1px solid #e5e6eb;
      box-shadow: 2px 0 8px rgba(0,0,0,0.03);
      position: fixed;
      height: 100vh;
      z-index: 100;
    }
    .sidebar-header {
      height: 60px;
      border-bottom: 1px solid #e5e6eb;
      display: flex;
      align-items: center;
      padding: 0 20px;
    }
    .logo {
      font-size: 18px;
      font-weight: bold;
      color: #1877f2;
    }
    .menu {
      padding: 20px 0;
    }
    .menu-title {
      font-size: 13px;
      color: #999;
      padding: 0 20px;
      margin-bottom: 8px;
    }
    .menu-item {
      height: 46px;
      display: flex;
      align-items: center;
      padding: 0 20px;
      color: #666;
      font-size: 15px;
      margin-bottom: 2px;
      cursor: pointer;
      position: relative;
      transition: all 0.3s;
    }
    .menu-item:hover {
      color: #1877f2;
      background-color: rgba(24, 119, 242, 0.04);
    }
    .menu-item.active {
      color: #1877f2;
      background-color: rgba(24, 119, 242, 0.08);
      font-weight: 500;
    }
    .menu-item.active::before {
      content: '';
      position: absolute;
      left: 0;
      top: 12px;
      bottom: 12px;
      width: 3px;
      background-color: #1877f2;
      border-radius: 0 2px 2px 0;
    }
    .menu-icon {
      width: 20px;
      height: 20px;
      margin-right: 8px;
      background-color: currentColor;
      opacity: 0.6;
      mask-size: cover;
      -webkit-mask-size: cover;
    }
    .main {
      flex: 1;
      margin-left: 220px;
    }
    .header {
      height: 60px;
      background-color: #fff;
      border-bottom: 1px solid #e5e6eb;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 24px;
      position: sticky;
      top: 0;
      z-index: 99;
    }
    .page-title {
      font-size: 18px;
      font-weight: 500;
      color: #222;
    }
    .user-info {
      display: flex;
      align-items: center;
    }
    .avatar {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      background-color: #1877f2;
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 500;
      margin-right: 8px;
    }
    .username {
      font-size: 14px;
      color: #666;
    }
    .content {
      padding: 24px;
    }
    .toolbar {
      display: flex;
      justify-content: space-between;
      margin-bottom: 24px;
    }
    .toolbar-left {
      display: flex;
      gap: 12px;
    }
    .search-box {
      width: 260px;
      height: 38px;
      border: 1px solid #e5e6eb;
      border-radius: 6px;
      padding: 0 16px;
      display: flex;
      align-items: center;
    }
    .search-input {
      flex: 1;
      height: 100%;
      border: none;
      outline: none;
      background: transparent;
      font-size: 14px;
      color: #222;
    }
    .search-icon {
      width: 16px;
      height: 16px;
      margin-right: 8px;
      opacity: 0.5;
    }
    .btn {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      height: 38px;
      padding: 0 16px;
      border-radius: 6px;
      font-size: 15px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s;
      border: none;
    }
    .btn-primary {
      background-color: #1877f2;
      color: white;
    }
    .btn-primary:hover {
      background-color: #0d6efd;
    }
    .btn-outline {
      background-color: transparent;
      color: #666;
      border: 1px solid #e5e6eb;
    }
    .btn-outline:hover {
      border-color: #1877f2;
      color: #1877f2;
    }
    .btn-icon {
      width: 16px;
      height: 16px;
      margin-right: 6px;
    }
    .tabs {
      display: flex;
      border-bottom: 1px solid #e5e6eb;
      margin-bottom: 24px;
    }
    .tab {
      padding: 0 20px;
      height: 40px;
      display: flex;
      align-items: center;
      font-size: 15px;
      color: #666;
      position: relative;
      cursor: pointer;
    }
    .tab.active {
      color: #1877f2;
      font-weight: 500;
    }
    .tab.active::after {
      content: '';
      position: absolute;
      left: 20px;
      right: 20px;
      bottom: -1px;
      height: 2px;
      background-color: #1877f2;
      border-radius: 2px 2px 0 0;
    }
    .tab-badge {
      min-width: 18px;
      height: 18px;
      background-color: #1877f2;
      border-radius: 9px;
      color: white;
      font-size: 12px;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      padding: 0 6px;
      margin-left: 8px;
    }
    .select {
      height: 38px;
      min-width: 120px;
      border: 1px solid #e5e6eb;
      border-radius: 6px;
      padding: 0 12px;
      font-size: 14px;
      color: #222;
      background-color: white;
      appearance: none;
      background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23999' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
      background-repeat: no-repeat;
      background-position: right 12px center;
      background-size: 16px;
      padding-right: 36px;
    }
    .select:focus {
      outline: none;
      border-color: #1877f2;
    }
    .table-card {
      background-color: #fff;
      border-radius: 10px;
      box-shadow: 0 2px 12px rgba(0,0,0,0.04);
      overflow: hidden;
      margin-bottom: 24px;
    }
    .table {
      width: 100%;
      border-collapse: collapse;
    }
    .table th, .table td {
      padding: 16px 24px;
      text-align: left;
      border-bottom: 1px solid #e5e6eb;
    }
    .table th {
      font-weight: 500;
      color: #666;
      background-color: #fafafa;
    }
    .table td {
      color: #222;
    }
    .table tr:last-child td {
      border-bottom: none;
    }
    .table tr:hover {
      background-color: #f5f7fa;
    }
    .badge {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      height: 24px;
      padding: 0 10px;
      border-radius: 12px;
      font-size: 13px;
      font-weight: 500;
    }
    .badge-blue {
      background-color: rgba(24, 119, 242, 0.1);
      color: #1877f2;
    }
    .badge-green {
      background-color: rgba(82, 196, 26, 0.1);
      color: #52c41a;
    }
    .badge-orange {
      background-color: rgba(245, 166, 35, 0.1);
      color: #f5a623;
    }
    .badge-red {
      background-color: rgba(255, 77, 79, 0.1);
      color: #ff4d4f;
    }
    .badge-gray {
      background-color: rgba(153, 153, 153, 0.1);
      color: #999;
    }
    .pagination {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: 32px;
    }
    .page-item {
      width: 36px;
      height: 36px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 4px;
      border-radius: 6px;
      font-size: 14px;
      color: #666;
      cursor: pointer;
      transition: all 0.3s;
    }
    .page-item:hover {
      background-color: rgba(0,0,0,0.04);
      color: #1877f2;
    }
    .page-item.active {
      background-color: #1877f2;
      color: white;
    }
    .table-actions {
      display: flex;
      gap: 8px;
    }
    .user-avatar {
      width: 36px;
      height: 36px;
      border-radius: 50%;
      background-color: #1877f2;
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 500;
      font-size: 16px;
    }
    .user-avatar.admin {
      background-color: #ff4d4f;
    }
    .user-avatar.developer {
      background-color: #52c41a;
    }
    .user-avatar.tester {
      background-color: #f5a623;
    }
    .user-status {
      display: inline-flex;
      align-items: center;
    }
    .status-dot {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      margin-right: 6px;
    }
    .status-dot.active {
      background-color: #52c41a;
    }
    .status-dot.inactive {
      background-color: #ff4d4f;
    }
    .user-cell {
      display: flex;
      align-items: center;
      gap: 12px;
    }
    .user-name {
      font-weight: 500;
      margin-bottom: 2px;
    }
    .user-email {
      font-size: 13px;
      color: #999;
    }
    .switch {
      position: relative;
      display: inline-block;
      width: 36px;
      height: 20px;
    }
    .switch input {
      opacity: 0;
      width: 0;
      height: 0;
    }
    .slider {
      position: absolute;
      cursor: pointer;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: #ccc;
      transition: .3s;
      border-radius: 10px;
    }
    .slider:before {
      position: absolute;
      content: "";
      height: 16px;
      width: 16px;
      left: 2px;
      bottom: 2px;
      background-color: white;
      transition: .3s;
      border-radius: 50%;
    }
    input:checked + .slider {
      background-color: #1877f2;
    }
    input:checked + .slider:before {
      transform: translateX(16px);
    }
  </style>
</head>
<body>
  <div class="layout">
    <div class="sidebar">
      <div class="sidebar-header">
        <div class="logo">项目管理系统</div>
      </div>
      <div class="menu">
        <div class="menu-title">通用</div>
        <div class="menu-item">
          <div class="menu-icon" style="mask-image: url('data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 width=%2224%22 height=%2224%22 viewBox=%220 0 24 24%22 fill=%22none%22 stroke=%22currentColor%22 stroke-width=%222%22 stroke-linecap=%22round%22 stroke-linejoin=%22round%22><path d=%22m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z%22/><polyline points=%229 22 9 12 15 12 15 22%22/></svg>');"></div>
          首页
        </div>
        <div class="menu-item">
          <div class="menu-icon" style="mask-image: url('data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 width=%2224%22 height=%2224%22 viewBox=%220 0 24 24%22 fill=%22none%22 stroke=%22currentColor%22 stroke-width=%222%22 stroke-linecap=%22round%22 stroke-linejoin=%22round%22><path d=%22M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2%22></path><rect x=%228%22 y=%222%22 width=%228%22 height=%224%22 rx=%221%22 ry=%221%22></rect></svg>');"></div>
          待办事项
        </div>
        <div class="menu-title">项目</div>
        <div class="menu-item">
          <div class="menu-icon" style="mask-image: url('data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 width=%2224%22 height=%2224%22 viewBox=%220 0 24 24%22 fill=%22none%22 stroke=%22currentColor%22 stroke-width=%222%22 stroke-linecap=%22round%22 stroke-linejoin=%22round%22><path d=%22M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5l2 3h9a2 2 0 0 1 2 2z%22></path></svg>');"></div>
          项目管理
        </div>
        <div class="menu-item">
          <div class="menu-icon" style="mask-image: url('data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 width=%2224%22 height=%2224%22 viewBox=%220 0 24 24%22 fill=%22none%22 stroke=%22currentColor%22 stroke-width=%222%22 stroke-linecap=%22round%22 stroke-linejoin=%22round%22><path d=%22M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z%22></path><polyline points=%2214 2 14 8 20 8%22></polyline><line x1=%2216%22 y1=%2213%22 x2=%228%22 y2=%2213%22></line><line x1=%2216%22 y1=%2217%22 x2=%228%22 y2=%2217%22></line><polyline points=%2210 9 9 9 8 9%22></polyline></svg>');"></div>
          需求管理
        </div>
        <div class="menu-item">
          <div class="menu-icon" style="mask-image: url('data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 width=%2224%22 height=%2224%22 viewBox=%220 0 24 24%22 fill=%22none%22 stroke=%22currentColor%22 stroke-width=%222%22 stroke-linecap=%22round%22 stroke-linejoin=%22round%22><polyline points=%2221 8 21 21 3 21 3 8%22></polyline><rect x=%221%22 y=%223%22 width=%2222%22 height=%225%22></rect><line x1=%2210%22 y1=%2212%22 x2=%2214%22 y2=%2212%22></line></svg>');"></div>
          合并请求
        </div>
        <div class="menu-title">测试</div>
        <div class="menu-item">
          <div class="menu-icon" style="mask-image: url('data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 width=%2224%22 height=%2224%22 viewBox=%220 0 24 24%22 fill=%22none%22 stroke=%22currentColor%22 stroke-width=%222%22 stroke-linecap=%22round%22 stroke-linejoin=%22round%22><path d=%22M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z%22></path><polyline points=%2214 2 14 8 20 8%22></polyline><path d=%22M16 13.58A2 2 0 0 0 15.3 12a2 2 0 0 0 .7-1.58A2 2 0 0 0 14 8.5h-6v8h6a2 2 0 0 0 2-1.92A2 2 0 0 0 16 13.58Z%22></path></svg>');"></div>
          测试用例
        </div>
        <div class="menu-title">系统</div>
        <div class="menu-item active">
          <div class="menu-icon" style="mask-image: url('data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 width=%2224%22 height=%2224%22 viewBox=%220 0 24 24%22 fill=%22none%22 stroke=%22currentColor%22 stroke-width=%222%22 stroke-linecap=%22round%22 stroke-linejoin=%22round%22><path d=%22M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2%22></path><circle cx=%2212%22 cy=%227%22 r=%224%22></circle></svg>');"></div>
          用户管理
        </div>
      </div>
    </div>
    <div class="main">
      <div class="header">
        <div class="page-title">用户管理</div>
        <div class="user-info">
          <div class="avatar">管</div>
          <div class="username">管理员</div>
        </div>
      </div>
      <div class="content">
        <div class="tabs">
          <div class="tab active">所有用户</div>
          <div class="tab">管理员</div>
          <div class="tab">开发人员</div>
          <div class="tab">测试人员</div>
          <div class="tab">项目管理</div>
        </div>
        
        <div class="toolbar">
          <div class="toolbar-left">
            <div class="search-box">
              <svg class="search-icon" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="11" cy="11" r="8"></circle><line x1="21" y1="21" x2="16.65" y2="16.65"></line></svg>
              <input type="text" class="search-input" placeholder="搜索用户">
            </div>
            <select class="select">
              <option>所有状态</option>
              <option>启用</option>
              <option>禁用</option>
            </select>
          </div>
          <button class="btn btn-primary">
            <span class="btn-icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="12" y1="5" x2="12" y2="19"></line><line x1="5" y1="12" x2="19" y2="12"></line></svg>
            </span>
            添加用户
          </button>
        </div>
        
        <div class="table-card">
          <table class="table">
            <thead>
              <tr>
                <th style="width: 250px;">用户信息</th>
                <th>角色</th>
                <th>企业微信ID</th>
                <th>创建时间</th>
                <th>状态</th>
                <th style="width: 180px;">操作</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>
                  <div class="user-cell">
                    <div class="user-avatar admin">管</div>
                    <div>
                      <div class="user-name">系统管理员</div>
                    </div>
                  </div>
                </td>
                <td>
                  <span class="badge badge-red">管理员</span>
                </td>
                <td>admin_qywx</td>
                <td>2023-01-01</td>
                <td>
                  <div class="user-status">
                    <span class="status-dot active"></span>
                    <span>启用</span>
                  </div>
                </td>
                <td>
                  <div class="table-actions">
                    <button class="btn btn-outline">编辑</button>
                    <label class="switch">
                      <input type="checkbox" checked>
                      <span class="slider"></span>
                    </label>
                  </div>
                </td>
              </tr>
              <tr>
                <td>
                  <div class="user-cell">
                    <div class="user-avatar developer">张</div>
                    <div>
                      <div class="user-name">张三</div>
                    </div>
                  </div>
                </td>
                <td>
                  <span class="badge badge-blue">开发人员</span>
                  <span class="badge badge-orange">测试人员</span>
                </td>
                <td>zhangsan_qywx</td>
                <td>2023-02-15</td>
                <td>
                  <div class="user-status">
                    <span class="status-dot active"></span>
                    <span>启用</span>
                  </div>
                </td>
                <td>
                  <div class="table-actions">
                    <button class="btn btn-outline">编辑</button>
                    <label class="switch">
                      <input type="checkbox" checked>
                      <span class="slider"></span>
                    </label>
                  </div>
                </td>
              </tr>
              <tr>
                <td>
                  <div class="user-cell">
                    <div class="user-avatar developer">李</div>
                    <div>
                      <div class="user-name">李四</div>
                    </div>
                  </div>
                </td>
                <td><span class="badge badge-blue">开发人员</span></td>
                <td>lisi_qywx</td>
                <td>2023-03-05</td>
                <td>
                  <div class="user-status">
                    <span class="status-dot active"></span>
                    <span>启用</span>
                  </div>
                </td>
                <td>
                  <div class="table-actions">
                    <button class="btn btn-outline">编辑</button>
                    <label class="switch">
                      <input type="checkbox" checked>
                      <span class="slider"></span>
                    </label>
                  </div>
                </td>
              </tr>
              <tr>
                <td>
                  <div class="user-cell">
                    <div class="user-avatar tester">王</div>
                    <div>
                      <div class="user-name">王五</div>
                    </div>
                  </div>
                </td>
                <td>
                  <span class="badge badge-orange">测试人员</span>
                  <span class="badge badge-blue">项目管理</span>
                </td>
                <td>wangwu_qywx</td>
                <td>2023-03-15</td>
                <td>
                  <div class="user-status">
                    <span class="status-dot active"></span>
                    <span>启用</span>
                  </div>
                </td>
                <td>
                  <div class="table-actions">
                    <button class="btn btn-outline">编辑</button>
                    <label class="switch">
                      <input type="checkbox" checked>
                      <span class="slider"></span>
                    </label>
                  </div>
                </td>
              </tr>
              <tr>
                <td>
                  <div class="user-cell">
                    <div class="user-avatar tester">赵</div>
                    <div>
                      <div class="user-name">赵六</div>
                    </div>
                  </div>
                </td>
                <td><span class="badge badge-orange">测试人员</span></td>
                <td>zhaoliu_qywx</td>
                <td>2023-04-10</td>
                <td>
                  <div class="user-status">
                    <span class="status-dot active"></span>
                    <span>启用</span>
                  </div>
                </td>
                <td>
                  <div class="table-actions">
                    <button class="btn btn-outline">编辑</button>
                    <label class="switch">
                      <input type="checkbox" checked>
                      <span class="slider"></span>
                    </label>
                  </div>
                </td>
              </tr>
              <tr>
                <td>
                  <div class="user-cell">
                    <div class="user-avatar developer">钱</div>
                    <div>
                      <div class="user-name">钱七</div>
                    </div>
                  </div>
                </td>
                <td><span class="badge badge-blue">开发人员</span></td>
                <td>qianqi_qywx</td>
                <td>2023-05-20</td>
                <td>
                  <div class="user-status">
                    <span class="status-dot inactive"></span>
                    <span>已禁用</span>
                  </div>
                </td>
                <td>
                  <div class="table-actions">
                    <button class="btn btn-outline">编辑</button>
                    <label class="switch">
                      <input type="checkbox">
                      <span class="slider"></span>
                    </label>
                  </div>
                </td>
              </tr>
              <tr>
                <td>
                  <div class="user-cell">
                    <div class="user-avatar admin">孙</div>
                    <div>
                      <div class="user-name">孙八</div>
                    </div>
                  </div>
                </td>
                <td>
                  <span class="badge badge-red">管理员</span>
                  <span class="badge badge-blue">项目管理</span>
                </td>
                <td>sunba_qywx</td>
                <td>2023-06-01</td>
                <td>
                  <div class="user-status">
                    <span class="status-dot active"></span>
                    <span>启用</span>
                  </div>
                </td>
                <td>
                  <div class="table-actions">
                    <button class="btn btn-outline">编辑</button>
                    <label class="switch">
                      <input type="checkbox" checked>
                      <span class="slider"></span>
                    </label>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
        
        <div class="pagination">
          <div class="page-item">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="15 18 9 12 15 6"></polyline></svg>
          </div>
          <div class="page-item active">1</div>
          <div class="page-item">2</div>
          <div class="page-item">3</div>
          <div class="page-item">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="9 18 15 12 9 6"></polyline></svg>
          </div>
        </div>
      </div>
    </div>
  </div>
</body>
</html> 