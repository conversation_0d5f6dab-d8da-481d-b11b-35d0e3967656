# Context
Filename: 时区问题分析.md
Created On: 2024-12-19
Created By: AI Assistant
Associated Protocol: RIPER-5 + Multidimensional + Agent Protocol

# Task Description
系统中现在写入以及读取数据库中的时间字段的时候，都是0时区的数据，导致页面显示的不太对。我希望连接数据库的时候，读取出来的时间都是中国时区的时间

# Project Overview
这是一个使用Python FastAPI作为后端、Vue.js作为前端的需求管理系统，采用SQLite数据库存储数据。系统涉及用户、项目、需求、测试用例等业务模块，所有时间字段都需要正确处理中国时区。

---
*The following sections are maintained by the AI during protocol execution*
---

# Analysis (Populated by RESEARCH mode)

## 当前时间处理现状

### 后端时间处理：
1. **数据库模型层面**：
   - 在 `api/app/models/requirement.py` 中使用了 `get_utc_now()` 函数，该函数返回 `datetime.now(timezone.utc)`，即UTC时间
   - 在 `api/app/models/user.py` 中使用了 `datetime.utcnow`，也是UTC时间
   - 在 `api/app/models/project.py` 中同样使用了 `datetime.utcnow`
   - 在关联表 `associations.py` 中也使用了 `datetime.utcnow`

2. **数据库连接配置**：
   - 使用SQLite数据库：`"sqlite:///site.db"`
   - 没有设置时区相关的连接参数

3. **API响应**：
   - 在schemas中，datetime字段直接返回数据库中的UTC时间
   - 没有进行时区转换处理

### 前端时间显示：
1. **时间格式化工具**：
   - `ui/src/utils/dateUtils.js` 中的函数只是简单的字符串格式化
   - 没有进行时区转换，直接显示后端返回的UTC时间

2. **显示组件**：
   - 各个Vue组件中使用 `formatToDateTime` 等函数显示时间
   - 用户看到的时间比实际北京时间慢8小时

## 问题根本原因

**核心问题**：系统全程使用UTC时间存储和显示，没有进行中国时区（Asia/Shanghai, UTC+8）的转换处理。

**具体表现**：
1. 数据库存储的是UTC时间
2. API返回的是UTC时间  
3. 前端显示的也是UTC时间
4. 用户期望看到的是北京时间（UTC+8）

## 关键文件清单

### 需要修改的后端文件：
- `api/app/models/requirement.py` - 需求模型的时间字段
- `api/app/models/user.py` - 用户模型的时间字段
- `api/app/models/project.py` - 项目模型的时间字段
- `api/app/models/test_case.py` - 测试用例模型的时间字段
- `api/app/models/test_module.py` - 测试模块模型的时间字段
- `api/app/models/associations.py` - 关联表的时间字段
- `api/app/core/database.py` - 数据库连接配置

### 需要修改的前端文件：
- `ui/src/utils/dateUtils.js` - 时间格式化工具函数

# Proposed Solution (Populated by INNOVATE mode)

## 解决方案对比分析

### 方案一：后端全局时区转换（推荐方案）
**核心思路**：在后端层面统一处理时区转换，数据库继续存储UTC时间，但在API响应时转换为中国时区。

**优势**：
- 数据库存储保持UTC标准，便于国际化扩展
- 前端无需关心时区处理，简化前端逻辑
- 集中处理，易于维护和调试
- 对现有数据无影响

**技术实现**：
- 创建统一的时区转换工具函数
- 在Pydantic schemas中增加时区转换逻辑
- 修改所有datetime字段的序列化方式

### 方案二：数据库存储时区修改
**核心思路**：修改所有模型的默认时间函数，直接存储中国时区时间。
**评估结果**：不推荐 - 需要数据迁移，违背存储最佳实践

### 方案三：前端时区转换
**核心思路**：后端继续返回UTC时间，在前端进行时区转换。
**评估结果**：不推荐 - 增加前端复杂度，处理分散

## 最终选择方案

**选择方案一：后端全局时区转换**

**技术架构**：
1. 数据库继续存储UTC时间（保持数据完整性）
2. 创建时区转换工具模块
3. 在Pydantic response schemas中自动转换为中国时区
4. 前端保持现有显示逻辑

**关键优势**：
- 最小化代码改动
- 保持数据完整性
- 集中化时区管理
- 易于未来扩展多时区支持

# Implementation Plan (Generated by PLAN mode)

## 详细实施计划

### 阶段一：创建时区转换工具
**文件**: `api/app/utils/timezone_utils.py` (新建)
**功能规格**:
- 创建`convert_utc_to_china_timezone(utc_datetime)` - UTC转中国时区函数
- 创建`get_china_now()` - 获取中国当前时间函数
- 定义`CHINA_TIMEZONE`常量 - 中国时区对象
- 处理None值边界情况

### 阶段二：创建基础Schema类
**文件**: `api/app/schemas/base.py` (新建)
**功能规格**:
- 定义`TimezoneAwareBaseModel`基础类
- 集成自动时区转换validator
- 提供统一的datetime字段处理

### 阶段三：修改Response Schemas
**文件变更列表**:
- `api/app/schemas/requirement.py` - 继承基础类，添加时区转换
- `api/app/schemas/user.py` - 继承基础类，添加时区转换
- `api/app/schemas/project.py` - 继承基础类，添加时区转换
- `api/app/schemas/test_case.py` - 继承基础类，添加时区转换
- `api/app/schemas/test_module.py` - 继承基础类，添加时区转换

### 阶段四：集成与测试
**验证步骤**:
- 启动API服务器测试
- 验证各API端点时间返回值
- 确认前端时间显示正确性

## Implementation Checklist:
1. 创建时区转换工具模块 `api/app/utils/timezone_utils.py`
2. 创建基础Schema类 `api/app/schemas/base.py`
3. 修改需求相关schemas `api/app/schemas/requirement.py`
4. 修改用户相关schemas `api/app/schemas/user.py`
5. 修改项目相关schemas `api/app/schemas/project.py`
6. 修改测试用例相关schemas `api/app/schemas/test_case.py`
7. 修改测试模块相关schemas `api/app/schemas/test_module.py`
8. 在utils目录的__init__.py中导入时区工具
9. 测试API端点的时间返回值
10. 验证前端时间显示效果 