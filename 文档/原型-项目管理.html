<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <title>项目管理系统 - 项目管理</title>
  <style>
    * { margin: 0; padding: 0; box-sizing: border-box; }
    body { 
      font-family: "Microsoft YaHei", -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Arial, sans-serif; 
      background-color: #f5f7fa; 
      color: #222;
      min-height: 100vh;
    }
    .layout {
      display: flex;
      min-height: 100vh;
    }
    .sidebar {
      width: 220px;
      background-color: #fff;
      border-right: 1px solid #e5e6eb;
      box-shadow: 2px 0 8px rgba(0,0,0,0.03);
      position: fixed;
      height: 100vh;
      z-index: 100;
    }
    .sidebar-header {
      height: 60px;
      border-bottom: 1px solid #e5e6eb;
      display: flex;
      align-items: center;
      padding: 0 20px;
    }
    .logo {
      font-size: 18px;
      font-weight: bold;
      color: #1877f2;
    }
    .menu {
      padding: 20px 0;
    }
    .menu-title {
      font-size: 13px;
      color: #999;
      padding: 0 20px;
      margin-bottom: 8px;
    }
    .menu-item {
      height: 46px;
      display: flex;
      align-items: center;
      padding: 0 20px;
      color: #666;
      font-size: 15px;
      margin-bottom: 2px;
      cursor: pointer;
      position: relative;
      transition: all 0.3s;
    }
    .menu-item:hover {
      color: #1877f2;
      background-color: rgba(24, 119, 242, 0.04);
    }
    .menu-item.active {
      color: #1877f2;
      background-color: rgba(24, 119, 242, 0.08);
      font-weight: 500;
    }
    .menu-item.active::before {
      content: '';
      position: absolute;
      left: 0;
      top: 12px;
      bottom: 12px;
      width: 3px;
      background-color: #1877f2;
      border-radius: 0 2px 2px 0;
    }
    .menu-icon {
      width: 20px;
      height: 20px;
      margin-right: 8px;
      background-color: currentColor;
      opacity: 0.6;
      mask-size: cover;
      -webkit-mask-size: cover;
    }
    .main {
      flex: 1;
      margin-left: 220px;
    }
    .header {
      height: 60px;
      background-color: #fff;
      border-bottom: 1px solid #e5e6eb;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 24px;
      position: sticky;
      top: 0;
      z-index: 99;
    }
    .page-title {
      font-size: 18px;
      font-weight: 500;
      color: #222;
    }
    .user-info {
      display: flex;
      align-items: center;
    }
    .avatar {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      background-color: #1877f2;
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 500;
      margin-right: 8px;
    }
    .username {
      font-size: 14px;
      color: #666;
    }
    .content {
      padding: 24px;
    }
    .toolbar {
      display: flex;
      justify-content: space-between;
      margin-bottom: 24px;
    }
    .toolbar-left {
      display: flex;
      gap: 12px;
    }
    .search-box {
      width: 260px;
      height: 38px;
      border: 1px solid #e5e6eb;
      border-radius: 6px;
      padding: 0 16px;
      display: flex;
      align-items: center;
    }
    .search-input {
      flex: 1;
      height: 100%;
      border: none;
      outline: none;
      background: transparent;
      font-size: 14px;
      color: #222;
    }
    .search-icon {
      width: 16px;
      height: 16px;
      margin-right: 8px;
      opacity: 0.5;
    }
    .btn {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      height: 38px;
      padding: 0 16px;
      border-radius: 6px;
      font-size: 15px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s;
      border: none;
    }
    .btn-primary {
      background-color: #1877f2;
      color: white;
    }
    .btn-primary:hover {
      background-color: #0d6efd;
    }
    .btn-outline {
      background-color: transparent;
      color: #666;
      border: 1px solid #e5e6eb;
    }
    .btn-outline:hover {
      border-color: #1877f2;
      color: #1877f2;
    }
    .btn-icon {
      width: 16px;
      height: 16px;
      margin-right: 6px;
    }
    .project-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
      gap: 24px;
    }
    .project-card {
      background-color: #fff;
      border-radius: 10px;
      box-shadow: 0 2px 12px rgba(0,0,0,0.04);
      overflow: hidden;
      transition: all 0.3s;
      cursor: pointer;
    }
    .project-card:hover {
      transform: translateY(-4px);
      box-shadow: 0 6px 20px rgba(0,0,0,0.08);
    }
    .project-header {
      height: 120px;
      background: linear-gradient(135deg, #1877f2, #36cfc9);
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
    }
    .project-logo {
      width: 60px;
      height: 60px;
      border-radius: 12px;
      background-color: rgba(255, 255, 255, 0.2);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
      font-weight: bold;
    }
    .project-body {
      padding: 20px;
    }
    .project-title {
      font-size: 18px;
      font-weight: 500;
      color: #222;
      margin-bottom: 8px;
    }
    .project-desc {
      font-size: 14px;
      color: #666;
      margin-bottom: 16px;
      line-height: 1.5;
      height: 42px;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
    }
    .project-meta {
      display: flex;
      justify-content: space-between;
      font-size: 13px;
      color: #999;
    }
    .meta-item {
      display: flex;
      align-items: center;
    }
    .meta-icon {
      width: 16px;
      height: 16px;
      margin-right: 4px;
      opacity: 0.6;
    }
    .badge {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      height: 24px;
      padding: 0 10px;
      border-radius: 12px;
      font-size: 13px;
      font-weight: 500;
    }
    .badge-blue {
      background-color: rgba(24, 119, 242, 0.1);
      color: #1877f2;
    }
    .badge-green {
      background-color: rgba(82, 196, 26, 0.1);
      color: #52c41a;
    }
    .badge-orange {
      background-color: rgba(245, 166, 35, 0.1);
      color: #f5a623;
    }
    .badge-red {
      background-color: rgba(255, 77, 79, 0.1);
      color: #ff4d4f;
    }
    .project-footer {
      padding: 16px 20px;
      border-top: 1px solid #e5e6eb;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .member-list {
      display: flex;
    }
    .member-avatar {
      width: 28px;
      height: 28px;
      border-radius: 50%;
      background-color: #1877f2;
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 13px;
      font-weight: 500;
      margin-right: -8px;
      border: 2px solid white;
    }
    .more-btn {
      width: 28px;
      height: 28px;
      border-radius: 50%;
      background-color: #f5f7fa;
      border: 2px solid white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 13px;
      color: #666;
    }
    .tabs {
      display: flex;
      border-bottom: 1px solid #e5e6eb;
      margin-bottom: 24px;
    }
    .tab {
      padding: 0 20px;
      height: 40px;
      display: flex;
      align-items: center;
      font-size: 15px;
      color: #666;
      position: relative;
      cursor: pointer;
    }
    .tab.active {
      color: #1877f2;
      font-weight: 500;
    }
    .tab.active::after {
      content: '';
      position: absolute;
      left: 20px;
      right: 20px;
      bottom: -1px;
      height: 2px;
      background-color: #1877f2;
      border-radius: 2px 2px 0 0;
    }
    .tab-badge {
      min-width: 18px;
      height: 18px;
      background-color: #1877f2;
      border-radius: 9px;
      color: white;
      font-size: 12px;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      padding: 0 6px;
      margin-left: 8px;
    }
    .empty-state {
      padding: 60px 0;
      text-align: center;
    }
    .empty-icon {
      width: 64px;
      height: 64px;
      margin: 0 auto 16px;
      color: #e5e6eb;
    }
    .empty-text {
      font-size: 16px;
      color: #999;
      margin-bottom: 16px;
    }
  </style>
</head>
<body>
  <div class="layout">
    <div class="sidebar">
      <div class="sidebar-header">
        <div class="logo">项目管理系统</div>
      </div>
      <div class="menu">
        <div class="menu-title">通用</div>
        <div class="menu-item">
          <div class="menu-icon" style="mask-image: url('data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 width=%2224%22 height=%2224%22 viewBox=%220 0 24 24%22 fill=%22none%22 stroke=%22currentColor%22 stroke-width=%222%22 stroke-linecap=%22round%22 stroke-linejoin=%22round%22><path d=%22m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z%22/><polyline points=%229 22 9 12 15 12 15 22%22/></svg>');"></div>
          首页
        </div>
        <div class="menu-item">
          <div class="menu-icon" style="mask-image: url('data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 width=%2224%22 height=%2224%22 viewBox=%220 0 24 24%22 fill=%22none%22 stroke=%22currentColor%22 stroke-width=%222%22 stroke-linecap=%22round%22 stroke-linejoin=%22round%22><path d=%22M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2%22></path><rect x=%228%22 y=%222%22 width=%228%22 height=%224%22 rx=%221%22 ry=%221%22></rect></svg>');"></div>
          待办事项
        </div>
        <div class="menu-title">项目</div>
        <div class="menu-item active">
          <div class="menu-icon" style="mask-image: url('data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 width=%2224%22 height=%2224%22 viewBox=%220 0 24 24%22 fill=%22none%22 stroke=%22currentColor%22 stroke-width=%222%22 stroke-linecap=%22round%22 stroke-linejoin=%22round%22><path d=%22M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5l2 3h9a2 2 0 0 1 2 2z%22></path></svg>');"></div>
          项目管理
        </div>
        <div class="menu-item">
          <div class="menu-icon" style="mask-image: url('data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 width=%2224%22 height=%2224%22 viewBox=%220 0 24 24%22 fill=%22none%22 stroke=%22currentColor%22 stroke-width=%222%22 stroke-linecap=%22round%22 stroke-linejoin=%22round%22><path d=%22M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z%22></path><polyline points=%2214 2 14 8 20 8%22></polyline><line x1=%2216%22 y1=%2213%22 x2=%228%22 y2=%2213%22></line><line x1=%2216%22 y1=%2217%22 x2=%228%22 y2=%2217%22></line><polyline points=%2210 9 9 9 8 9%22></polyline></svg>');"></div>
          需求管理
        </div>
        <div class="menu-item">
          <div class="menu-icon" style="mask-image: url('data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 width=%2224%22 height=%2224%22 viewBox=%220 0 24 24%22 fill=%22none%22 stroke=%22currentColor%22 stroke-width=%222%22 stroke-linecap=%22round%22 stroke-linejoin=%22round%22><polyline points=%2221 8 21 21 3 21 3 8%22></polyline><rect x=%221%22 y=%223%22 width=%2222%22 height=%225%22></rect><line x1=%2210%22 y1=%2212%22 x2=%2214%22 y2=%2212%22></line></svg>');"></div>
          合并请求
        </div>
        <div class="menu-title">测试</div>
        <div class="menu-item">
          <div class="menu-icon" style="mask-image: url('data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 width=%2224%22 height=%2224%22 viewBox=%220 0 24 24%22 fill=%22none%22 stroke=%22currentColor%22 stroke-width=%222%22 stroke-linecap=%22round%22 stroke-linejoin=%22round%22><path d=%22M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z%22></path><polyline points=%2214 2 14 8 20 8%22></polyline><path d=%22M16 13.58A2 2 0 0 0 15.3 12a2 2 0 0 0 .7-1.58A2 2 0 0 0 14 8.5h-6v8h6a2 2 0 0 0 2-1.92A2 2 0 0 0 16 13.58Z%22></path></svg>');"></div>
          测试用例
        </div>
        <div class="menu-title">系统</div>
        <div class="menu-item">
          <div class="menu-icon" style="mask-image: url('data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 width=%2224%22 height=%2224%22 viewBox=%220 0 24 24%22 fill=%22none%22 stroke=%22currentColor%22 stroke-width=%222%22 stroke-linecap=%22round%22 stroke-linejoin=%22round%22><path d=%22M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2%22></path><circle cx=%2212%22 cy=%227%22 r=%224%22></circle></svg>');"></div>
          用户管理
        </div>
      </div>
    </div>
    <div class="main">
      <div class="header">
        <div class="page-title">项目管理</div>
        <div class="user-info">
          <div class="avatar">管</div>
          <div class="username">管理员</div>
        </div>
      </div>
      <div class="content">
        <div class="tabs">
          <div class="tab active">我的项目 <span class="tab-badge">6</span></div>
          <div class="tab">我参与的</div>
          <div class="tab">全部项目</div>
          <div class="tab">已归档</div>
        </div>
        
        <div class="toolbar">
          <div class="toolbar-left">
            <div class="search-box">
              <svg class="search-icon" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="11" cy="11" r="8"></circle><line x1="21" y1="21" x2="16.65" y2="16.65"></line></svg>
              <input type="text" class="search-input" placeholder="搜索项目">
            </div>
          </div>
          <button class="btn btn-primary">
            <span class="btn-icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="12" y1="5" x2="12" y2="19"></line><line x1="5" y1="12" x2="19" y2="12"></line></svg>
            </span>
            创建项目
          </button>
        </div>
        
        <div class="project-grid">
          <div class="project-card">
            <div class="project-header">
              <div class="project-logo">用</div>
            </div>
            <div class="project-body">
              <h3 class="project-title">用户中心</h3>
              <p class="project-desc">集成用户身份认证、权限管理等功能的中心化服务，提供统一的用户账号与登录体系。</p>
              <div class="project-meta">
                <div class="meta-item">
                  <svg class="meta-icon" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path><polyline points="14 2 14 8 20 8"></polyline><line x1="16" y1="13" x2="8" y2="13"></line><line x1="16" y1="17" x2="8" y2="17"></line><polyline points="10 9 9 9 8 9"></polyline></svg>
                  8个需求
                </div>
                <span class="badge badge-blue">开发中</span>
              </div>
            </div>
            <div class="project-footer">
              <div class="member-list">
                <div class="member-avatar">张</div>
                <div class="member-avatar">李</div>
                <div class="member-avatar">王</div>
                <div class="more-btn">+2</div>
              </div>
            </div>
          </div>
          
          <div class="project-card">
            <div class="project-header" style="background: linear-gradient(135deg, #f5a623, #ff7d1a);">
              <div class="project-logo">支</div>
            </div>
            <div class="project-body">
              <h3 class="project-title">支付系统</h3>
              <p class="project-desc">提供多渠道支付能力，包括支付宝、微信支付、银联等接口对接，统一的交易流程管理。</p>
              <div class="project-meta">
                <div class="meta-item">
                  <svg class="meta-icon" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path><polyline points="14 2 14 8 20 8"></polyline><line x1="16" y1="13" x2="8" y2="13"></line><line x1="16" y1="17" x2="8" y2="17"></line><polyline points="10 9 9 9 8 9"></polyline></svg>
                  6个需求
                </div>
                <span class="badge badge-orange">测试中</span>
              </div>
            </div>
            <div class="project-footer">
              <div class="member-list">
                <div class="member-avatar">赵</div>
                <div class="member-avatar">王</div>
                <div class="member-avatar">钱</div>
              </div>
            </div>
          </div>
          
          <div class="project-card">
            <div class="project-header" style="background: linear-gradient(135deg, #52c41a, #1abb9c);">
              <div class="project-logo">消</div>
            </div>
            <div class="project-body">
              <h3 class="project-title">消息服务</h3>
              <p class="project-desc">提供站内信、短信、邮件等多渠道消息推送服务，支持模板管理和定时发送。</p>
              <div class="project-meta">
                <div class="meta-item">
                  <svg class="meta-icon" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path><polyline points="14 2 14 8 20 8"></polyline><line x1="16" y1="13" x2="8" y2="13"></line><line x1="16" y1="17" x2="8" y2="17"></line><polyline points="10 9 9 9 8 9"></polyline></svg>
                  5个需求
                </div>
                <span class="badge badge-blue">开发中</span>
              </div>
            </div>
            <div class="project-footer">
              <div class="member-list">
                <div class="member-avatar">李</div>
                <div class="member-avatar">张</div>
                <div class="more-btn">+1</div>
              </div>
            </div>
          </div>
          
          <div class="project-card">
            <div class="project-header" style="background: linear-gradient(135deg, #ff4d4f, #ff7875);">
              <div class="project-logo">订</div>
            </div>
            <div class="project-body">
              <h3 class="project-title">订单管理</h3>
              <p class="project-desc">提供订单全生命周期管理，包括创建、支付、发货、退款等流程，支持多种订单类型。</p>
              <div class="project-meta">
                <div class="meta-item">
                  <svg class="meta-icon" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path><polyline points="14 2 14 8 20 8"></polyline><line x1="16" y1="13" x2="8" y2="13"></line><line x1="16" y1="17" x2="8" y2="17"></line><polyline points="10 9 9 9 8 9"></polyline></svg>
                  12个需求
                </div>
                <span class="badge badge-green">已上线</span>
              </div>
            </div>
            <div class="project-footer">
              <div class="member-list">
                <div class="member-avatar">钱</div>
                <div class="member-avatar">赵</div>
                <div class="member-avatar">李</div>
                <div class="more-btn">+3</div>
              </div>
            </div>
          </div>
          
          <div class="project-card">
            <div class="project-header" style="background: linear-gradient(135deg, #722ed1, #b37feb);">
              <div class="project-logo">商</div>
            </div>
            <div class="project-body">
              <h3 class="project-title">商品管理</h3>
              <p class="project-desc">商品信息管理系统，支持商品创建、分类、规格、价格等管理，提供商品上下架和库存同步。</p>
              <div class="project-meta">
                <div class="meta-item">
                  <svg class="meta-icon" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path><polyline points="14 2 14 8 20 8"></polyline><line x1="16" y1="13" x2="8" y2="13"></line><line x1="16" y1="17" x2="8" y2="17"></line><polyline points="10 9 9 9 8 9"></polyline></svg>
                  9个需求
                </div>
                <span class="badge badge-green">已上线</span>
              </div>
            </div>
            <div class="project-footer">
              <div class="member-list">
                <div class="member-avatar">王</div>
                <div class="member-avatar">钱</div>
                <div class="member-avatar">赵</div>
                <div class="more-btn">+2</div>
              </div>
            </div>
          </div>
          
          <div class="project-card">
            <div class="project-header" style="background: linear-gradient(135deg, #2f54eb, #597ef7);">
              <div class="project-logo">搜</div>
            </div>
            <div class="project-body">
              <h3 class="project-title">搜索服务</h3>
              <p class="project-desc">基于Elasticsearch构建的搜索引擎服务，提供商品、内容等多维度搜索，支持自动补全与纠错。</p>
              <div class="project-meta">
                <div class="meta-item">
                  <svg class="meta-icon" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path><polyline points="14 2 14 8 20 8"></polyline><line x1="16" y1="13" x2="8" y2="13"></line><line x1="16" y1="17" x2="8" y2="17"></line><polyline points="10 9 9 9 8 9"></polyline></svg>
                  3个需求
                </div>
                <span class="badge badge-blue">开发中</span>
              </div>
            </div>
            <div class="project-footer">
              <div class="member-list">
                <div class="member-avatar">张</div>
                <div class="member-avatar">李</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</body>
</html> 