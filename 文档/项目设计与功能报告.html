<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <title>项目管理系统设计与功能报告</title>
  <style>
    body { font-family: "Microsoft YaHei", Arial, sans-serif; margin: 40px; color: #222; }
    h1, h2, h3 { color: #1877f2; }
    h1 { border-bottom: 2px solid #eee; padding-bottom: 8px; }
    table { border-collapse: collapse; width: 100%; margin-bottom: 24px; }
    th, td { border: 1px solid #ddd; padding: 8px; }
    th { background: #f0f2f5; }
    .section { margin-bottom: 36px; }
    code { background: #f5f5f5; padding: 2px 4px; border-radius: 3px; }
    ul, ol { margin-left: 1.5em; }
  </style>
</head>
<body>
  <h1>项目管理系统设计与功能报告</h1>

  <div class="section">
    <h2>一、系统定位与目标</h2>
    <p>
      本系统是一套面向研发团队的项目管理平台，集成了项目、需求、测试用例、合并请求（PR）、用户与权限等核心管理功能，支持与GitLab代码仓库联动，适用于需求全流程追踪、协作开发与质量保障。
    </p>
  </div>

  <div class="section">
    <h2>二、技术架构</h2>
    <ul>
      <li><b>前端：</b>Vue 3、Vite、Element Plus、Axios</li>
      <li><b>后端：</b>Python FastAPI、SQLAlchemy、SQLite</li>
      <li><b>其他：</b>与GitLab API集成，AI服务用于需求分支名生成</li>
    </ul>
  </div>

  <div class="section">
    <h2>三、核心功能模块</h2>
    <ol>
      <li><b>用户与权限管理</b>
        <ul>
          <li>支持用户的注册、登录、信息修改、删除、启用/禁用，所有操作需管理员权限</li>
          <li>用户信息包括用户名、姓名、角色（管理员、开发人员、测试人员、项目管理，可多选）、企业微信ID、状态（启用/禁用）</li>
          <li>权限校验严格，防止越权操作</li>
        </ul>
      </li>
      <li><b>项目管理</b>
        <ul>
          <li>项目的创建、编辑、删除、查询，需管理员权限</li>
          <li>项目成员分为开发人员和测试人员，支持成员的添加、移除</li>
          <li>与GitLab仓库深度集成，支持分支、PR等操作</li>
          <li>项目详情接口可获取项目的全部成员、分支、PR等信息</li>
        </ul>
      </li>
      <li><b>需求管理</b>
        <ul>
          <li>需求支持创建、分配、认领、开发、提交测试、撤回、通过/驳回、验证、删除等全流程操作</li>
          <li>需求编号自动生成，分支名可通过AI服务自动生成，支持多分支管理</li>
          <li>需求历史记录自动追踪，所有状态流转均有记录</li>
          <li>需求内容、分支等敏感信息仅在认领后可见，权限动态控制</li>
          <li>支持需求的多条件查询、分页、详情获取等</li>
        </ul>
      </li>
      <li><b>测试用例与功能模块管理</b>
        <ul>
          <li>支持测试用例的增删改查，测试用例与项目、功能模块关联</li>
          <li>测试用例功能模块支持增删改查，权限控制为管理员或项目相关成员</li>
          <li>测试用例详细信息包括编号、名称、模块、类型、端、状态、步骤、预期结果、关联代码、创建人、修改人等</li>
        </ul>
      </li>
      <li><b>合并请求（PR）管理</b>
        <ul>
          <li>支持PR的创建（通常由系统自动或根据开发流程触发）、查询、分组、批量合并、关闭、状态追踪</li>
          <li>PR与需求、项目、GitLab分支深度联动，支持按需求分组展示</li>
          <li>一个需求可能关联多个合并分支请求，系统可以追踪同一需求下的所有分支</li>
          <li>所有分支合并成功后，系统自动删除远程的当前分支，保持代码库整洁</li>
          <li>只有管理员可进行批量合并、关闭等操作，权限严格</li>
        </ul>
      </li>
      <li><b>首页与工作台</b>
        <ul>
          <li>登录后直接进入首页，首页作为用户的核心工作台，集成待办任务与快捷操作。</li>
          <li>首页根据用户角色（管理员/非管理员）展示不同布局和功能模块。</li>
          <li><b>管理员首页：</b>展示系统概览（用户、项目、PR、需求统计）、系统级待处理任务（如待分配需求、待审核PR）、常用管理功能快捷入口（如用户管理、项目管理）。</li>
          <li><b>非管理员首页：</b>根据用户的具体细分角色（如开发、测试，或两者兼有）展示个性化的任务列表（如我的开发任务、我的测试任务、待认领任务）。任务列表直接展示，无需额外筛选操作。任务列表会清晰标示任务类型（如开发、测试）。测试任务卡片将显示相关的分支信息，而不是测试用例数量。</li>
          <li>所有操作均支持一键完成，提升工作效率。</li>
        </ul>
      </li>
      <li><b>权限与安全控制</b>
        <ul>
          <li>后端接口权限校验严格，所有操作均需身份认证和角色校验</li>
          <li>前端路由守卫实现基于token和角色的页面访问控制</li>
          <li>操作按钮和页面内容根据用户角色和数据状态动态显示，防止越权操作</li>
        </ul>
      </li>
      <li><b>登录与权限控制</b>
        <ul>
          <li>基于Token的认证，前后端接口权限校验</li>
        </ul>
      </li>
    </ol>
  </div>

  <div class="section">
    <h2>四、主要页面与交互流程</h2>
    <ul>
      <li><b>登录页：</b>用户输入账号密码登录，支持忘记密码入口（预留）</li>
      <li><b>首页（核心工作台）：</b>用户登录后默认进入此页面。移除独立的"待办事项"页面，其功能完全整合入首页。
        <ul>
          <li><b>管理员首页：</b>
            <ul>
              <li>顶部展示关键数据统计（如用户总数、项目总数、待审核PR数、待处理需求数）。</li>
              <li>下方为核心管理任务区，如"系统待处理事项"（包括待分配的需求、待审核的PR等，可进行分配、审核操作）、"快速入口"（如用户管理、项目创建、全局设置等）。</li>
              <li>页面设计简洁高效，突出管理操作的便捷性。</li>
            </ul>
          </li>
          <li><b>非管理员首页：</b>
            <ul>
              <li>顶部根据用户具体角色（开发、测试、或两者兼有）展示个人相关的任务统计和提醒。</li>
              <li>核心区域为任务列表，如"我的开发任务"（状态：开发中、待提交测试、已驳回等）、"我的测试任务"（状态：待测试、测试中、测试不通过等）、"可认领任务"。任务列表直接展示，移除了筛选功能。</li>
              <li>任务卡片清晰展示任务类型（开发/测试）、项目、状态、截止日期、分支信息、关键操作按钮（如提交测试、通过、驳回、查看详情）。</li>
              <li>页面布局清晰，方便用户快速定位和处理个人任务。</li>
            </ul>
          </li>
        </ul>
      </li>
      <li><b>项目管理：</b>项目列表、添加/编辑/删除项目、成员管理（开发/测试）、与GitLab项目关联，项目详情页支持多维度管理</li>
      <li><b>需求列表：</b>支持多条件筛选、需求详情、状态流转（认领、提交测试、撤回、通过/驳回等），分支信息和内容权限动态控制</li>
      <li><b>用户管理：</b>管理员可增删改查用户，分配角色（可多选，包括管理员、开发人员、测试人员、项目管理）、状态，记录企业微信ID。</li>
      <li><b>合并请求管理：</b>按需求分组展示PR，支持批量合并、关闭、查看详情</li>
      <li><b>测试用例项目列表：</b>展示所有可管理项目，点击进入项目的测试用例列表</li>
      <li><b>测试用例列表：</b>支持新增、查看、编辑测试用例，展示详细信息</li>
    </ul>
  </div>

  <div class="section">
    <h2>五、用户角色与权限说明</h2>
    <table>
      <tr>
        <th>角色</th>
        <th>主要权限</th>
      </tr>
      <tr>
        <td>管理员</td>
        <td>全局管理（用户、项目、需求、PR等），可分配成员，删除项目/用户</td>
      </tr>
      <tr>
        <td>开发人员</td>
        <td>认领/开发需求，提交测试，参与项目，查看相关PR</td>
      </tr>
      <tr>
        <td>测试人员</td>
        <td>参与测试，审核需求，反馈测试结果</td>
      </tr>
      <tr>
        <td>项目管理</td>
        <td>管理项目范围、进度、资源，协调项目成员，但不直接参与代码开发或测试执行。负责项目整体规划与风险控制。</td>
      </tr>
      <tr>
        <td>普通用户</td>
        <td>仅可查看参与项目及需求，有限操作</td>
      </tr>
    </table>
  </div>

  <div class="section">
    <h2>六、典型业务流程</h2>
    <ol>
      <li><b>项目创建与成员分配：</b>管理员新建项目，分配开发/测试成员，关联GitLab仓库</li>
      <li><b>需求全流程：</b>
        <ul>
          <li>需求创建（自动生成编号与分支）→ 认领 → 开发 → 提交测试 → 测试审核 → 验证 → 合并PR</li>
          <li>每一步均有权限校验与状态流转，支持撤回、驳回等操作</li>
          <li>分支信息、需求内容等仅在认领后可见，权限动态控制</li>
        </ul>
      </li>
      <li><b>PR管理：</b>开发完成后自动/手动创建PR，同一需求可关联多个分支PR，管理员可批量合并或关闭，状态实时同步，所有分支合并完成后自动删除远程分支</li>
      <li><b>用户管理：</b>管理员可增删改查用户，分配角色与状态</li>
      <li><b>测试用例管理：</b>支持项目-用例两级管理，测试用例可增删改查，详细信息展示</li>
    </ol>
  </div>

  <div class="section">
    <h2>七、接口与数据规范</h2>
    <ul>
      <li>所有接口均为RESTful风格，数据格式为JSON</li>
      <li>请求需携带Token，响应统一格式（code/message/data）</li>
      <li>支持多条件查询、分页、分组等高级数据操作，提升数据管理效率</li>
      <li>详细接口见前后端 <code>api/</code> 目录及 <code>README.md</code></li>
    </ul>
  </div>

  <div class="section">
    <h2>八、系统亮点</h2>
    <ul>
      <li>需求分支名可AI自动生成，提升规范性与效率</li>
      <li>与GitLab深度集成，自动化分支/PR管理，支持按需求管理多个分支PR</li>
      <li>合并请求完成后智能清理，所有分支合并成功后自动删除远程分支，保持代码库整洁</li>
      <li>权限细粒度控制，安全可靠，分支和内容权限动态显示</li>
      <li>全流程可追溯，历史记录完善</li>
      <li>后端接口权限校验严格，所有操作均需身份认证和角色校验</li>
      <li>支持多条件查询、分页、分组等高级数据操作，提升数据管理效率</li>
      <li>与AI服务、GitLab等外部系统集成，自动化和智能化水平高</li>
      <li>前端页面交互友好，操作按钮根据角色和状态动态显示，极大提升用户体验</li>
      <li>需求、项目、PR、测试用例等多维度联动，支持全流程追踪</li>
      <li>根据用户角色智能展示不同内容，为管理员、开发人员、测试人员提供针对性视图</li>
      <li>支持用户多重角色身份，同时展示开发任务和测试任务，提高跨角色协作效率</li>
      <li>首页和待办事项页面针对不同角色定制，显示最相关的任务和操作</li>
    </ul>
  </div>

  <div class="section">
    <h2>九、部署与运行</h2>
    <ul>
      <li>前端：<code>npm install</code> → <code>npm run dev</code> 启动开发环境</li>
      <li>后端：<code>python main.py</code> 或 <code>uvicorn main:app</code> 启动API服务</li>
      <li>详细部署见各自 <code>README.md</code></li>
    </ul>
  </div>

  <div class="section">
    <h2>十、前端未实现或无效功能点说明</h2>
    <ul>
      <li><b>忘记密码功能：</b>登录页"忘记密码"仅为占位，暂无实际找回密码流程。</li>
      <li><b>空白页面：</b>首页仅有欢迎语，无功能入口；各类列表页无数据时页面空白，仅显示"暂无数据"。</li>
      <li><b>无实际业务功能的按钮：</b>如弹窗"取消"按钮仅关闭弹窗，部分"编辑"按钮仅切换UI状态，未做数据校验或保存提示。</li>
      <li><b>部分"复制"按钮：</b>如需求分支的"复制"按钮，若浏览器不支持剪贴板API则无效。</li>
      <li><b>部分超链接无跳转：</b>如PR管理页面"在GitLab中查看"链接，若无URL则无任何反应。</li>
      <li><b>部分弹窗/表单未做校验：</b>如"添加/编辑测试功能模块"弹窗，未填写内容时可直接关闭，无提示。</li>
      <li><b>部分API异常未提示：</b>部分操作如接口异常，页面无明显错误提示。</li>
    </ul>
    <p>建议后续开发中完善上述功能点，提升用户体验和系统健壮性。</p>
  </div>
</body>
</html> 