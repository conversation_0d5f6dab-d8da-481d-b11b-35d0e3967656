<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <title>项目管理系统 - 需求管理</title>
  <style>
    * { margin: 0; padding: 0; box-sizing: border-box; }
    body { 
      font-family: "Microsoft YaHei", -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Arial, sans-serif; 
      background-color: #f5f7fa; 
      color: #222;
      min-height: 100vh;
    }
    .layout {
      display: flex;
      min-height: 100vh;
    }
    .sidebar {
      width: 220px;
      background-color: #fff;
      border-right: 1px solid #e5e6eb;
      box-shadow: 2px 0 8px rgba(0,0,0,0.03);
      position: fixed;
      height: 100vh;
      z-index: 100;
    }
    .sidebar-header {
      height: 60px;
      border-bottom: 1px solid #e5e6eb;
      display: flex;
      align-items: center;
      padding: 0 20px;
    }
    .logo {
      font-size: 18px;
      font-weight: bold;
      color: #1877f2;
    }
    .menu {
      padding: 20px 0;
    }
    .menu-title {
      font-size: 13px;
      color: #999;
      padding: 0 20px;
      margin-bottom: 8px;
    }
    .menu-item {
      height: 46px;
      display: flex;
      align-items: center;
      padding: 0 20px;
      color: #666;
      font-size: 15px;
      margin-bottom: 2px;
      cursor: pointer;
      position: relative;
      transition: all 0.3s;
    }
    .menu-item:hover {
      color: #1877f2;
      background-color: rgba(24, 119, 242, 0.04);
    }
    .menu-item.active {
      color: #1877f2;
      background-color: rgba(24, 119, 242, 0.08);
      font-weight: 500;
    }
    .menu-item.active::before {
      content: '';
      position: absolute;
      left: 0;
      top: 12px;
      bottom: 12px;
      width: 3px;
      background-color: #1877f2;
      border-radius: 0 2px 2px 0;
    }
    .menu-icon {
      width: 20px;
      height: 20px;
      margin-right: 8px;
      background-color: currentColor;
      opacity: 0.6;
      mask-size: cover;
      -webkit-mask-size: cover;
    }
    .main {
      flex: 1;
      margin-left: 220px;
    }
    .header {
      height: 60px;
      background-color: #fff;
      border-bottom: 1px solid #e5e6eb;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 24px;
      position: sticky;
      top: 0;
      z-index: 99;
    }
    .page-title {
      font-size: 18px;
      font-weight: 500;
      color: #222;
    }
    .user-info {
      display: flex;
      align-items: center;
    }
    .avatar {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      background-color: #1877f2;
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 500;
      margin-right: 8px;
    }
    .username {
      font-size: 14px;
      color: #666;
    }
    .content {
      padding: 24px;
    }
    .filter-bar {
      background: #fff;
      border-radius: 10px;
      box-shadow: 0 2px 12px rgba(0,0,0,0.04);
      padding: 20px;
      margin-bottom: 24px;
    }
    .filter-form {
      display: flex;
      flex-wrap: wrap;
      gap: 16px;
    }
    .filter-item {
      flex: 1;
      min-width: 180px;
    }
    .filter-label {
      display: block;
      margin-bottom: 8px;
      font-size: 14px;
      color: #666;
    }
    .filter-input {
      width: 100%;
      height: 38px;
      border: 1px solid #e5e6eb;
      border-radius: 6px;
      padding: 0 12px;
      font-size: 14px;
      color: #222;
    }
    .filter-input:focus {
      outline: none;
      border-color: #1877f2;
    }
    .filter-actions {
      display: flex;
      justify-content: flex-end;
      margin-top: 20px;
      gap: 12px;
    }
    .toolbar {
      display: flex;
      justify-content: space-between;
      margin-bottom: 24px;
    }
    .btn {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      height: 38px;
      padding: 0 16px;
      border-radius: 6px;
      font-size: 15px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s;
      border: none;
    }
    .btn-primary {
      background-color: #1877f2;
      color: white;
    }
    .btn-primary:hover {
      background-color: #0d6efd;
    }
    .btn-outline {
      background-color: transparent;
      color: #666;
      border: 1px solid #e5e6eb;
    }
    .btn-outline:hover {
      border-color: #1877f2;
      color: #1877f2;
    }
    .btn-text {
      background-color: transparent;
      color: #1877f2;
    }
    .btn-text:hover {
      background-color: rgba(24, 119, 242, 0.04);
    }
    .btn-small {
      height: 32px;
      padding: 0 12px;
      font-size: 14px;
    }
    .btn-icon {
      width: 16px;
      height: 16px;
      margin-right: 6px;
    }
    .table-card {
      background-color: #fff;
      border-radius: 10px;
      box-shadow: 0 2px 12px rgba(0,0,0,0.04);
      overflow: hidden;
    }
    .table {
      width: 100%;
      border-collapse: collapse;
    }
    .table th, .table td {
      padding: 16px 20px;
      text-align: left;
      border-bottom: 1px solid #e5e6eb;
    }
    .table th {
      font-weight: 500;
      color: #666;
      background-color: #fafafa;
    }
    .table td {
      color: #222;
    }
    .table tr:last-child td {
      border-bottom: none;
    }
    .table tr:hover {
      background-color: #f5f7fa;
    }
    .badge {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      height: 24px;
      padding: 0 10px;
      border-radius: 12px;
      font-size: 13px;
      font-weight: 500;
    }
    .badge-blue {
      background-color: rgba(24, 119, 242, 0.1);
      color: #1877f2;
    }
    .badge-green {
      background-color: rgba(82, 196, 26, 0.1);
      color: #52c41a;
    }
    .badge-orange {
      background-color: rgba(245, 166, 35, 0.1);
      color: #f5a623;
    }
    .badge-red {
      background-color: rgba(255, 77, 79, 0.1);
      color: #ff4d4f;
    }
    .badge-gray {
      background-color: rgba(153, 153, 153, 0.1);
      color: #999;
    }
    .pagination {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: 24px;
    }
    .page-item {
      width: 36px;
      height: 36px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 4px;
      border-radius: 6px;
      font-size: 14px;
      color: #666;
      cursor: pointer;
      transition: all 0.3s;
    }
    .page-item:hover {
      background-color: rgba(0,0,0,0.04);
      color: #1877f2;
    }
    .page-item.active {
      background-color: #1877f2;
      color: white;
    }
  </style>
</head>
<body>
  <div class="layout">
    <div class="sidebar">
      <div class="sidebar-header">
        <div class="logo">项目管理系统</div>
      </div>
      <div class="menu">
        <div class="menu-title">通用</div>
        <div class="menu-item">
          <div class="menu-icon" style="mask-image: url('data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 width=%2224%22 height=%2224%22 viewBox=%220 0 24 24%22 fill=%22none%22 stroke=%22currentColor%22 stroke-width=%222%22 stroke-linecap=%22round%22 stroke-linejoin=%22round%22><path d=%22m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z%22/><polyline points=%229 22 9 12 15 12 15 22%22/></svg>');"></div>
          首页
        </div>
        <div class="menu-item">
          <div class="menu-icon" style="mask-image: url('data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 width=%2224%22 height=%2224%22 viewBox=%220 0 24 24%22 fill=%22none%22 stroke=%22currentColor%22 stroke-width=%222%22 stroke-linecap=%22round%22 stroke-linejoin=%22round%22><path d=%22M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2%22></path><rect x=%228%22 y=%222%22 width=%228%22 height=%224%22 rx=%221%22 ry=%221%22></rect></svg>');"></div>
          待办事项
        </div>
        <div class="menu-title">项目</div>
        <div class="menu-item">
          <div class="menu-icon" style="mask-image: url('data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 width=%2224%22 height=%2224%22 viewBox=%220 0 24 24%22 fill=%22none%22 stroke=%22currentColor%22 stroke-width=%222%22 stroke-linecap=%22round%22 stroke-linejoin=%22round%22><path d=%22M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5l2 3h9a2 2 0 0 1 2 2z%22></path></svg>');"></div>
          项目管理
        </div>
        <div class="menu-item active">
          <div class="menu-icon" style="mask-image: url('data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 width=%2224%22 height=%2224%22 viewBox=%220 0 24 24%22 fill=%22none%22 stroke=%22currentColor%22 stroke-width=%222%22 stroke-linecap=%22round%22 stroke-linejoin=%22round%22><path d=%22M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z%22></path><polyline points=%2214 2 14 8 20 8%22></polyline><line x1=%2216%22 y1=%2213%22 x2=%228%22 y2=%2213%22></line><line x1=%2216%22 y1=%2217%22 x2=%228%22 y2=%2217%22></line><polyline points=%2210 9 9 9 8 9%22></polyline></svg>');"></div>
          需求管理
        </div>
        <div class="menu-item">
          <div class="menu-icon" style="mask-image: url('data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 width=%2224%22 height=%2224%22 viewBox=%220 0 24 24%22 fill=%22none%22 stroke=%22currentColor%22 stroke-width=%222%22 stroke-linecap=%22round%22 stroke-linejoin=%22round%22><polyline points=%2221 8 21 21 3 21 3 8%22></polyline><rect x=%221%22 y=%223%22 width=%2222%22 height=%225%22></rect><line x1=%2210%22 y1=%2212%22 x2=%2214%22 y2=%2212%22></line></svg>');"></div>
          合并请求
        </div>
        <div class="menu-title">测试</div>
        <div class="menu-item">
          <div class="menu-icon" style="mask-image: url('data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 width=%2224%22 height=%2224%22 viewBox=%220 0 24 24%22 fill=%22none%22 stroke=%22currentColor%22 stroke-width=%222%22 stroke-linecap=%22round%22 stroke-linejoin=%22round%22><path d=%22M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z%22></path><polyline points=%2214 2 14 8 20 8%22></polyline><path d=%22M16 13.58A2 2 0 0 0 15.3 12a2 2 0 0 0 .7-1.58A2 2 0 0 0 14 8.5h-6v8h6a2 2 0 0 0 2-1.92A2 2 0 0 0 16 13.58Z%22></path></svg>');"></div>
          测试用例
        </div>
        <div class="menu-title">系统</div>
        <div class="menu-item">
          <div class="menu-icon" style="mask-image: url('data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 width=%2224%22 height=%2224%22 viewBox=%220 0 24 24%22 fill=%22none%22 stroke=%22currentColor%22 stroke-width=%222%22 stroke-linecap=%22round%22 stroke-linejoin=%22round%22><path d=%22M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2%22></path><circle cx=%2212%22 cy=%227%22 r=%224%22></circle></svg>');"></div>
          用户管理
        </div>
      </div>
    </div>
    <div class="main">
      <div class="header">
        <div class="page-title">需求管理</div>
        <div class="user-info">
          <div class="avatar">管</div>
          <div class="username">管理员</div>
        </div>
      </div>
      <div class="content">
        <div class="filter-bar">
          <div class="filter-form">
            <div class="filter-item">
              <label class="filter-label">项目</label>
              <select class="filter-input">
                <option value="">全部项目</option>
                <option value="1">用户中心</option>
                <option value="2">消息服务</option>
                <option value="3">支付系统</option>
              </select>
            </div>
            <div class="filter-item">
              <label class="filter-label">状态</label>
              <select class="filter-input">
                <option value="">全部状态</option>
                <option value="1">待认领</option>
                <option value="2">开发中</option>
                <option value="3">待测试</option>
                <option value="4">测试中</option>
                <option value="5">已完成</option>
                <option value="6">已驳回</option>
              </select>
            </div>
            <div class="filter-item">
              <label class="filter-label">负责人</label>
              <select class="filter-input">
                <option value="">全部</option>
                <option value="1">张三</option>
                <option value="2">李四</option>
                <option value="3">王五</option>
              </select>
            </div>
            <div class="filter-item">
              <label class="filter-label">关键词</label>
              <input type="text" class="filter-input" placeholder="需求编号/标题/内容">
            </div>
          </div>
          <div class="filter-actions">
            <button class="btn btn-outline">重置</button>
            <button class="btn btn-primary">筛选</button>
          </div>
        </div>

        <div class="toolbar">
          <div style="font-size: 15px; color: #666;">
            共 <span style="color: #1877f2; font-weight: 500;">34</span> 条需求
          </div>
          <button class="btn btn-primary">
            <span class="btn-icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="12" y1="5" x2="12" y2="19"></line><line x1="5" y1="12" x2="19" y2="12"></line></svg>
            </span>
            新建需求
          </button>
        </div>

        <div class="table-card">
          <table class="table">
            <thead>
              <tr>
                <th>需求编号</th>
                <th width="28%">需求标题</th>
                <th>状态</th>
                <th>负责人</th>
                <th>项目</th>
                <th>创建时间</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>REQ-20230501</td>
                <td>用户中心-登录功能优化</td>
                <td><span class="badge badge-blue">开发中</span></td>
                <td>张三</td>
                <td>用户中心</td>
                <td>2023-05-01</td>
                <td>
                  <button class="btn btn-small btn-text">详情</button>
                </td>
              </tr>
              <tr>
                <td>REQ-20230502</td>
                <td>消息服务-推送功能接口开发</td>
                <td><span class="badge badge-orange">待测试</span></td>
                <td>李四</td>
                <td>消息服务</td>
                <td>2023-05-02</td>
                <td>
                  <button class="btn btn-small btn-text">详情</button>
                </td>
              </tr>
              <tr>
                <td>REQ-20230503</td>
                <td>用户中心-注册接口优化</td>
                <td><span class="badge badge-green">已完成</span></td>
                <td>张三</td>
                <td>用户中心</td>
                <td>2023-05-03</td>
                <td>
                  <button class="btn btn-small btn-text">详情</button>
                </td>
              </tr>
              <tr>
                <td>REQ-20230504</td>
                <td>支付系统-支付回调处理</td>
                <td><span class="badge badge-red">已驳回</span></td>
                <td>赵六</td>
                <td>支付系统</td>
                <td>2023-05-04</td>
                <td>
                  <button class="btn btn-small btn-text">详情</button>
                </td>
              </tr>
              <tr>
                <td>REQ-20230505</td>
                <td>消息服务-邮件模板功能</td>
                <td><span class="badge badge-gray">待认领</span></td>
                <td>-</td>
                <td>消息服务</td>
                <td>2023-05-05</td>
                <td>
                  <button class="btn btn-small btn-text">认领</button>
                  <button class="btn btn-small btn-text">详情</button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <div class="pagination">
          <div class="page-item">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="15 18 9 12 15 6"></polyline></svg>
          </div>
          <div class="page-item active">1</div>
          <div class="page-item">2</div>
          <div class="page-item">3</div>
          <div class="page-item">4</div>
          <div class="page-item">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="9 18 15 12 9 6"></polyline></svg>
          </div>
        </div>
      </div>
    </div>
  </div>
</body>
</html> 