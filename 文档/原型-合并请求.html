<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <title>项目管理系统 - 合并请求</title>
  <style>
    * { margin: 0; padding: 0; box-sizing: border-box; }
    body { 
      font-family: "Microsoft YaHei", -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Arial, sans-serif; 
      background-color: #f5f7fa; 
      color: #222;
      min-height: 100vh;
    }
    .layout {
      display: flex;
      min-height: 100vh;
    }
    .sidebar {
      width: 220px;
      background-color: #fff;
      border-right: 1px solid #e5e6eb;
      box-shadow: 2px 0 8px rgba(0,0,0,0.03);
      position: fixed;
      height: 100vh;
      z-index: 100;
    }
    .sidebar-header {
      height: 60px;
      border-bottom: 1px solid #e5e6eb;
      display: flex;
      align-items: center;
      padding: 0 20px;
    }
    .logo {
      font-size: 18px;
      font-weight: bold;
      color: #1877f2;
    }
    .menu {
      padding: 20px 0;
    }
    .menu-title {
      font-size: 13px;
      color: #999;
      padding: 0 20px;
      margin-bottom: 8px;
    }
    .menu-item {
      height: 46px;
      display: flex;
      align-items: center;
      padding: 0 20px;
      color: #666;
      font-size: 15px;
      margin-bottom: 2px;
      cursor: pointer;
      position: relative;
      transition: all 0.3s;
    }
    .menu-item:hover {
      color: #1877f2;
      background-color: rgba(24, 119, 242, 0.04);
    }
    .menu-item.active {
      color: #1877f2;
      background-color: rgba(24, 119, 242, 0.08);
      font-weight: 500;
    }
    .menu-item.active::before {
      content: '';
      position: absolute;
      left: 0;
      top: 12px;
      bottom: 12px;
      width: 3px;
      background-color: #1877f2;
      border-radius: 0 2px 2px 0;
    }
    .menu-icon {
      width: 20px;
      height: 20px;
      margin-right: 8px;
      background-color: currentColor;
      opacity: 0.6;
      mask-size: cover;
      -webkit-mask-size: cover;
    }
    .main {
      flex: 1;
      margin-left: 220px;
    }
    .header {
      height: 60px;
      background-color: #fff;
      border-bottom: 1px solid #e5e6eb;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 24px;
      position: sticky;
      top: 0;
      z-index: 99;
    }
    .page-title {
      font-size: 18px;
      font-weight: 500;
      color: #222;
    }
    .user-info {
      display: flex;
      align-items: center;
    }
    .avatar {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      background-color: #1877f2;
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 500;
      margin-right: 8px;
    }
    .username {
      font-size: 14px;
      color: #666;
    }
    .content {
      padding: 24px;
    }
    .toolbar {
      display: flex;
      justify-content: space-between;
      margin-bottom: 24px;
    }
    .toolbar-left {
      display: flex;
      gap: 12px;
    }
    .search-box {
      width: 260px;
      height: 38px;
      border: 1px solid #e5e6eb;
      border-radius: 6px;
      padding: 0 16px;
      display: flex;
      align-items: center;
    }
    .search-input {
      flex: 1;
      height: 100%;
      border: none;
      outline: none;
      background: transparent;
      font-size: 14px;
      color: #222;
    }
    .search-icon {
      width: 16px;
      height: 16px;
      margin-right: 8px;
      opacity: 0.5;
    }
    .btn {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      height: 38px;
      padding: 0 16px;
      border-radius: 6px;
      font-size: 15px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s;
      border: none;
    }
    .btn-primary {
      background-color: #1877f2;
      color: white;
    }
    .btn-primary:hover {
      background-color: #0d6efd;
    }
    .btn-outline {
      background-color: transparent;
      color: #666;
      border: 1px solid #e5e6eb;
    }
    .btn-outline:hover {
      border-color: #1877f2;
      color: #1877f2;
    }
    .btn-icon {
      width: 16px;
      height: 16px;
      margin-right: 6px;
    }
    .tabs {
      display: flex;
      border-bottom: 1px solid #e5e6eb;
      margin-bottom: 24px;
    }
    .tab {
      padding: 0 20px;
      height: 40px;
      display: flex;
      align-items: center;
      font-size: 15px;
      color: #666;
      position: relative;
      cursor: pointer;
    }
    .tab.active {
      color: #1877f2;
      font-weight: 500;
    }
    .tab.active::after {
      content: '';
      position: absolute;
      left: 20px;
      right: 20px;
      bottom: -1px;
      height: 2px;
      background-color: #1877f2;
      border-radius: 2px 2px 0 0;
    }
    .tab-badge {
      min-width: 18px;
      height: 18px;
      background-color: #1877f2;
      border-radius: 9px;
      color: white;
      font-size: 12px;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      padding: 0 6px;
      margin-left: 8px;
    }
    .card {
      background-color: #fff;
      border-radius: 10px;
      box-shadow: 0 2px 12px rgba(0,0,0,0.04);
      overflow: hidden;
      margin-bottom: 16px;
      transition: all 0.3s;
    }
    .card:hover {
      box-shadow: 0 4px 16px rgba(0,0,0,0.08);
    }
    .mr-card {
      padding: 20px;
      display: flex;
      gap: 16px;
    }
    .mr-group {
      border-left: 3px solid #1877f2;
      padding-left: 12px;
      margin-bottom: 6px;
      font-weight: 500;
      font-size: 16px;
      color: #1877f2;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    .mr-group-badge {
      font-size: 13px;
      color: #666;
      font-weight: normal;
    }
    .branch-list {
      margin-top: 8px;
      padding: 8px 0;
      border-top: 1px dashed #e5e6eb;
    }
    .branch-item {
      display: flex;
      align-items: center;
      padding: 6px 0;
      font-size: 14px;
      color: #666;
    }
    .branch-icon {
      width: 16px;
      height: 16px;
      margin-right: 8px;
      opacity: 0.7;
    }
    .auto-delete-notice {
      display: flex;
      align-items: center;
      margin-top: 8px;
      padding: 8px 12px;
      background-color: rgba(82, 196, 26, 0.05);
      border-radius: 6px;
      font-size: 13px;
      color: #52c41a;
    }
    .notice-icon {
      width: 16px;
      height: 16px;
      margin-right: 8px;
      color: #52c41a;
    }
    .mr-info {
      flex: 1;
    }
    .mr-title {
      font-size: 16px;
      font-weight: 500;
      color: #222;
      margin-bottom: 8px;
    }
    .mr-title a {
      color: #1877f2;
      text-decoration: none;
    }
    .mr-title a:hover {
      text-decoration: underline;
    }
    .mr-meta {
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      gap: 16px;
      color: #666;
      font-size: 14px;
      margin-bottom: 12px;
    }
    .meta-item {
      display: flex;
      align-items: center;
    }
    .meta-icon {
      width: 16px;
      height: 16px;
      margin-right: 6px;
      opacity: 0.7;
    }
    .mr-desc {
      color: #666;
      font-size: 14px;
      line-height: 1.5;
    }
    .badge {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      height: 24px;
      padding: 0 10px;
      border-radius: 12px;
      font-size: 13px;
      font-weight: 500;
    }
    .badge-blue {
      background-color: rgba(24, 119, 242, 0.1);
      color: #1877f2;
    }
    .badge-green {
      background-color: rgba(82, 196, 26, 0.1);
      color: #52c41a;
    }
    .badge-orange {
      background-color: rgba(245, 166, 35, 0.1);
      color: #f5a623;
    }
    .badge-red {
      background-color: rgba(255, 77, 79, 0.1);
      color: #ff4d4f;
    }
    .select {
      height: 38px;
      min-width: 120px;
      border: 1px solid #e5e6eb;
      border-radius: 6px;
      padding: 0 12px;
      font-size: 14px;
      color: #222;
      background-color: white;
      appearance: none;
      background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23999' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
      background-repeat: no-repeat;
      background-position: right 12px center;
      background-size: 16px;
      padding-right: 36px;
    }
    .select:focus {
      outline: none;
      border-color: #1877f2;
    }
    .mr-actions {
      display: flex;
      gap: 8px;
    }
    .pagination {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: 32px;
    }
    .page-item {
      width: 36px;
      height: 36px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 4px;
      border-radius: 6px;
      font-size: 14px;
      color: #666;
      cursor: pointer;
      transition: all 0.3s;
    }
    .page-item:hover {
      background-color: rgba(0,0,0,0.04);
      color: #1877f2;
    }
    .page-item.active {
      background-color: #1877f2;
      color: white;
    }
  </style>
</head>
<body>
  <div class="layout">
    <div class="sidebar">
      <div class="sidebar-header">
        <div class="logo">项目管理系统</div>
      </div>
      <div class="menu">
        <div class="menu-title">通用</div>
        <div class="menu-item">
          <div class="menu-icon" style="mask-image: url('data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 width=%2224%22 height=%2224%22 viewBox=%220 0 24 24%22 fill=%22none%22 stroke=%22currentColor%22 stroke-width=%222%22 stroke-linecap=%22round%22 stroke-linejoin=%22round%22><path d=%22m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z%22/><polyline points=%229 22 9 12 15 12 15 22%22/></svg>');"></div>
          首页
        </div>
        <div class="menu-item">
          <div class="menu-icon" style="mask-image: url('data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 width=%2224%22 height=%2224%22 viewBox=%220 0 24 24%22 fill=%22none%22 stroke=%22currentColor%22 stroke-width=%222%22 stroke-linecap=%22round%22 stroke-linejoin=%22round%22><path d=%22M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2%22></path><rect x=%228%22 y=%222%22 width=%228%22 height=%224%22 rx=%221%22 ry=%221%22></rect></svg>');"></div>
          待办事项
        </div>
        <div class="menu-title">项目</div>
        <div class="menu-item">
          <div class="menu-icon" style="mask-image: url('data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 width=%2224%22 height=%2224%22 viewBox=%220 0 24 24%22 fill=%22none%22 stroke=%22currentColor%22 stroke-width=%222%22 stroke-linecap=%22round%22 stroke-linejoin=%22round%22><path d=%22M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5l2 3h9a2 2 0 0 1 2 2z%22></path></svg>');"></div>
          项目管理
        </div>
        <div class="menu-item">
          <div class="menu-icon" style="mask-image: url('data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 width=%2224%22 height=%2224%22 viewBox=%220 0 24 24%22 fill=%22none%22 stroke=%22currentColor%22 stroke-width=%222%22 stroke-linecap=%22round%22 stroke-linejoin=%22round%22><path d=%22M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z%22></path><polyline points=%2214 2 14 8 20 8%22></polyline><line x1=%2216%22 y1=%2213%22 x2=%228%22 y2=%2213%22></line><line x1=%2216%22 y1=%2217%22 x2=%228%22 y2=%2217%22></line><polyline points=%2210 9 9 9 8 9%22></polyline></svg>');"></div>
          需求管理
        </div>
        <div class="menu-item active">
          <div class="menu-icon" style="mask-image: url('data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 width=%2224%22 height=%2224%22 viewBox=%220 0 24 24%22 fill=%22none%22 stroke=%22currentColor%22 stroke-width=%222%22 stroke-linecap=%22round%22 stroke-linejoin=%22round%22><polyline points=%2221 8 21 21 3 21 3 8%22></polyline><rect x=%221%22 y=%223%22 width=%2222%22 height=%225%22></rect><line x1=%2210%22 y1=%2212%22 x2=%2214%22 y2=%2212%22></line></svg>');"></div>
          合并请求
        </div>
        <div class="menu-title">测试</div>
        <div class="menu-item">
          <div class="menu-icon" style="mask-image: url('data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 width=%2224%22 height=%2224%22 viewBox=%220 0 24 24%22 fill=%22none%22 stroke=%22currentColor%22 stroke-width=%222%22 stroke-linecap=%22round%22 stroke-linejoin=%22round%22><path d=%22M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z%22></path><polyline points=%2214 2 14 8 20 8%22></polyline><path d=%22M16 13.58A2 2 0 0 0 15.3 12a2 2 0 0 0 .7-1.58A2 2 0 0 0 14 8.5h-6v8h6a2 2 0 0 0 2-1.92A2 2 0 0 0 16 13.58Z%22></path></svg>');"></div>
          测试用例
        </div>
        <div class="menu-title">系统</div>
        <div class="menu-item">
          <div class="menu-icon" style="mask-image: url('data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 width=%2224%22 height=%2224%22 viewBox=%220 0 24 24%22 fill=%22none%22 stroke=%22currentColor%22 stroke-width=%222%22 stroke-linecap=%22round%22 stroke-linejoin=%22round%22><path d=%22M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2%22></path><circle cx=%2212%22 cy=%227%22 r=%224%22></circle></svg>');"></div>
          用户管理
        </div>
      </div>
    </div>
    <div class="main">
      <div class="header">
        <div class="page-title">合并请求</div>
        <div class="user-info">
          <div class="avatar">管</div>
          <div class="username">管理员</div>
        </div>
      </div>
      <div class="content">
        <div class="tabs">
          <div class="tab active">待处理 <span class="tab-badge">3</span></div>
          <div class="tab">已合并</div>
          <div class="tab">已关闭</div>
          <div class="tab">全部</div>
        </div>
        
        <div class="toolbar">
          <div class="toolbar-left">
            <div class="search-box">
              <svg class="search-icon" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="11" cy="11" r="8"></circle><line x1="21" y1="21" x2="16.65" y2="16.65"></line></svg>
              <input type="text" class="search-input" placeholder="搜索合并请求">
            </div>
            <select class="select">
              <option>全部项目</option>
              <option>用户中心</option>
              <option>支付系统</option>
              <option>消息服务</option>
            </select>
          </div>
          <button class="btn btn-primary">
            <span class="btn-icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="12" y1="5" x2="12" y2="19"></line><line x1="5" y1="12" x2="19" y2="12"></line></svg>
            </span>
            创建需求合并请求
          </button>
        </div>
        
        <!-- 需求1：用户注册功能 -->
        <div class="mr-group">
          需求: [用户中心] 实现基于手机号的用户注册功能 (REQ-2023-001)
          <span class="mr-group-badge">2个分支待合并</span>
          <button class="btn btn-outline" style="margin-left: auto; height: 30px; font-size: 13px;">一键合并所有分支</button>
        </div>
        
        <div class="card mr-card">
          <div class="mr-info">
            <h3 class="mr-title">
              <a href="#">前端注册页面与表单验证 (#23)</a>
            </h3>
            <div class="mr-meta">
              <div class="meta-item">
                <svg class="meta-icon" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5l2 3h9a2 2 0 0 1 2 2z"></path></svg>
                项目: 用户中心
              </div>
              <div class="meta-item">
                <svg class="meta-icon" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path><circle cx="12" cy="10" r="3"></circle></svg>
                feature/phone-register-ui → develop
              </div>
              <div class="meta-item">
                <svg class="meta-icon" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><polyline points="12 6 12 12 16 14"></polyline></svg>
                3小时前
              </div>
              <div class="meta-item">
                <svg class="meta-icon" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path><circle cx="12" cy="7" r="4"></circle></svg>
                张三
              </div>
              <span class="badge badge-blue">待审核</span>
            </div>
            <p class="mr-desc">实现了手机号注册页面UI，包括输入框、验证码获取按钮、表单验证等，增加了自适应布局。</p>
          </div>
          <div class="mr-actions">
            <button class="btn btn-outline">查看</button>
            <button class="btn btn-primary">审核</button>
          </div>
        </div>
        
        <div class="card mr-card">
          <div class="mr-info">
            <h3 class="mr-title">
              <a href="#">后端手机验证API实现 (#24)</a>
            </h3>
            <div class="mr-meta">
              <div class="meta-item">
                <svg class="meta-icon" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5l2 3h9a2 2 0 0 1 2 2z"></path></svg>
                项目: 用户中心
              </div>
              <div class="meta-item">
                <svg class="meta-icon" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path><circle cx="12" cy="10" r="3"></circle></svg>
                feature/phone-register-api → develop
              </div>
              <div class="meta-item">
                <svg class="meta-icon" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><polyline points="12 6 12 12 16 14"></polyline></svg>
                5小时前
              </div>
              <div class="meta-item">
                <svg class="meta-icon" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path><circle cx="12" cy="7" r="4"></circle></svg>
                李四
              </div>
              <span class="badge badge-blue">待审核</span>
            </div>
            <p class="mr-desc">增加了手机号验证码发送与验证逻辑。更新了用户表结构，添加了手机号字段。集成了短信服务API。</p>
            
            <div class="auto-delete-notice">
              <svg class="notice-icon" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z"></path><path d="m9 12 2 2 4-4"></path></svg>
              所有分支合并完成后将自动删除远程分支
            </div>
          </div>
          <div class="mr-actions">
            <button class="btn btn-outline">查看</button>
            <button class="btn btn-primary">审核</button>
          </div>
        </div>
        
        <!-- 需求2：微信支付功能 -->
        <div class="mr-group">
          需求: [支付系统] 添加微信支付功能 (REQ-2023-017)
          <span class="mr-group-badge">3个分支待合并</span>
          <button class="btn btn-outline" style="margin-left: auto; height: 30px; font-size: 13px;">一键合并所有分支</button>
        </div>
        
        <div class="card mr-card">
          <div class="mr-info">
            <h3 class="mr-title">
              <a href="#">微信支付基础API对接 (#17)</a>
            </h3>
            <div class="mr-meta">
              <div class="meta-item">
                <svg class="meta-icon" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5l2 3h9a2 2 0 0 1 2 2z"></path></svg>
                项目: 支付系统
              </div>
              <div class="meta-item">
                <svg class="meta-icon" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path><circle cx="12" cy="10" r="3"></circle></svg>
                feature/wechat-pay-core → master
              </div>
              <div class="meta-item">
                <svg class="meta-icon" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><polyline points="12 6 12 12 16 14"></polyline></svg>
                昨天
              </div>
              <div class="meta-item">
                <svg class="meta-icon" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path><circle cx="12" cy="7" r="4"></circle></svg>
                王五
              </div>
              <span class="badge badge-orange">有冲突</span>
            </div>
            <p class="mr-desc">接入微信支付核心API，实现了基础支付功能、签名验证和参数配置。</p>
            
            <div class="branch-list">
              <div class="branch-item">
                <svg class="branch-icon" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M6 3v12"></path><circle cx="18" cy="6" r="3"></circle><circle cx="6" cy="18" r="3"></circle><path d="M18 9a9 9 0 0 1-9 9"></path></svg>
                相关分支: feature/wechat-pay-jsapi → master (#18)
              </div>
              <div class="branch-item">
                <svg class="branch-icon" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M6 3v12"></path><circle cx="18" cy="6" r="3"></circle><circle cx="6" cy="18" r="3"></circle><path d="M18 9a9 9 0 0 1-9 9"></path></svg>
                相关分支: feature/wechat-pay-refund → master (#19)
              </div>
            </div>
            
            <div class="auto-delete-notice">
              <svg class="notice-icon" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z"></path><path d="m9 12 2 2 4-4"></path></svg>
              所有分支合并完成后将自动删除远程分支
            </div>
          </div>
          <div class="mr-actions">
            <button class="btn btn-outline">查看</button>
            <button class="btn btn-primary">处理冲突</button>
          </div>
        </div>
        
        <!-- 需求3：订单导出功能 -->
        <div class="mr-group">
          需求: [订单管理] 订单导出功能优化 (REQ-2023-031)
          <span class="mr-group-badge">1个分支待合并</span>
        </div>
        
        <div class="card mr-card">
          <div class="mr-info">
            <h3 class="mr-title">
              <a href="#">订单Excel导出与异步处理 (#31)</a>
            </h3>
            <div class="mr-meta">
              <div class="meta-item">
                <svg class="meta-icon" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5l2 3h9a2 2 0 0 1 2 2z"></path></svg>
                项目: 订单管理
              </div>
              <div class="meta-item">
                <svg class="meta-icon" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path><circle cx="12" cy="10" r="3"></circle></svg>
                feature/export-excel → develop
              </div>
              <div class="meta-item">
                <svg class="meta-icon" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><polyline points="12 6 12 12 16 14"></polyline></svg>
                2天前
              </div>
              <div class="meta-item">
                <svg class="meta-icon" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path><circle cx="12" cy="7" r="4"></circle></svg>
                赵六
              </div>
              <span class="badge badge-green">审核通过</span>
            </div>
            <p class="mr-desc">优化了订单导出Excel的功能，支持自定义导出字段和条件筛选，使用异步任务处理大批量导出，提高了性能。</p>
            
            <div class="auto-delete-notice">
              <svg class="notice-icon" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z"></path><path d="m9 12 2 2 4-4"></path></svg>
              分支合并完成后将自动删除远程分支
            </div>
          </div>
          <div class="mr-actions">
            <button class="btn btn-outline">查看</button>
            <button class="btn btn-primary">合并并清理</button>
          </div>
        </div>
        
        <div class="pagination">
          <div class="page-item">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="15 18 9 12 15 6"></polyline></svg>
          </div>
          <div class="page-item active">1</div>
          <div class="page-item">2</div>
          <div class="page-item">3</div>
          <div class="page-item">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="9 18 15 12 9 6"></polyline></svg>
          </div>
        </div>
      </div>
    </div>
  </div>
</body>
</html> 