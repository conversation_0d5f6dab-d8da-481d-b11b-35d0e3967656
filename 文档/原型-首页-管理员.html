<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <title>项目管理系统 - 首页（管理员）</title>
  <style>
    * { margin: 0; padding: 0; box-sizing: border-box; }
    body { 
      font-family: "Microsoft YaHei", -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Arial, sans-serif; 
      background-color: #f5f7fa; 
      color: #222;
      min-height: 100vh;
    }
    .layout {
      display: flex;
      min-height: 100vh;
    }
    .sidebar {
      width: 220px;
      background-color: #fff;
      border-right: 1px solid #e5e6eb;
      box-shadow: 2px 0 8px rgba(0,0,0,0.03);
      position: fixed;
      height: 100vh;
      z-index: 100;
    }
    .sidebar-header {
      height: 60px;
      border-bottom: 1px solid #e5e6eb;
      display: flex;
      align-items: center;
      padding: 0 20px;
    }
    .logo {
      font-size: 18px;
      font-weight: bold;
      color: #1877f2;
    }
    .menu {
      padding: 20px 0;
    }
    .menu-title {
      font-size: 13px;
      color: #999;
      padding: 0 20px;
      margin-bottom: 8px;
    }
    .menu-item {
      height: 46px;
      display: flex;
      align-items: center;
      padding: 0 20px;
      color: #666;
      font-size: 15px;
      margin-bottom: 2px;
      cursor: pointer;
      position: relative;
      transition: all 0.3s;
    }
    .menu-item:hover {
      color: #1877f2;
      background-color: rgba(24, 119, 242, 0.04);
    }
    .menu-item.active {
      color: #1877f2;
      background-color: rgba(24, 119, 242, 0.08);
      font-weight: 500;
    }
    .menu-item.active::before {
      content: '';
      position: absolute;
      left: 0;
      top: 12px;
      bottom: 12px;
      width: 3px;
      background-color: #1877f2;
      border-radius: 0 2px 2px 0;
    }
    .menu-icon {
      width: 20px;
      height: 20px;
      margin-right: 8px;
      background-color: currentColor;
      opacity: 0.6;
      mask-size: cover;
      -webkit-mask-size: cover;
    }
    .main {
      flex: 1;
      margin-left: 220px;
    }
    .header {
      height: 60px;
      background-color: #fff;
      border-bottom: 1px solid #e5e6eb;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 24px;
      position: sticky;
      top: 0;
      z-index: 99;
    }
    .page-title {
      font-size: 18px;
      font-weight: 500;
      color: #222;
    }
    .user-info {
      display: flex;
      align-items: center;
    }
    .avatar {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      background-color: #1877f2;
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 500;
      margin-right: 8px;
    }
    .username {
      font-size: 14px;
      color: #666;
    }
    .content {
      padding: 24px;
    }
    .welcome-card {
      background-color: #fff;
      border-radius: 10px;
      box-shadow: 0 2px 12px rgba(0,0,0,0.04);
      padding: 40px;
      margin-bottom: 24px;
      text-align: center;
    }
    .welcome-title {
      font-size: 28px;
      font-weight: bold;
      color: #222;
      margin-bottom: 16px;
    }
    .welcome-desc {
      font-size: 16px;
      color: #666;
      line-height: 1.6;
      max-width: 600px;
      margin: 0 auto 32px;
    }
    .card-grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 24px;
      margin-top: 40px;
    }
    .home-card {
      background-color: #fff;
      border-radius: 10px;
      box-shadow: 0 2px 12px rgba(0,0,0,0.04);
      padding: 24px;
      text-align: center;
      transition: all 0.3s;
      cursor: pointer;
    }
    .home-card:hover {
      transform: translateY(-4px);
      box-shadow: 0 6px 20px rgba(0,0,0,0.08);
    }
    .card-icon {
      width: 48px;
      height: 48px;
      border-radius: 12px;
      background-color: rgba(24, 119, 242, 0.1);
      color: #1877f2;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto 16px;
    }
    .card-title {
      font-size: 18px;
      font-weight: 500;
      color: #222;
      margin-bottom: 8px;
    }
    .card-desc {
      font-size: 14px;
      color: #666;
      line-height: 1.5;
    }
    .btn {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      height: 40px;
      padding: 0 24px;
      border-radius: 6px;
      font-size: 15px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s;
      border: none;
    }
    .btn-primary {
      background-color: #1877f2;
      color: white;
    }
    .btn-primary:hover {
      background-color: #0d6efd;
    }
    .stats-row {
      display: flex;
      gap: 24px;
      margin-bottom: 30px;
    }
    .stat-card {
      flex: 1;
      background-color: #fff;
      border-radius: 10px;
      box-shadow: 0 2px 12px rgba(0,0,0,0.04);
      padding: 20px;
    }
    .stat-title {
      font-size: 14px;
      color: #666;
      margin-bottom: 8px;
    }
    .stat-value {
      font-size: 24px;
      font-weight: bold;
      color: #1877f2;
    }
    .stat-desc {
      font-size: 13px;
      color: #999;
      margin-top: 4px;
    }
    /* 从待办事项页面引入的样式 */
    .tabs {
      display: flex;
      border-bottom: 1px solid #e5e6eb;
      margin-bottom: 24px;
    }
    .tab {
      padding: 0 20px;
      height: 40px;
      display: flex;
      align-items: center;
      font-size: 15px;
      color: #666;
      position: relative;
      cursor: pointer;
    }
    .tab.active {
      color: #1877f2;
      font-weight: 500;
    }
    .tab.active::after {
      content: '';
      position: absolute;
      left: 20px;
      right: 20px;
      bottom: -1px;
      height: 2px;
      background-color: #1877f2;
      border-radius: 2px 2px 0 0;
    }
    .tab-badge {
      min-width: 18px;
      height: 18px;
      background-color: #1877f2;
      border-radius: 9px;
      color: white;
      font-size: 12px;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      padding: 0 6px;
      margin-left: 8px;
    }
    .task-card {
      background-color: #fff;
      border-radius: 10px;
      box-shadow: 0 2px 12px rgba(0,0,0,0.04);
      margin-bottom: 16px;
      padding: 20px;
      transition: all 0.3s;
    }
    .task-card:hover {
      box-shadow: 0 4px 16px rgba(0,0,0,0.08);
    }
    .task-header {
      display: flex;
      justify-content: space-between;
      margin-bottom: 16px;
    }
    .task-title {
      font-size: 16px;
      font-weight: 500;
      color: #222;
    }
    .task-project {
      font-size: 13px;
      color: #666;
      margin-top: 4px;
    }
    .task-meta {
      display: flex;
      align-items: flex-end;
      flex-direction: column;
    }
    .badge {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      height: 24px;
      padding: 0 10px;
      border-radius: 12px;
      font-size: 13px;
      font-weight: 500;
    }
    .badge-blue {
      background-color: rgba(24, 119, 242, 0.1);
      color: #1877f2;
    }
    .badge-orange {
      background-color: rgba(245, 166, 35, 0.1);
      color: #f5a623;
    }
    .badge-purple {
      background-color: rgba(114, 46, 209, 0.1);
      color: #722ed1;
    }
    .task-desc-detail {
      font-size: 14px;
      color: #666;
      margin-bottom: 16px;
      line-height: 1.5;
    }
    .task-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-top: 16px;
      border-top: 1px solid #e5e6eb;
    }
    .task-date {
      font-size: 13px;
      color: #999;
      display: flex;
      align-items: center;
    }
    .task-date-icon {
      width: 16px;
      height: 16px;
      margin-right: 4px;
      opacity: 0.5;
    }
    .task-actions {
      display: flex;
      gap: 8px;
    }
    .user-assign {
      font-size: 13px;
      color: #666;
      display: flex;
      align-items: center;
      margin-top: 4px;
    }
    .user-assign-icon {
      width: 16px;
      height: 16px;
      margin-right: 4px;
      opacity: 0.7;
    }
    .filter-row {
      display: flex;
      gap: 16px;
      margin-bottom: 24px;
      margin-top: 16px; /* Added margin top for spacing */
    }
    .filter-select {
      height: 36px;
      padding: 0 12px;
      border: 1px solid #e5e6eb;
      border-radius: 6px;
      background-color: #fff;
      color: #666;
      font-size: 14px;
      width: 160px;
    }
    .page-section-title {
        font-size: 20px;
        font-weight: 500;
        color: #222;
        margin-bottom: 16px;
        padding-bottom: 8px;
        border-bottom: 1px solid #eee;
    }
  </style>
</head>
<body>
  <div class="layout">
    <div class="sidebar">
      <div class="sidebar-header">
        <div class="logo">项目管理系统</div>
      </div>
      <div class="menu">
        <div class="menu-title">通用</div>
        <div class="menu-item active">
          <div class="menu-icon" style="mask-image: url('data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 width=%2224%22 height=%2224%22 viewBox=%220 0 24 24%22 fill=%22none%22 stroke=%22currentColor%22 stroke-width=%222%22 stroke-linecap=%22round%22 stroke-linejoin=%22round%22><path d=%22m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z%22/><polyline points=%229 22 9 12 15 12 15 22%22/></svg>');"></div>
          首页
        </div>
        <div class="menu-title">项目</div>
        <div class="menu-item">
          <div class="menu-icon" style="mask-image: url('data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 width=%2224%22 height=%2224%22 viewBox=%220 0 24 24%22 fill=%22none%22 stroke=%22currentColor%22 stroke-width=%222%22 stroke-linecap=%22round%22 stroke-linejoin=%22round%22><path d=%22M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5l2 3h9a2 2 0 0 1 2 2z%22></path></svg>');"></div>
          项目管理
        </div>
        <div class="menu-item">
          <div class="menu-icon" style="mask-image: url('data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 width=%2224%22 height=%2224%22 viewBox=%220 0 24 24%22 fill=%22none%22 stroke=%22currentColor%22 stroke-width=%222%22 stroke-linecap=%22round%22 stroke-linejoin=%22round%22><path d=%22M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z%22></path><polyline points=%2214 2 14 8 20 8%22></polyline><line x1=%2216%22 y1=%2213%22 x2=%228%22 y2=%2213%22></line><line x1=%2216%22 y1=%2217%22 x2=%228%22 y2=%2217%22></line><polyline points=%2210 9 9 9 8 9%22></polyline></svg>');"></div>
          需求管理
        </div>
        <div class="menu-item">
          <div class="menu-icon" style="mask-image: url('data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 width=%2224%22 height=%2224%22 viewBox=%220 0 24 24%22 fill=%22none%22 stroke=%22currentColor%22 stroke-width=%222%22 stroke-linecap=%22round%22 stroke-linejoin=%22round%22><polyline points=%2221 8 21 21 3 21 3 8%22></polyline><rect x=%221%22 y=%223%22 width=%2222%22 height=%225%22></rect><line x1=%2210%22 y1=%2212%22 x2=%2214%22 y2=%2212%22></line></svg>');"></div>
          合并请求
        </div>
        <div class="menu-title">测试</div>
        <div class="menu-item">
          <div class="menu-icon" style="mask-image: url('data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 width=%2224%22 height=%2224%22 viewBox=%220 0 24 24%22 fill=%22none%22 stroke=%22currentColor%22 stroke-width=%222%22 stroke-linecap=%22round%22 stroke-linejoin=%22round%22><path d=%22M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z%22></path><polyline points=%2214 2 14 8 20 8%22></polyline><path d=%22M16 13.58A2 2 0 0 0 15.3 12a2 2 0 0 0 .7-1.58A2 2 0 0 0 14 8.5h-6v8h6a2 2 0 0 0 2-1.92A2 2 0 0 0 16 13.58Z%22></path></svg>');"></div>
          测试用例
        </div>
        <div class="menu-title">系统</div>
        <div class="menu-item">
          <div class="menu-icon" style="mask-image: url('data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 width=%2224%22 height=%2224%22 viewBox=%220 0 24 24%22 fill=%22none%22 stroke=%22currentColor%22 stroke-width=%222%22 stroke-linecap=%22round%22 stroke-linejoin=%22round%22><path d=%22M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2%22></path><circle cx=%2212%22 cy=%227%22 r=%224%22></circle></svg>');"></div>
          用户管理
        </div>
      </div>
    </div>
    <div class="main">
      <div class="header">
        <div class="page-title">管理员工作台</div>
        <div class="user-info">
          <div class="avatar">管</div>
          <div class="username">管理员</div>
        </div>
      </div>
      <div class="content">
        <div class="stats-row">
          <div class="stat-card">
            <div class="stat-title">用户总数</div>
            <div class="stat-value">24</div>
            <div class="stat-desc">近7天新增3人</div>
          </div>
          <div class="stat-card">
            <div class="stat-title">项目总数</div>
            <div class="stat-value">12</div>
            <div class="stat-desc">活跃项目8个</div>
          </div>
          <div class="stat-card">
            <div class="stat-title">待审核PR</div>
            <div class="stat-value">15</div>
            <div class="stat-desc">今日新增5个</div>
          </div>
          <div class="stat-card">
            <div class="stat-title">待处理需求</div>
            <div class="stat-value">32</div>
            <div class="stat-desc">近7天完成18个</div>
          </div>
        </div>

        <div class="page-section-title">快速操作</div>
        <div class="card-grid" style="margin-top:0; margin-bottom: 24px;">
          <div class="home-card">
            <div class="card-icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5l2 3h9a2 2 0 0 1 2 2z"></path></svg>
            </div>
            <h3 class="card-title">项目管理</h3>
            <p class="card-desc">创建、管理项目，分配成员</p>
          </div>
          <div class="home-card">
            <div class="card-icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path><circle cx="12" cy="7" r="4"></circle></svg>
            </div>
            <h3 class="card-title">用户管理</h3>
            <p class="card-desc">添加、编辑用户，分配角色</p>
          </div>
          <div class="home-card">
            <div class="card-icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="21 8 21 21 3 21 3 8"></polyline><rect x="1" y="3" width="22" height="5"></rect><line x1="10" y1="12" x2="14" y2="12"></line></svg>
            </div>
            <h3 class="card-title">PR批量审核</h3>
            <p class="card-desc">批量审核、合并PR</p>
          </div>
        </div>

        <div class="page-section-title">系统待处理任务</div>
        <div class="filter-row">
            <select class="filter-select">
              <option value="">所有项目</option>
              <option value="user">用户中心</option>
              <option value="payment">支付系统</option>
              <option value="message">消息服务</option>
            </select>
            <select class="filter-select">
              <option value="">所有类型</option>
              <option value="pr">待审核PR</option>
              <option value="requirement">待分配需求</option>
              <option value="bug">待处理Bug</option>
            </select>
            <select class="filter-select">
                <option value="">所有状态</option>
                <option value="pending">待分配</option>
                <option value="dev">开发中</option>
                <option value="test">待测试</option>
              </select>
          </div>
          
          <div class="tabs">
            <div class="tab active">全部待处理 <span class="tab-badge">8</span></div>
            <div class="tab">我创建的 <span class="tab-badge">4</span></div>
            <div class="tab">已逾期 <span class="tab-badge">2</span></div>
          </div>
          
          <div class="task-card">
            <div class="task-header">
              <div>
                <div class="task-title">用户中心API性能优化</div>
                <div class="task-project">项目：用户中心</div>
                <div class="user-assign">
                  <svg class="user-assign-icon" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path><circle cx="12" cy="7" r="4"></circle></svg>
                  负责人：未分配
                </div>
              </div>
              <div class="task-meta">
                <span class="badge badge-purple">待分配需求</span>
                <div style="margin-top: 8px; font-size: 13px; color: #999;">截止日期：2023-06-30</div>
              </div>
            </div>
            <div class="task-desc-detail">
              优化用户中心API接口性能，减少响应时间，提高并发处理能力。重点优化用户查询、权限校验等高频接口，并完善缓存策略。
            </div>
            <div class="task-footer">
              <div class="task-date">
                <svg class="task-date-icon" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><polyline points="12 6 12 12 16 14"></polyline></svg>
                创建于 2023-05-25 by 管理员
              </div>
              <div class="task-actions">
                <button class="btn btn-outline">分配开发</button>
                <button class="btn btn-primary">查看详情</button>
              </div>
            </div>
          </div>
          
          <div class="task-card">
            <div class="task-header">
              <div>
                <div class="task-title">支付系统-支付宝回调接口待审核PR</div>
                <div class="task-project">项目：支付系统</div>
                <div class="user-assign">
                  <svg class="user-assign-icon" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path><circle cx="12" cy="7" r="4"></circle></svg>
                  开发者：张小明
                </div>
              </div>
              <div class="task-meta">
                <span class="badge badge-orange">待审核PR</span>
                <div style="margin-top: 8px; font-size: 13px; color: #999;">提交日期：2023-06-18</div>
              </div>
            </div>
            <div class="task-desc-detail">
              PR#123: 实现支付宝支付回调接口，处理支付成功/失败的业务逻辑，更新订单状态，发送通知等。
            </div>
            <div class="task-footer">
              <div class="task-date">
                <svg class="task-date-icon" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><polyline points="12 6 12 12 16 14"></polyline></svg>
                提交于 2023-06-18
              </div>
              <div class="task-actions">
                <button class="btn btn-outline">拒绝</button>
                <button class="btn btn-primary">合并PR</button>
              </div>
            </div>
          </div>

      </div>
    </div>
  </div>
</body>
</html> 