#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import time
import json

class QYWXClient:
    def __init__(self, corpid, corpsecret):
        self.corpid = corpid
        self.corpsecret = corpsecret
        self.access_token = None
        self.token_expiry = 0
        self.token_cache_duration = 7000  # 缓存时间，单位秒，略低于实际过期时间7200秒

    def get_token(self, force_refresh=False):
        """获取企业微信token，支持缓存机制"""
        current_time = time.time()
        if not force_refresh and self.access_token and current_time < self.token_expiry:
            return self.access_token
        
        try:
            response = requests.get(f"https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid={self.corpid}&corpsecret={self.corpsecret}")
            token_data = response.json()
            if token_data.get('errcode') == 0:
                self.access_token = token_data.get('access_token')
                self.token_expiry = current_time + self.token_cache_duration
                print(f"获取企业微信Token成功: {self.access_token}, 过期时间: {self.token_expiry}")
                return self.access_token
            else:
                print(f"获取企业微信Token失败: {token_data}")
                return None
        except Exception as e:
            print(f"获取企业微信Token时出错: {str(e)}")
            return None

    def send_notification(self, user_id, agentid, content):
        """发送通知给指定用户"""
        access_token = self.get_token()
        if not access_token:
            print("无法发送通知: 没有有效的access_token")
            return False
        
        try:
            message_data = {
                "touser": user_id,
                "msgtype": "text",
                "agentid": agentid,
                "text": {
                    "content": content
                }
            }
            response = requests.post(
                f"https://qyapi.weixin.qq.com/cgi-bin/message/send?access_token={access_token}",
                json=message_data
            )
            result = response.json()
            if result.get('errcode') == 0:
                print(f"成功通知用户 {user_id}")
                return True
            else:
                print(f"通知用户 {user_id} 失败: {result}")
                return False
        except Exception as e:
            print(f"通知用户 {user_id} 时出错: {str(e)}")
            return False