#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import json
import argparse
import time
import threading
import requests
from qywx import QYWXClient
from datetime import datetime
import git
from git import Repo


def format_date(timestamp):
    """将时间戳格式化为易读的日期时间格式"""
    dt = datetime.fromtimestamp(timestamp)
    return dt.strftime('%Y-%m-%d %H:%M:%S')


def get_all_branches(repo_path):
    """获取仓库中的所有分支（本地和远程）"""
    try:
        # 打开仓库
        repo = Repo(repo_path)
        
        # 检查是否是有效的Git仓库
        if not repo.bare:
            # 获取所有本地分支
            local_branches = [branch.name for branch in repo.branches]
            
            # 获取所有远程分支
            remote_branches = []
            for remote in repo.remotes:
                for ref in remote.refs:
                    # 去掉远程名称前缀
                    branch_name = ref.name.split('/', 1)[1] if '/' in ref.name else ref.name
                    remote_branches.append(f"{remote.name}/{branch_name}")
            
            return local_branches, remote_branches
        else:
            print(f"错误: {repo_path} 是一个裸仓库")
            return [], []
    except git.exc.InvalidGitRepositoryError:
        print(f"错误: {repo_path} 不是一个有效的Git仓库")
        return [], []
    except Exception as e:
        print(f"获取分支时出错: {str(e)}")
        return [], []


def get_branch_commits(repo_path, branch_name):
    """获取指定分支的提交历史"""
    try:
        repo = Repo(repo_path)
        commits = []
        
        # 处理远程分支名称
        if '/' in branch_name:
            # 对于远程分支，我们需要使用完整的引用名称
            ref_name = branch_name
        else:
            # 对于本地分支，直接使用分支名称
            ref_name = branch_name
        
        try:
            # 获取分支的提交历史
            for commit in repo.iter_commits(ref_name, max_count=50):  # 限制为最近50个提交
                # 获取提交中修改的文件列表
                files = []
                for file_path in commit.stats.files.keys():
                    # 获取文件的完整路径
                    full_path = os.path.join(repo_path, file_path)
                    files.append(full_path)
                
                commits.append({
                    'hash': commit.hexsha,
                    'author': f"{commit.author.name} <{commit.author.email}>",
                    'date': format_date(commit.committed_date),
                    'message': commit.message.strip(),
                    'files': files
                })
            return commits
        except git.exc.GitCommandError:
            print(f"警告: 无法获取分支 '{branch_name}' 的提交历史")
            return []
    except Exception as e:
        print(f"获取分支 '{branch_name}' 的提交历史时出错: {str(e)}")
        return []


def print_commit_history(branch_name, commits):
    """打印分支的提交历史"""
    if not commits:
        print(f"\n分支 '{branch_name}' 没有找到提交记录或无法访问")
        return
    
    print(f"\n===== 分支 '{branch_name}' 的提交历史 (最近 {len(commits)} 条) =====")
    for i, commit in enumerate(commits, 1):
        print(f"\n提交 #{i}:")
        print(f"  哈希值: {commit['hash']}")
        print(f"  作者: {commit['author']}")
        print(f"  日期: {commit['date']}")
        print(f"  消息: {commit['message']}")


def scan_git_blame(repo_path, file_path):
    """使用git blame获取文件的每一行的作者信息"""
    try:
        repo = Repo(repo_path)
        if not os.path.exists(os.path.join(repo_path, file_path)):
            print(f"错误: 文件 '{file_path}' 不存在")
            return
        
        print(f"\n===== 文件 '{file_path}' 的 Git Blame 信息 =====")
        blame = repo.git.blame(file_path, '--date=short', '-l').split('\n')
        
        current_commit = None
        for line in blame:
            if line.startswith('^'):
                # 这是一个新的提交信息行
                parts = line.split('(', 1)
                if len(parts) > 1:
                    commit_info = parts[1].split(')', 1)[0]
                    current_commit = commit_info
            else:
                # 这是一个代码行
                if current_commit:
                    print(f"{current_commit}: {line.strip()}")
    except Exception as e:
        print(f"执行git blame时出错: {str(e)}")


def load_config(config_path='config.json'):
    """从配置文件加载仓库路径和分支白名单"""
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        return config
    except Exception as e:
        print(f"加载配置文件时出错: {str(e)}")
        return None


def save_config(config, config_path='config.json'):
    """保存配置到文件"""
    try:
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        return True
    except Exception as e:
        print(f"保存配置文件时出错: {str(e)}")
        return False


def scan_branches(repo_path, branch_whitelist, config=None, config_path='config.json', output_file=None):
    """扫描指定分支的提交历史，只显示新的提交"""
    # 获取所有分支
    _, remote_branches = get_all_branches(repo_path)
    
    # 过滤分支（如果有白名单）
    if branch_whitelist:
        remote_branches = [b for b in remote_branches if any(b.endswith('/' + wb) for wb in branch_whitelist)]
    
    # 确保配置中有last_commit_hashes字段
    if config is None:
        config = load_config(config_path)
    if 'last_commit_hashes' not in config:
        config['last_commit_hashes'] = {}
    
    # 获取并打印每个远程分支的新提交历史
    for branch in remote_branches:
        commits = get_branch_commits(repo_path, branch)
        if commits:
            # 获取该分支上次记录的提交哈希值
            last_hash = config['last_commit_hashes'].get(branch)
            
            # 如果是第一次扫描该分支，直接记录最新的提交哈希值
            if not last_hash and commits:
                config['last_commit_hashes'][branch] = commits[0]['hash']
                continue
            
            # 找出新的提交
            new_commits = []
            for commit in commits:
                if last_hash and commit['hash'] == last_hash:
                    break
                new_commits.append(commit)
            
            # 生成并输出JSON
            if new_commits:
                json_output_data = {
                    "repository": repo_path,
                    "branch": branch,
                    "new_commits": [
                        {
                            "hash": commit['hash'],
                            "author": commit['author'],
                            "date": commit['date'],
                            "message": commit['message'],
                            "files": commit['files']
                        } for commit in new_commits
                    ],
                    "last_hash": new_commits[0]['hash'] if new_commits else None
                }

                output = json.dumps(json_output_data, ensure_ascii=False, indent=2)
                if output_file:
                    with open(output_file, 'a') as f:
                        f.write(output + '\n')
                else:
                    print(output)
                
                # 使用QYWXClient处理企业微信相关操作
                weixin_config = config.get('weixin', {})
                corpid = weixin_config.get('corpid')
                corpsecret = weixin_config.get('corpsecret')
                if corpid and corpsecret:
                    if not hasattr(scan_branches, 'qywx_client'):
                        scan_branches.qywx_client = QYWXClient(corpid, corpsecret)
                    
                    notify_users(weixin_config, new_commits, branch)
                
                # 更新配置中的最后提交哈希值
                if new_commits:
                    config['last_commit_hashes'][branch] = new_commits[0]['hash']
    
    # 保存更新后的配置
    save_config(config, config_path)
    return config


def poll_branches(repo_path, branch_whitelist, interval=10, config_path='config.json'):
    """定时轮询指定分支的提交历史，只显示新的提交"""
    print(f"\n===== 开始轮询分支提交历史 (每{interval}秒一次) =====\n")
    
    # 加载配置文件
    config = load_config(config_path)
    if config is None:
        print("错误: 无法加载配置文件，程序退出")
        return
        
    if 'last_commit_hashes' not in config:
        config['last_commit_hashes'] = {}
    
    # 初始化企业微信客户端
    weixin_config = config.get('weixin', {})
    corpid = weixin_config.get('corpid')
    corpsecret = weixin_config.get('corpsecret')
    if corpid and corpsecret:
        if not hasattr(scan_branches, 'qywx_client'):
            scan_branches.qywx_client = QYWXClient(corpid, corpsecret)
    
    print(f"开始轮询循环...")
    try:
        while True:
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            print(f"\n[{current_time}] 执行分支扫描...")
            # 传递配置对象给scan_branches函数
            config = scan_branches(repo_path, branch_whitelist, config, config_path)
            # 刷新token
            if hasattr(scan_branches, 'qywx_client'):
                scan_branches.qywx_client.get_token(force_refresh=True)
                print(f"[{current_time}] 已刷新企业微信Token")
            print(f"\n[{current_time}] 扫描完成，等待{interval}秒后重新扫描...")
            time.sleep(interval)
    except KeyboardInterrupt:
        print("\n轮询已停止")
        # 确保在中断时保存配置
        save_config(config, config_path)


def main():
    parser = argparse.ArgumentParser(description='扫描Git仓库的分支和提交历史')
    parser.add_argument('--repo-path', help='Git仓库的路径（覆盖配置文件中的设置）')
    parser.add_argument('--blame', help='对指定文件执行git blame（相对于仓库根目录的路径）')
    parser.add_argument('--config', default='config.json', help='配置文件路径（默认为config.json）')
    parser.add_argument('--output-file', help='指定JSON输出文件路径')
    parser.add_argument('--interval', type=int, default=10, help='轮询间隔时间（秒），默认为10秒')
    args = parser.parse_args()
    
    # 加载配置文件
    config = load_config(args.config)
    if not config:
        print("错误: 无法加载配置文件，请确保config.json文件存在且格式正确")
        sys.exit(1)
    
    # 命令行参数优先于配置文件
    repo_path = os.path.abspath(args.repo_path) if args.repo_path else os.path.abspath(config.get('repo_path', ''))
    branch_whitelist = config.get('branch_whitelist', [])
    
    print(f"使用仓库路径: {repo_path}")
    if not repo_path or not os.path.exists(repo_path):
        print(f"错误: 路径 '{repo_path}' 不存在或未指定")
        sys.exit(1)
    
    print(f"检查仓库路径是否为有效Git仓库...")
    try:
        repo = Repo(repo_path)
        if repo.bare:
            print(f"错误: {repo_path} 是一个裸仓库")
            sys.exit(1)
        print(f"仓库路径有效，继续执行...")
    except git.exc.InvalidGitRepositoryError:
        print(f"错误: {repo_path} 不是一个有效的Git仓库")
        sys.exit(1)
    except Exception as e:
        print(f"检查仓库时出错: {str(e)}")
        sys.exit(1)
    
    # 如果指定了文件，执行git blame
    if args.blame:
        scan_git_blame(repo_path, args.blame)
        return
        
    print(f"开始轮询模式，间隔时间: {args.interval}秒")
    print(f"分支白名单: {branch_whitelist}")
    # 直接使用轮询模式
    poll_branches(repo_path, branch_whitelist, args.interval, args.config)
    print("轮询模式结束")
        
def notify_users(weixin_config, new_commits, branch):
    """通知用户有新的提交，包含详细信息"""
    notice_user_ids = weixin_config.get('notice_user_ids', [])
    agentid = weixin_config.get('agentid', 1000002)
    
    for user_id in notice_user_ids:
        for commit in new_commits:
            content = f"分支 {branch} 有新的提交:\n"
            content += f"哈希值: {commit['hash']}\n"
            content += f"作者: {commit['author']}\n"
            content += f"日期: {commit['date']}\n"
            content += f"消息: {commit['message']}"
            if scan_branches.qywx_client.send_notification(user_id, agentid, content):
                print(f"成功通知用户 {user_id} 关于提交 {commit['hash'][:7]}")
            else:
                print(f"通知用户 {user_id} 关于提交 {commit['hash'][:7]} 失败")


if __name__ == '__main__':
    main()