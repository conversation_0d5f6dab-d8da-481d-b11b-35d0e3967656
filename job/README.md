# Git 仓库分支扫描工具

这个Python脚本可以帮助您获取Git仓库的所有分支信息（本地和远程）以及查看所有分支的提交历史记录。此外，它还支持使用git blame功能查看特定文件的每一行代码的作者信息。

## 功能特点

- 列出仓库中的所有本地分支
- 列出仓库中的所有远程分支
- 显示每个分支的详细提交历史（最近100条）
- 支持对特定文件执行git blame操作

## 安装依赖

在使用此脚本之前，您需要安装GitPython库：

```bash
pip install gitpython
```

## 配置文件

项目使用`config.json`配置文件来定义需要监控的Git仓库路径和分支白名单：

```json
{
  "repo_path": "/path/to/your/git/repository",
  "branch_whitelist": [
    "main",
    "develop",
    "feature/example"
  ]
}
```

- `repo_path`: 指定要监控的Git仓库路径
- `branch_whitelist`: 字符串数组，定义需要监听的分支白名单

## 使用方法

### 使用配置文件查看分支的提交历史

```bash
python git_branch_scanner.py
```

脚本将启动轮询模式，定时扫描分支的提交历史。


### 覆盖配置文件中的仓库路径

```bash
python git_branch_scanner.py --repo-path /path/to/your/git/repository
```

### 对特定文件执行git blame

```bash
python git_branch_scanner.py /path/to/your/git/repository --blame path/to/file
```

注意：
- `--blame`参数后面的文件路径应该是相对于仓库根目录的路径
- 可以通过`--config`参数指定自定义配置文件路径：`python git_branch_scanner.py --config custom_config.json`

## 输出示例

```
===== 仓库信息 =====
仓库路径: /path/to/your/git/repository

本地分支数量: 2
- main
- develop

远程分支数量: 3
- origin/main
- origin/develop
- origin/feature/new-feature

===== 分支 'main' 的提交历史 (最近 5 条) =====

提交 #1:
  哈希值: a1b2c3d (a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0)
  作者: 张三 <<EMAIL>>
  日期: 2023-05-15 14:30:45
  消息: 修复登录功能的bug

提交 #2:
  ...
```

## 注意事项

- 该脚本默认最多显示每个分支的最近100条提交记录
- 需要确保您有足够的权限访问指定的Git仓库
- 对于大型仓库，获取所有分支的提交历史可能需要一些时间
- 脚本将启动轮询模式，定时扫描分支的提交历史
- 可以通过命令行参数 `--interval` 调整轮询间隔时间
- 使用 Ctrl+C 可以停止轮询