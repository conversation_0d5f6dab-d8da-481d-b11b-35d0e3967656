# [v0.0.5] - 2025-05-26

## Features
- 优化需求列表的开发人员和测试人员的信息
- 顶部子菜单调整
- 重新调整了需求列表的UI样式

## Bug Fixes
- 修复非管理人员在首页查看需求列表的时候，测试人员的遗漏显示
- 修复页面显示的时间的问题
- 修复修改密码的功能

# [v0.0.4] - 2025-05-21

## 修复内容（Bug Fixes）
- 修复了SQLAlchemy版本兼容性问题，确保无论使用哪个版本都能正常工作
- 修复了测试用例列表分页问题
- 修复了需求列表分页问题

# [v0.0.3] - 2025-05-21

## 修复内容（Bug Fixes）
- 修复了需求列表页面顶部菜单消失的问题
- 修复了需求列表的搜索条件不生效和滚动条问题
- 修复了需求详情页面开发人员显示不正确的问题
- 修复了需求编辑页面开发人员选择不生效的问题

---

# [v0.0.2] - 2025-05-21

## 新增功能（New Features）
- 添加了发布docker的脚本

---

# [v0.0.1] - 2025-05-20

## 新增功能（New Features）
- 初始化项目结构
- 添加了登录功能
- 实现了基本的权限控制
- 开发了项目管理模块
- 完成了需求管理模块的初步开发
- 集成了测试用例管理功能
- 添加了用户与权限管理模块
- 开发了代码仓库管理功能
- 实现了需求与代码仓库的关联