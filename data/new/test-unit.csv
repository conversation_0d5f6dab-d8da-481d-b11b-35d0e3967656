﻿测试功能模块,测试编号,测试名称,测试类型,代码类型,前提条件,测试步骤,预期结果,关联JUnit测试类,测试状态,测试人员,测试完成时间
登录功能,UC-LOGIN-001,使用正确的用户名、密码、验证码进行登录,功能测试,Junit,无,进入系统首页，使用coralreef / password进行登录,能够登录进系统,无,PASS,陶文亮,20250411
登录功能,UC-LOGIN-002,使用不存在的用户进行登录时应该返回用户不存在的错误,异常测试,Junit,无,进入系统首页，一个不存在的用户进行登录操作,后端返回码为500，错误信息“登录用户：xxx 不存在”,"SysLoginControllerTest#testLoginWithWrongVerifyCode
SysLoginServiceTest#testLoginWithWrongVerifyCode",PASS,陶文亮,20250411
登录功能,UC-LOGIN-003,使用错误的验证码登录时应该返回验证码错误,异常测试,Junit,无,进入系统首页，登录时输入错误的验证码,后端返回码为500，错误信息“验证码错误”,"SysLoginControllerTest#testLoginWithWrongCredentials
SysLoginServiceTest#testLoginWithWrongCredentials",PASS,陶文亮,20250411
数据链接,UC-DATASOURCE-001,查询列表正常情况，应该返回数据列表,功能测试,Junit,无,进入数据链接,能够正常返回数据链接列表,DataSourceControllerTest#testGetListSuccess,PASS,黄艳锋,20250507
数据链接,UC-DATASOURCE-002,使用存在的关键词进行搜索，应该返回非空列表,功能测试,Junit,数据链接关键词存在,进入数据链接，输入关键词并点击搜索,搜索成功,DataSourceControllerTest#testFilterBySearchValue,PASS,钟慧静,20250410
数据链接,UC-DATASOURCE-003,使用不存在的关键词进行搜索，应该返回空列表,功能测试,Junit,数据连接不存在该关键词,进入数据链接，输入关键词并点击搜索,搜索成功，并返回空列表,DataSourceControllerTest#testGetListEmpty,PASS,黄艳锋,20250507
数据链接,UC-DATASOURCE-004,使用不正确的父路径查询应该返回空列表,功能测试,Junit,无,前端非法篡改写死的父路径parentPath进行查询列表,搜索成功，并返回空列表,DataSourceControllerTest#testGetListWithInvalidParentPath,PASS,黄艳锋,20250507
数据链接,UC-DATASOURCE-005,父路径为空时查询列表应该返回异常,异常测试,Junit,无,前端非法清空写死的父路径parentPath进行查询列表,后端返回码500，以及错误信息,DataSourceControllerTest#testGetListWithEmptyParentPath,PASS,黄艳锋,20250507
数据链接,UC-DATASOURCE-006,删除一个存在的正式版的数据源应该返回操作成功,功能测试,Junit,数据链接存在,"1.进入数据链接页面
2.点击正式版数据链接对应的删除按钮",删除成功,DataSourceControllerTest#testDeleteDataSource,PASS,黄艳锋,20250507
数据链接,UC-DATASOURCE-007,删除一个存在的草稿版的数据源应该返回操作成功,功能测试,Junit,数据链接存在,"1.进入数据链接页面
2.点击数据链接对应的删除按钮草稿本",删除成功,DataSourceControllerTest#testDeleteDraftDataSource,PASS,黄艳锋,20250507
数据链接,UC-DATASOURCE-008,删除一个不存在的数据源应该返回操作失败的错误,异常测试,Junit,数据链接不存在,调用接口删除一个不存在的数据连接,删除不成功,DataSourceControllerTest#testDeleteNonExistentDataSource,FAIL,黄艳锋,20250507
数据链接,UC-DATASOURCE-009,删除一个不存在状态的数据源应该返回操作失败的错误,异常测试,Junit,无,调用接口删除一个错误状态的数据链接,删除不成功,DataSourceControllerTest#testDeleteDataSourceWithInvalidStatus,PASS,黄艳锋,20250507
数据链接,UC-DATASOURCE-010,复制一个存在的正式版的数据链接应该返回操作成功,功能测试,Junit,数据连接存在,进入数据链接页面，点击一个正式版数据链接的复制按钮,复制成功,DataSourceControllerTest#testCopyDataSource,PASS,黄艳锋,20250508
数据链接,UC-DATASOURCE-011,复制一个存在的草稿版的数据源应该返回操作成功,功能测试,Junit,数据连接存在,进入数据链接页面，点击一个草稿版数据链接的复制按钮,复制成功,DataSourceControllerTest#testCopyDraftDataSource,PASS,黄艳锋,20250508
数据链接,UC-DATASOURCE-012,复制一个不存在的数据链接应该返回复制失败的错误信息,异常测试,Junit,数据链接不存在,调用接口复制一个不存在的数据链接,复制不成功,DataSourceControllerTest#testCopyNonExistentDataSource,PASS,黄艳锋,20250508
数据链接,UC-DATASOURCE-013,复制一个不正确版本号的数据源应该返回复制失败的错误,异常测试,Junit,数据连接存在,调用接口复制一个存在的数据源，版本号改不正确,复制不成功,DataSourceControllerTest#testCopyIncorrectVersionDataSource,PASS,黄艳锋,20250508
数据链接,UC-DATASOURCE-014,复制一个不正确ModuleType的数据源应该返回复制失败的错误,异常测试,Junit,数据连接存在,调用接口复制一个存在的数据源，ModuleType改不正确,复制不成功,DataSourceControllerTest#testCopyIncorrectModuleTypeDataSource,PASS,黄艳锋,20250508
数据链接,UC-DATASOURCE-015,复制一个不正确父级路径的数据源应该返回操作成功,异常测试,Junit,数据连接存在,调用接口复制一个存在的数据源，父级路径改不正确,复制成功,DataSourceControllerTest#testCopyIncorrectParentPathDataSource,PASS,黄艳锋,20250508
数据链接,UC-DATASOURCE-016,正常获取文件版本号应该返回文件版本号列表,功能测试,Junit,文件存在,进入数据链接，选中文件，点击导出,获取版本号列表成功,DataSourceControllerTest#testGetObjectVersionInfoByList,PASS,黄艳锋,20250508
数据链接,UC-DATASOURCE-017,获取文件版本号列表时没有传入数据GUID应该返回错误信息,异常测试,Junit,无,调用接口时不传GUID,后端返回码为500，错误信息：CoralReef接口异常，异常信息为：CoralReef接口异常，异常信息为：The given key 'list' was not present in the dictionary.,DataSourceControllerTest#testGetObjectVersionInfoByListWithoutGuid,PASS,黄艳锋,20250508
数据链接,UC-DATASOURCE-018,获取文件版本号列表时没有传入模块类型应该返回报错信息,异常测试,Junit,无,调用接口时，不传入模块类型,后端返回码为501，错误信息：CoralReef接口异常，异常信息为：The given key 'moduleType' was not present in the dictionary.,DataSourceControllerTest#testGetObjectVersionInfoByListWithoutModuleType,PASS,黄艳锋,20250508
数据链接,UC-DATASOURCE-019,导出存在的正式版文件应该返回成功的响应,功能测试,Junit,数据链接存在,进入数据链接页面，选中一个正式版数据链接，点击导出按钮，弹出导出窗口，选择导出版本，再点击导出按钮导出文件,导出成功,DataSourceControllerTest#testExportDataSource,PASS,黄艳锋,20250512
数据链接,UC-DATASOURCE-020,导出存在的草稿版文件应该返回成功的响应,功能测试,Junit,数据链接存在,进入数据链接页面，选中一个草稿版数据链接，点击导出按钮，弹出导出窗口，选择导出版本，再点击导出按钮导出文件,导出成功,DataSourceControllerTest#testExportDraftDataSource,PASS,黄艳锋,20250512
数据链接,UC-DATASOURCE-021,导出不存在的数据连接应该返回失败的响应,异常测试,Junit,数据链接不存在,调用接口，填写不存在的数据链接objectGuid,导出不成功,DataSourceControllerTest#testExportNotExistDataSource,FAIL,黄艳锋,20250512
数据链接,UC-DATASOURCE-022,使用导入数据链接返回操作成功,功能测试,Junit,无,进入数据连接，点击导入,导入成功,DataSourceControllerTest#importDataSource,PASS,钟慧静,20250410
数据链接,UC-DATASOURCE-023,使用相同的链接导入返回接口异常,异常测试,Junit,文件夹内有相同的数据链接,进入数据连接，点击导入,接口异常,DataSourceControllerTest#testgetObjectsByImportValueRepeat,PASS,钟慧静,20250422
数据链接,UC-DATASOURCE-024,使用错误的链接导入返回接口异常,异常测试,Junit,错误链接,进入数据连接，点击导入,接口异常,"DataSourceControllerTest#testgetObjectsByImportValueRepeatException
DataSourceServiceTest#testgetObjectsByImportValueRepeatException",PASS,钟慧静,20250424
数据链接,UC-DATASOURCE-025,数据链接输入关键字进行搜索,功能测试,Cypress,输入有效的关键字,"1.进入数据链接页面
2.输入关键字
3.点击查询",显示搜索内容,dataProcess.cy.js,PASS,钟慧静,20250507
数据链接,UC-DATASOURCE-026,数据链接页面重置搜索,功能测试,Cypress,无,"1.进入数据链接页面
2.输入关键字
3.点击重置",重置内容,dataProcess.cy.js,PASS,钟慧静,20250507
数据链接,UC-DATASOURCE-027,新增重复的数据名称,异常测试,Cypress,存在重复名称,"1.进入数据链接页面
2.点击新增
3.输入重复数据连接名称",提示重复名称,dataProcess.cy.js,PASS,钟慧静,20250507
数据链接,UC-DATASOURCE-028,新增空的数据名称,异常测试,Cypress,无,"1.进入数据链接页面
2.点击新增
3.不输入名称",提示请输入名称,dataProcess.cy.js,PASS,钟慧静,20250507
数据链接,UC-DATASOURCE-029,新增数据链接,功能测试,Cypress,无,"1.进入数据链接页面
2.点击新增
3.输入数据连接名称",添加成功,dataProcess.cy.js,PASS,钟慧静,20250507
数据链接,UC-DATASOURCE-030,复制数据链接,功能测试,Cypress,无,"1.进入数据链接页面
2.点击复制",复制成功,dataProcess.cy.js,PASS,钟慧静,20250507
数据链接,UC-DATASOURCE-031,数据链接授权,功能测试,Cypress,无,"1.进入数据链接页面
2.点击授权",授权成功,dataProcess.cy.js,PASS,钟慧静,20250507
数据链接,UC-DATASOURCE-032,删除数据链接,功能测试,Cypress,无,"1.进入数据链接页面
2.点击删除",删除成功,dataProcess.cy.js,PASS,钟慧静,20250507
数据链接,UC-DATASOURCE-033,导出数据链接,功能测试,Cypress,无,"1.进入数据链接页面
2.点击数据连接
3.点击导出",导出成功,dataProcess.cy.js,PASS,钟慧静,20250507
指标热度,UC-INDHEAT-01,指标热度页面进行搜索,功能测试,Cypress,无,"1.进入指标热度页面
2.输入关键词
3.点击搜索",搜索成功,indHeat.cy.js,PASS,黄艳锋,20250512
指标热度,UC-INDHEAT-02,指标热度页面进行重置,功能测试,Cypress,无,"1.进入指标热度页面
2.输入关键词搜索后
3.点击重置按钮进行重置",重置成功,indHeat.cy.js,PASS,黄艳锋,20250513
指标热度,UC-INDHEAT-03,指标热度页面进行查看,功能测试,Cypress,无,"1.进入指标热度页面
2.点击任意一条数据的查看按钮",页面弹窗指标热度的查看弹窗,indHeat.cy.js,PASS,黄艳锋,20250513
用户组管理,UC-OBJECTAUTHUSER-01,用户组管理页面进行搜索,功能测试,Cypress,无,"1.进入用户组管理页面
2.输入关键词
3.点击搜索",搜索成功,objectAuthUser.cy.js,PASS,黄艳锋,20250513
用户组管理,UC-OBJECTAUTHUSER-02,用户组管理页面进行重置,功能测试,Cypress,无,"1.进入用户组管理页面
2.输入关键词搜索看
3.点击重置按钮",重置成功,objectAuthUser.cy.js,PASS,黄艳锋,20250513
指标管理,UC-INDICATOR-001,使用存在的关键字进行条件查询应该返回操作成功,功能测试,Junit,无,进入指标管理，输入关键信息并点击搜索,搜索成功,IndicatorControllerTest#testlist,PASS,钟慧静,20250411
指标管理,UC-INDICATOR-002,使用指标类型返回操作成功,功能测试,Junit,无,进入指标管理页面自动触发,操作成功,IndicatorControllerTest#testfilterInfoList,PASS,钟慧静,20250411
指标管理,UC-INDICATOR-003,使用灰度发布模型应该返回操作成功,功能测试,Junit,无,进入指标管理页面，点击灰度发布，对指标进行发布,发布成功,IndicatorControllerTest#testpublishIndInfo,PASS,钟慧静,20250411
指标管理,UC-INDICATOR-004,已发布模型编辑返回操作成功,功能测试,Junit,无,进入指标管理页面，点击编辑按钮，对指标进行编辑,操作成功,IndicatorControllerTest#testeditIndInfo,PASS,钟慧静,20250411
指标管理,UC-INDICATOR-005,使用存在的指标数据查看返回操作成功,功能测试,Junit,无,进入指标管理页面，点击编辑按钮，对指标进行编辑,操作成功,IndicatorControllerTest#testgetSameName,PASS,钟慧静,20250415
指标管理,UC-INDICATOR-006,获取指标所属者返回操作成功,功能测试,Junit,无,进入指标管理页面，点击编辑按钮，对指标进行编辑,操作成功,IndicatorControllerTest#testgetUserInfo,PASS,钟慧静,20250415
指标管理,UC-INDICATOR-007,定义派生指标的获取数据名返回操作成功,功能测试,Junit,无,进入指标管理页面，选中已发布，选择创建派生指标,操作成功,IndicatorControllerTest#testgetIndOrgName,PASS,钟慧静,20250415
指标管理,UC-INDICATOR-008,获取数据返回操作成功,功能测试,Junit,无,进入指标管理页面自动触发,操作成功,IndicatorControllerTest#testgetQueryCount,PASS,钟慧静,20250415
指标管理,UC-INDICATOR-009,获取指标数据返回操作成功,功能测试,Junit,无,进入指标管理页面，点击指标状态灰度发布，点击查看按钮,操作成功,IndicatorControllerTest#testgetIndicatorData,PASS,钟慧静,20250415
指标管理,UC-INDICATOR-010,获取指标数据返回操作成功,功能测试,Junit,无,进入指标管理页面，点击指标状态灰度发布，点击查看按钮,操作成功,IndicatorControllerTest#testgetIndInfo,PASS,钟慧静,20250416
指标管理,UC-INDICATOR-011,获取指标当前时间返回查询成功,功能测试,Junit,无,进入指标管理页面，点击指标状态灰度发布，点击查看按钮,查询成功,IndicatorControllerTest#testgetIndCardData,PASS,钟慧静,20250416
指标管理,UC-INDICATOR-012,获取指标信息返回操作成功,功能测试,Junit,无,进入指标管理页面，点击指标状态灰度发布，点击查看按钮,查询成功,IndicatorControllerTest#testgetObjectMapping,PASS,钟慧静,20250416
指标管理,UC-INDICATOR-013,获取所有指标名称返回操作成功,功能测试,Junit,无,进入指标管理页面，点击指标状态灰度发布,查询成功,IndicatorControllerTest#testgetAllDir,PASS,钟慧静,20250416
指标管理,UC-INDICATOR-014,查询指示符父路径返回操作成功,功能测试,Junit,无,进入指标管理页面，点击指标状态灰度发布，点击查看按钮,查询成功,IndicatorControllerTest#testqueryIndicatorParentPath,PASS,钟慧静,20250416
指标管理,UC-INDICATOR-015,定义衍生指标返回操作成功,功能测试,Junit,无,进入指标管理页面，选中已发布，选择创建衍生指标,操作成功,IndicatorControllerTest#testsaveSql,PASS,钟慧静,20250416
指标管理,UC-INDICATOR-016,异常设置返回操作成功,功能测试,Junit,无,进入指标管理页面，选中已发布，选择设置异常,操作成功,IndicatorControllerTest#testsetIndExceptionTrue,PASS,钟慧静,20250416
指标管理,UC-INDICATOR-017,取消异常设置返回操作成功,功能测试,Junit,无,进入指标管理页面，选中已发布，选择取消设置异常,操作成功,IndicatorControllerTest#testsetIndExceptionFalse,PASS,钟慧静,20250416
指标管理,UC-INDICATOR-018,指标默认时间返回操作成功,功能测试,Junit,无,进入指标管理页面，选中已发布，选择查看按钮,操作成功,IndicatorControllerTest#testgetDefaultDataDate,PASS,钟慧静,20250415
指标管理,UC-INDICATOR-019,预览组合指标的值返回操作成功,功能测试,Junit,无,进入指标管理页面，选中已发布，选择查看按钮,操作成功,IndicatorControllerTest#testpreviewCombiningIndValue,PASS,钟慧静,20250415
指标管理,UC-INDICATOR-020,修改的指标名称与某指标相同返回存在重复名称,异常测试,Junit,无,进入指标管理页面，点击编辑按钮，对指标进行编辑,存在重复名称,IndicatorControllerTest#testgetSameNameException,PASS,钟慧静,20250423
指标管理,UC-INDICATOR-021,对已发布的衍生指标进行编辑，查看指标信息返回操作成功,功能测试,Junit,无,进入指标管理页面，选中已发布，选择创建衍生指标，选择查看,操作成功,IndicatorControllerTest#testgetGroupsList,PASS,钟慧静,20250424
指标管理,UC-INDICATOR-022,预览创建的衍生指标,功能测试,Junit,无,进入指标管理页面，选中已发布，选择创建衍生指标，选择预览,操作成功,IndicatorControllerTest#testpreviewDerivativesIndValue,PASS,钟慧静,20250424
指标管理,UC-INDICATOR-023,创建派生指标的设计逻辑返回操作成功,功能测试,Junit,无,进入指标管理页面，选中已发布，选择创建派生指标，点击设计逻辑按钮,操作成功,IndicatorControllerTest#testqueryFilterData,PASS,钟慧静,20250423
指标管理,UC-INDICATOR-024,创建衍生指标获取类别返回操作成功,功能测试,Junit,无,进入指标管理页面，选中已发布，选择创建衍生指标,操作成功,IndicatorControllerTest#testgetTypes,PASS,钟慧静,20250423
指标管理,UC-INDICATOR-025,已发布指标停用返回操作成功,功能测试,Junit,无,进入指标管理页面，选中已发布，选择停用,操作成功,IndicatorControllerTest#getObjectMappingR,PASS,钟慧静,20250423
指标管理,UC-INDICATOR-026,预览派生指标逻辑设置,功能测试,Junit,无,进入指标管理页面，选中已发布，选择创建衍生指标，选择预览,操作成功,IndicatorControllerTest#testpreviewDeriveIndValue,PASS,钟慧静,20250424
指标管理,UC-INDICATOR-027,预览错误逻辑的派生指标,异常测试,Junit,错误的逻辑,进入指标管理页面，选中已发布，选择创建衍生指标，选择预览,CoralReef接口异常,IndicatorControllerTest#testpreviewDeriveIndValueException,PASS,钟慧静,20250424
指标管理,UC-INDICATOR-028,批量指标发布返回操作成功,功能测试,Junit,无,进入指标管理页面，点击批量发布,操作成功,IndicatorControllerTest#testpublishBatchIndInfo,PASS,钟慧静,20250427
指标管理,UC-INDICATOR-029,指标管理输入关键信息进行搜索,功能测试,Cypress,无,进入指标管理，输入关键信息并点击搜索,操作成功,indicatorManage.cy.js,PASS,钟慧静,20250507
指标管理,UC-INDICATOR-030,指标管理输入关键信息进行重置,功能测试,Cypress,无,进入指标管理，输入关键信息并点击重置,操作成功,indicatorManage.cy.js,PASS,钟慧静,20250507
指标管理,UC-INDICATOR-031,编辑指标名称，重复,异常测试,Cypress,存在重复名称,进入指标管理页面，点击编辑按钮，对指标进行编辑,存在重复名称,indicatorManage.cy.js,PASS,钟慧静,20250507
指标管理,UC-INDICATOR-032,编辑指标名称,功能测试,Cypress,无,进入指标管理页面，点击编辑按钮，对指标进行编辑,操作成功,indicatorManage.cy.js,PASS,钟慧静,20250507
指标管理,UC-INDICATOR-033,灰度发布,功能测试,Cypress,无,进入指标管理页面，点击灰度发布，对指标进行发布,操作成功,indicatorManage.cy.js,PASS,钟慧静，黄艳锋,20250507，20250515
指标管理,UC-INDICATOR-034,对灰度发布的指标查看,功能测试,Cypress,无,进入指标管理页面，选中已发布，选择查看按钮,操作成功,indicatorManage.cy.js,PASS,钟慧静,20250507
指标管理,UC-INDICATOR-035,授权,功能测试,Cypress,无,进入指标管理页面，选中已发布，选择授权,操作成功,indicatorManage.cy.js,PASS,钟慧静,20250507
指标管理,UC-INDICATOR-036,已发布设置异常,功能测试,Cypress,无,进入指标管理页面，选中已发布，选择设置异常,操作成功,indicatorManage.cy.js,PASS,钟慧静,20250507
指标管理,UC-INDICATOR-037,创建组合指标功能,功能测试,Cypress,无,进入指标管理页面，选中已发布，选择创建组合指标,操作成功,indicatorManage.cy.js,PASS,钟慧静,20250507
指标管理,UC-INDICATOR-038,创建衍生指标,功能测试,Cypress,无,进入指标管理页面，选中已发布，选择创建衍生指标,操作成功,indicatorManage.cy.js,PASS,钟慧静,20250507
指标管理,UC-INDICATOR-039,血缘分析功能,功能测试,Cypress,无,进入指标管理页面，选中已发布，选择血缘分析,操作成功,indicatorManage.cy.js,PASS,钟慧静,20250519
指标管理,UC-INDICATOR-040,存在血缘指标无法停用,异常测试,Cypress,无,进入指标管理页面，选中已发布，选择停用,指标存在血缘关系,indicatorManage.cy.js,PASS,钟慧静,20250519
指标管理,UC-INDICATOR-041,停用功能,功能测试,Cypress,无,进入指标管理页面，选中已发布，选择停用,停用成功,indicatorManage.cy.js,PASS,钟慧静,20250519
指标评价,UC-INDIEVALUATION-001,查询指标后评价列表返回成功,功能测试,Junit,无,进入指标评价页面自动触发,操作成功,IndEvaluationControllerTest#testlist,PASS,钟慧静,20250421
指标评价,UC-INDIEVALUATION-002,获取指标后评价详细信息返回操作成功,功能测试,Junit,无,进入指标评价页面自动触发,新增成功,IndEvaluationControllerTest#testgetInfo,PASS,钟慧静,20250421
指标评价,UC-INDIEVALUATION-003,新增指标后评价,功能测试,Junit,无,进入指标评价页面，点击后评价，点击新增,反馈成功,IndEvaluationControllerTest#testadd,PASS,钟慧静,20250421
指标评价,UC-INDIEVALUATION-004,后评价反馈返回操作成功,功能测试,Junit,无,进入指标评价页面，点击后评价，点击反馈,操作成功,IndEvaluationControllerTest#testedit,PASS,钟慧静,20250421
指标评价,UC-INDIEVALUATION-005,获取指标后评价汇总信息返回操作成功 ,功能测试,Junit,无,进入指标评价页面，点击后评价,操作成功,IndEvaluationControllerTest#testGetInfo,PASS,钟慧静,20250421
指标评价,UC-INDIEVALUATION-006,不存在的后评价反馈返回操作失败,异常测试,Junit,无,进入指标评价页面，点击后评价，点击反馈,操作失败,"IndEvaluationControllerTest#testeditException
IndEvaluationServiceTest#testupdateIndEvaluation",PASS,钟慧静,20250427
指标评价,UC-INDIEVALUATION-007,删除指标后评价,功能测试,Junit,无,无,操作成功,IndEvaluationControllerTest#testremove,PASS,钟慧静,20250427
指标评价,UC-INDIEVALUATION-008,删除不存在的指标后评价,异常测试,Junit,无,无,操作失败,IndEvaluationControllerTest#testremoveExceptionIndEvaluationServiceTest#deleteIndEvaluationByEvaIds,PASS,钟慧静,20250427
指标评价,UC-INDIEVALUATION-009,导出指标后评价列表,功能测试,Junit,无,无,导出成功,IndEvaluationControllerTest#testexport,PASS,钟慧静,20250427
审批管理,UC-APPROVE-001,切换到待我填写意见页面返回操作成功,功能测试,Junit,无,进入审批管理页面，切换到待我填写意见页面,操作成功,ApproveControllerTest#testtoMyApproveList,PASS,钟慧静,20250424
审批管理,UC-APPROVE-002,切换到待我审批返回操作成功,功能测试,Junit,无,进入审批管理页面，切换到待我审批,操作成功,ApproveControllerTest#testtoMyApplyApproveTaskList,PASS,钟慧静,20250424
审批管理,UC-APPROVE-003,待我审批查询任务的子列表,功能测试,Junit,无,进入审批管理页面，切换到待我审批，点击查看详情按钮,操作成功,ApproveControllerTest#testgetMyApplySub,PASS,钟慧静,20250424
审批管理,UC-APPROVE-004,查询待我填写意见详情部分的上面信息返回查询成功,功能测试,Junit,无,进入审批管理页面，切换到待我填写意见页面，点击填写意见,查询成功,ApproveControllerTest#testgetDetail,PASS,钟慧静,20250424
审批管理,UC-APPROVE-005,查询待我填写意见的查询待我审批的任务的子任务列表,功能测试,Junit,无,进入审批管理页面，切换到待我填写意见页面，点击填写意见,查询成功,ApproveControllerTest#testgetMySub,PASS,钟慧静,20250424
审批管理,UC-APPROVE-006,填写待我填写意见返回操作成功,功能测试,Junit,无,进入审批管理页面，切换到待我填写意见页面，点击填写意见，再次点击填写意见,操作成功,ApproveControllerTest#testupdateOpinion,PASS,钟慧静,20250424
审批管理,UC-APPROVE-007,查询待我审批查看流水返回操作成功,功能测试,Junit,无,进入审批管理页面，切换到待我审批，点击擦查看详情，点击查看流水,操作成功,ApproveControllerTest#testgetWater,PASS,钟慧静,20250424
审批管理,UC-APPROVE-008,查询待我审批通过审批返回操作成功,功能测试,Junit,无,进入审批管理页面，切换到待我审批，点击查看详情，点击审批,操作成功,ApproveControllerTest#testsinglePass,PASS,钟慧静,20250424
审批管理,UC-APPROVE-009,待我审批通过审批，再次审批,异常测试,Junit,存在已经审批过的信息,进入审批管理页面，切换到待我审批，点击查看详情，点击审批,接口异常,"ApproveControllerTest#testsinglePassException
ApproveServiceImplTest#testrejectApproveSubTaskException",PASS,钟慧静,20250424
审批管理,UC-APPROVE-010,切换到我的申请页面返回操作成功,功能测试,Junit,无,进入审批管理页面，切换到我的申请页面,操作成功,ApproveControllerTest#testgetMyApply,PASS,钟慧静,20250424
审批管理,UC-APPROVE-011,我的申请的查看详情顶部信息返回查询成功,功能测试,Junit,无,进入审批管理页面，切换到我的申请，点击查看详情按钮,查询成功,ApproveControllerTest#testgetMyApplyDetail,PASS,钟慧静,20250424
审批管理,UC-APPROVE-012,我的申请查看流水返回操作成功,功能测试,Junit,无,进入审批管理页面，切换到我的申请，点击查看详情按钮,操作成功,ApproveControllerTest#testgetMyApplyS,PASS,钟慧静,20250424
审批管理,UC-APPROVE-013,再次新增审批功能,异常测试,Junit,存在审批中的任务,进入指标管理页面，对指标进行灰度发布,接口异常,ApproveControllerTest#testsaveApproveException,PASS,钟慧静,20250425
审批管理,UC-APPROVE-014,再次新增审批功能,异常测试,Junit,存在审批中的任务,进入指标管理页面，对指标进行灰度发布,接口异常,ApproveServiceImplTest#testsaveApproveException,PASS,钟慧静,20250425
审批管理,UC-APPROVE-015,新增审批功能,功能测试,Junit,无,进入指标管理页面，对指标进行灰度发布,操作成功,ApproveControllerTest#testsaveApprove,PASS,钟慧静,20250425
审批管理,UC-APPROVE-016,待我审批单个拒绝返回操作成功,功能测试,Junit,无,进入审批管理页面，切换到待我审批，点击查看详情，点击审批，点击拒绝,操作成功,ApproveControllerTest#testsingleReject,PASS,钟慧静,20250425
审批管理,UC-APPROVE-017,再次对待我审批单个拒绝返回操作成功,异常测试,Junit,存在已经审批过的信息,进入审批管理页面，切换到待我审批，点击查看详情，点击审批，点击拒绝,接口异常,"ApproveControllerTest#testsingleRejectException
ApproveServiceImplTest#testagreeApproveSubTaskException",PASS,钟慧静,20250425
审批管理,UC-APPROVE-018,错误的审批信息保存返回接口异常,异常测试,Junit,错误的审批信息,进入审批管理页面，切换到待我审批，点击查看详情，点击审批,接口异常,ApproveControllerTest#testcancelApproveException,PASS,钟慧静,20250425
模型管理,UC-MODEL-001,缺少参数查询模型列表,功能测试,Junit,无,进入模型管理，点击搜索,搜索成功,ModelManageControllerTest#testQueryModels,PASS,林泽芳,20250423
模型管理,UC-MODEL-002,父路径参数为空查询模型列表,异常测试,Junit,父路径参数为空,进入模型管理，点击搜索,后端返回码为500，错误信息：CoralReef接口异常，异常信息为：Object reference not set to an instance of an object.,"ModelManageControllerTest#testNullValueQueryModels
ModelManageServiceTest.java#testNullValueQueryModels",PASS,林泽芳,20250423
模型管理,UC-MODEL-003,获取模型id,异常测试,Junit,物件id不存在,非法传不存在的物件id,后端返回码为500，错误信息CoralReef接口异常，异常信息为：未查找到指定的物件 type:INDMODEL=>8c71648c-03c9-45ff-b30f-9c7a097a8283,"ModelManageControllerTest#testIncorrectModelId
ModelManageServiceTest.java#testNullValueQueryModels",PASS,林泽芳,20250508
模型管理,UC-MODEL-004,模型类型参数为空时获取模型id,异常测试,Junit,模型类型参数为空,非法篡改模型类型置空,后端返回码为500，错误信息:CoralReef接口异常，异常信息为：Object reference not set to an instance of an object.,"ModelManageControllerTest#testNullTypeGetObjectItemByGuidModel
ModelManageServiceTest.java#testNullTypeGetObjectItemByGuidModel",PASS,林泽芳,20250508
模型管理,UC-MODEL-005,模型id参数为空时获取模型id,异常测试,Junit,物件iid为空,非法篡改物件id置空,操作成功,ModelManageControllerTest#testNullGuidObjectItemByGuidModel,PASS,林泽芳,20250508
模型管理,UC-MODEL-006,保存模型应该返回成功响应,功能测试,Junit,无,进入模型管理，新增模型，并进行保存,保存成功,ModelManageControllerTest#testModeAddObjectToDraft,PASS,林泽芳,20250423
模型管理,UC-MODEL-007,更新模型应该返回成功响应,功能测试,Junit,无,进入模型管理，新增模型，修改模型，并进行保存,保存成功,ModelManageControllerTest#testModelUpdateObjectToDraft,PASS,林泽芳,20250425
模型管理,UC-MODEL-008,更新模型维度名称重复,功能测试,Junit,已有重复名字的维度名称,进入模型管理，新增模型，修改模型，修改维度名称,后端返回500，信息报错：存在重复名称:org_id,"ModelManageControllerTest#testModelGetSameName
ModelManageServiceTest.java#testModelGetSameName",PASS,林泽芳,20250425
模型管理,UC-MODEL-009,复制模型应该返回成功响应,功能测试,Junit,无,进入模型管理，点击复制按钮,复制成功,ModelManageControllerTest#testModelCopy,PASS,林泽芳,20250418
模型管理,UC-MODEL-010,复制模型缺少参数,异常测试,Junit,无,进入模型管理，点击复制按钮,后端返回码为500，信息:CoralReef接口异常，异常信息为：复制失败,"ModelManageControllerTest#testModelCopyNoParentPath
ModelManageServiceTest.java#testModelCopyNoParentPath",PASS,林泽芳,20250425
模型管理,UC-MODEL-011,复制不存在的模型,异常测试,Junit,无,进入模型管理，一个不存在的模型id进行删除,后端返回码为500，错误信息:复制失败,"ModelManageControllerTest#testNoModelCopy 
ModelManageServiceTest.java#testNoModelCopy",PASS,林泽芳,20250423
模型管理,UC-MODEL-012,删除不属于自己的目录,异常测试,Junit,无,删除不属于该用户下的目录,后端返回码为500，错误信息:CoralReef接口异常，异常信息为：非目录拥有者，无权限删除目录!,"ModelManageControllerTest#testDeleteDire
ModelManageServiceTest.java#testDeleteDire",PASS,林泽芳,20250423
模型管理,UC-MODEL-013,保存相同模型,异常测试,Junit,有之前名称一样测模型,进入模型管理，新增模型，名称一样，进行保存,后端返回码为500，信息:CoralReef接口异常，异常信息为：添加失败:单元测试111 物件名称已经存在！,"ModelManageControllerTest#testModeAddSameObjectToDraft
",PASS,林泽芳,20250423
模型管理,UC-MODEL-014,删除草稿模型列表应该返回成功响应,功能测试,Junit,无,进入模型管理，点击删除草稿版的模型,删除成功,ModelManageControllerTest#testDeleteDraft,PASS,林泽芳,20250418
模型管理,UC-MODEL-015,删除正式版模型前血缘关系验证应该返回成功响应,功能测试,Junit,无,进入模型管理，点击删除正式版的模版进行血缘关系验证,验证成功,ModelManageControllerTest#testDeleteFormatVerify,PASS,林泽芳,20250418
模型管理,UC-MODEL-016,保存已有的模型进行判断是否提交审批应该返回成功响应,功能测试,Junit,无,进入模型管理，新增模型并进行保存模型验证是否已审批,验证成功,ModelManageControllerTest#testOldModelApprove,PASS,林泽芳,20250418
模型管理,UC-MODEL-017,获取导出的文件或者文件夹应该返回成功响应,功能测试,Junit,无,进入模型管理，选择导出文件，点击导出按钮,获取导出文件成功,ModelManageControllerTest#testModelExportObjects,PASS,林泽芳,20250418
模型管理,UC-MODEL-018,获取不存在导出的文件或者文件夹,异常测试,Junit,路径不存在,进入模型管理，选择导出文件，点击导出按钮,后端返回的数据为空，实际上获取了所有数据,"ModelManageControllerTest#testModelNoExportObjects
ModelManageServiceTest.java#testModelNoExportObjects",FAIL,林泽芳,20250422
模型管理,UC-MODEL-019,获取导出的文件或者文件夹里面的文件版本号应该返回成功响应,功能测试,Junit,无,进入模型管理，对选择的文件或文件夹获取版本,获取文件版本成功,ModelManageControllerTest#testModelGetObjectVersionInfoByList,PASS,林泽芳,20250418
模型管理,UC-MODEL-020,导出的文件应该返回成功响应,功能测试,Junit,无,进入模型管理，对选择的文件进行导出,导出文件成功,ModelManageControllerTest#testModelExport,PASS,林泽芳,20250418
模型管理,UC-MODEL-021,解析文件应该返回成功响应,功能测试,Junit,无,进入模型管理，点击导入文件，对文件进行解析,文件解析成功 ,ModelManageControllerTest#testModelFindObject,PASS,林泽芳,20250418
模型管理,UC-MODEL-022,解析错误文件,异常测试,Junit,上传一个错误的文件,进入模型管理，点击导入文件，对文件进行解析,"后端返回码为500，错误信息:CoralReef接口异常，异常信息为：The input is not a valid Base-64 string as it contains a non-base 64 character, more than two padding characters, or an illegal character among the padding characters.","ModelManageControllerTest#testModelNoFindObject
ModelManageServiceTest.java#testModelNoFindObject",PASS,林泽芳,20250515
模型管理,UC-MODEL-023,导入重复的文件,异常测试,Junit,无,进入模型管理，点击导入文件并对已有的文件重新导入,"后端返回码为500，错误信息:“CoralReef接口异常，异常信息为：物件导入失败,已存在1个物件重名”","ModelManageControllerTest#testModelRepeatImport
ModelManageServiceTest.java#testModelRepeatImport",PASS,林泽芳,20250418
模型管理,UC-MODEL-024,导入文件模型-参数不对,异常测试,Junit,参数和objectID与文件对应不上,进入模型管理，点击导入文件并对新文件重新导入,"后端返回码为500，错误信息:CoralReef接口异常，异常信息为：42601: syntax error at or near "")""","ModelManageControllerTest#testModelImport
ModelManageServiceTest.java#testModelImport",PASS,林泽芳,20250418
模型管理,UC-MODEL-025,创建目录应返回成功的响应,功能测试,Junit,无,进入模型管理，点击新增目录,新增目录成功,ModelManageControllerTest#testModelCreateFolder,PASS,林泽芳,20250418
模型管理,UC-MODEL-026,新增相同的目录,异常测试,Junit,已存在相同名称的目录,进入模型管理，点击新增目录，输入相同名称,后端返回码为500，错误信息:CoralReef接口异常，异常信息为：添加失败，同级目录下不允许有相同名称存在！,"ModelManageControllerTest#testModelCreateSameFolder
ModelManageServiceTest.java#testModelCreateSameFolder",PASS,林泽芳,20250422
模型管理,UC-MODEL-027,更新目录应返回成功的响应,功能测试,Junit,无,进入模型管理，选择目录的编辑按钮,更新目录成功,ModelManageControllerTest#testModelUpdateFolder,PASS,林泽芳,20250418
模型管理,UC-MODEL-028,删除目录应返回成功的响应,功能测试,Junit,无,进入模型管理，选择目录的删除按钮,删除目录成功,,PASS,林泽芳,20250424
模型管理,UC-MODEL-029,加载目录列表应返回成功的响应,功能测试,Junit,无,进入模型管理,加载文件夹成功,ModelManageControllerTest#testModelGetAllFolder,PASS,林泽芳,20250418
模型管理,UC-MODEL-030,加载维度管理列表应返回成功的响应,功能测试,Junit,无,进入模型管理，点击维度管理按钮,显示维度数据成功,ModelManageControllerTest#testModelQueryModelDimLvl,PASS,林泽芳,20250418
模型管理,UC-MODEL-031,加载维度管理树应返回成功的响应,功能测试,Junit,无,进入模型管理，点击维度管理按钮，选择添加维度,操作成功,ModelManageControllerTest#testModelQueryPfsDimInfo,PASS,林泽芳,20250418
模型管理,UC-MODEL-032,新增维度应返回成功的响应,功能测试,Junit,无,进入模型管理，点击维度管理按钮，选择添加维度，并进行提交,新增维度成功,ModelManageControllerTest#testModelAddModelDimLvl,PASS,林泽芳,20250418
模型管理,UC-MODEL-033,查询模型管理列表当前页审批通过可进行预计算返回成功的响应,功能测试,Junit,无,进入模型管理，是否显示预计算按钮,操作成功,ModelManageControllerTest#testModelGetApprovingStatus,PASS,林泽芳,20250418
模型管理,UC-MODEL-034,模型管理预计算发布作业返回成功的响应,功能测试,Junit,无,进入模型管理，选择预计算按钮，点击发布作业,发布成功,ModelManageControllerTest#testModelUpdateFolder,PASS,林泽芳,20250418
模型管理,UC-MODEL-035,模型管理预计算发布作业缺少参数,异常测试,Junit,无,进入模型管理，选择预计算按钮，点击发布作业,后端返回码为500，错误信息：null,"ModelManageControllerTest#testModelApproveSaveNoNUllType
ModelManageServiceTest.java#testModelApproveSaveNoNUllType",PASS,林泽芳,20250424
模型管理,UC-MODEL-036,模型发布未审批，重新编辑模型并保存发布返回成功的响应,功能测试,Junit,无,进入模型管理，选择正式版本的模型，重新保存并发布,后端返回码为500，信息“存在审批中的任务，待审批通过再操作“,ModelManageControllerTest#testModelApproveRelodSave,PASS,林泽芳,20250418
模型管理,UC-MODEL-037,删除已发布未审批的模型,异常测试,Junit,模型已经发布未审批,进入模型管理，选择正式版本的模型，重新保存并发布进入审批后，选择删除按钮,后端返回码为500，信息“存在审批中的任务，待审批通过再操作“,"ModelManageControllerTest#testDeletePublishNotApproved
ModelManageServiceTest.java#testDeletePublishNotApproved",PASS,林泽芳,20250418
模型管理,UC-MODEL-038,已发布未审批的模型重新保存判断是否审核,异常测试,Junit,模型已经发布未审批,进入模型管理，选择正式版本的模型，重新保存并发布进入审批后,后端返回码为500，信息“存在审批中的任务，待审批通过再操作“,"ModelManageControllerTest#testModelIsApprove
ModelManageServiceTest.java#testModelIsApprove",PASS,林泽芳,20250424
模型管理,UC-MODEL-039,模型管理授权获取机构数返回成功的响应,功能测试,Junit,无,进入模型管理，选择授权/批量授权功能,获取成功,ModelManageControllerTest#testModelFindDeptByDeptId,PASS,林泽芳,20250418
模型管理,UC-MODEL-040,模型管理授权按照用户组授权返回成功的响应,功能测试,Junit,无,"进入模型管理，选择授权/批量授权功能,选择用户组授权",操作成功,ModelManageControllerTest#testModelAddObjectsAuthorize,PASS,林泽芳,20250418
模型管理,UC-MODEL-041,批量导出模型，文件只生成一个,功能测试,Junit,无,进入模型管理，选择多个模型进行导出,操作成功,model.cy.js,PASS,林泽芳,20250425
模型管理,UC-MODEL-042,批量授权模型，只选择用户,功能测试,Junit,不选择数据管理员,进入模型管理，选择授权/批量授权功能,被授权的用户有编辑模型功能（授权的时候请进行提示：需要选择数据管理员）,model.cy.js,PASS,林泽芳,20250425
模型管理,UC-MODEL-043,新增模型的时候，无聚合函数,功能测试,Junit,时间和机构维度正常,进入模型管理，进行新增模型并进行发布,前端提示：存在无聚合函数,model.cy.js,PASS,林泽芳,20250425
模型管理,UC-MODEL-044,修改维度名称,异常测试,Junit,参数都是小写,进入模型管理，编辑模型管理，修改维度名称,后端返回码为500，信息“存在审批中的任务，待审批通过再操作“,"ModelManageControllerTest#testModelGetSameName
ModelManageServiceTest.java#testModelGetSameName",PASS,林泽芳,20250425
模型管理,UC-MODEL-045,模型授权后进行编辑并发布，审批拒绝应退回原来的模型,功能测试,Cypress,模型正式版，被授权的用户进行编辑,模型管理授权正式版的给其他用户，其他用户进行编辑并发布，经过审批，审批拒绝,审批拒绝应该返回原来的模型,model.cy.js,PASS,林泽芳,20250425
模型管理,UC-MODEL-046,模型管理页面进行搜索,功能测试,Cypress,无,"1.进入模型管理页面
2.输入关键词
3.点击搜索",操作成功,model.cy.js,PASS,林泽芳,20250507
模型管理,UC-MODEL-047,模型管理页面进行重置,功能测试,Cypress,无,"1.进入模型管理页面
2.输入关键词
3.点击重置",操作成功,model.cy.js,PASS,林泽芳,20250507
模型管理,UC-MODEL-048,模型管理页面新增目录,功能测试,Cypress,无,"1.进入模型管理页面
2.点击新增目录
3.添加目录名称
4.点击确定",操作成功,model.cy.js,PASS,林泽芳,20250507
模型管理,UC-MODEL-049,模型管理页面新增目录不填写名称，直接点击确定按钮应阻止提交并提示错误,功能测试,Cypress,无,"1.进入模型管理页面
2.点击新增目录
3.点击确定",输入框提示：请输入目录名称,model.cy.js,PASS,林泽芳,20250507
模型管理,UC-MODEL-050,创建同名目录时显示报错,功能测试,Cypress,已有test目录这个目录,"1.进入模型管理页面
2.点击新增目录
3.输入test名称
4.点击确定",页面报错提示：CoralReef接口异常，异常信息为：添加失败，同级目录下不允许有相同名称存在！,model.cy.js,PASS,林泽芳,20250507
模型管理,UC-MODEL-051,模型管理页面进行搜索目录并进行编辑,功能测试,Cypress,已有test目录这个目录,"1.进入模型管理页面
2.搜索test目录
3.点击编辑
4.修改test名称
5.点击确定",操作成功,model.cy.js,PASS,林泽芳,20250508
模型管理,UC-MODEL-052,模型管理页面目录进行编辑,功能测试,Cypress,已有test目录这个目录,"1.进入模型管理页面
2.点击编辑
3.修改test名称
4.点击确定",操作成功,model.cy.js,PASS,林泽芳,20250508
模型管理,UC-MODEL-053,模型管理页面进行搜索并进行删除,功能测试,Cypress,已有test目录这个目录,"1.进入模型管理页面
2.搜索test目录
3.点击删除
4.点击确定",操作成功,model.cy.js,PASS,林泽芳,20250508
模型管理,UC-MODEL-054,模型管理页面目录内容不为空进行删除,功能测试,Cypress,已有test目录这个目录，且该目录里面是有新目录,"1.进入模型管理页面
2.找到test目录
3.点击删除
4.点击确定","页面报错提示：CoralReef接口异常，异常信息为：当前目录不为空，无权限删除目录!""",model.cy.js,PASS,林泽芳,20250508
模型管理,UC-MODEL-055,模型管理页面目录内容为空进行删除,功能测试,Cypress,已有test目录这个目录，且该目录里面内容为空,"1.进入模型管理页面
2.找到test目录
3.点击删除
4.点击确定",操作成功,model.cy.js,PASS,林泽芳,20250508
模型管理,UC-MODEL-056,导出模型txt文件,功能测试,Cypress,无,"1.进入模型管理页面
2.选择模型
3.点击导出
4.点击确定",操作成功,model.cy.js,PASS,林泽芳,20250508
模型管理,UC-MODEL-057,成功导入模型文件,功能测试,Cypress,无,"1.进入模型管理页面
2.点击导入
3.选择文件
4.点击确定",操作成功,model.cy.js,PASS,林泽芳,20250508
模型管理,UC-MODEL-058,导入重复的模型文件,功能测试,Cypress,已存在测试单元模型文件,"1.进入模型管理页面
2.点击导入
3.选择文件
4.点击确定","页面报错提示：CoralReef接口异常，异常信息为：物件导入失败,已存在1个物件重名",model.cy.js,PASS,林泽芳,20250508
模型管理,UC-MODEL-059,添加维度组不填写名称，直接点击确定按钮应阻止提交并提示错误,功能测试,Cypress,无,"1.进入模型管理页面
2.点击维度管理
3.选择添加维度组
4.点击确定",输入框提示：请输入维度组名称,model.cy.js,PASS,林泽芳,20250508
模型管理,UC-MODEL-060,添加维度不填写名称，直接点击确定按钮应阻止提交并提示错误,功能测试,Cypress,已有单元测试维度组,"1.进入模型管理页面
2.点击维度管理
3.选择单元测试维度组
4.点击添加图标
5.点击确定",输入框提示：请输入维度组名称，请输入维度中文名，请选择维度机构范围,model.cy.js,PASS,林泽芳,20250508
模型管理,UC-MODEL-061,编辑维度填写已有名称，直接点击确定按钮应阻止提交并提示错误,功能测试,Cypress,已有单元测试维度组,"1.进入模型管理页面
2.点击维度管理
3.选择单元测试维度组
4.点击编辑图标
5.点击确定",提示：维度组名称已存在,model.cy.js,PASS,林泽芳,20250508
模型管理,UC-MODEL-062,添加维度组未添加维度，直接点击提交钮应阻止提交并报错,功能测试,Cypress,已有单元测试维度组,"1.进入模型管理页面
2.点击维度管理
3.添加单元测试维度组
4.点击确定",提示：维度组【单元测试】未添加维度,model.cy.js,PASS,林泽芳,20250508
模型管理,UC-MODEL-063,添加维度组添加维度并进行提交,功能测试,Cypress,无,"1.进入模型管理页面
2.点击维度管理
3.添加单元测试维度组
4.新增单元测试维度组的维度
4.点击确定",页面显示：维度提交成功,model.cy.js,PASS,林泽芳,20250508
模型管理,UC-MODEL-064,已存在的维度编辑并提交,功能测试,Cypress,已有单元测试维度组,"1.进入模型管理页面
2.点击维度管理
3.选择单元测试维度组
4.编辑单元测试维度组的维度
5.点击确定",页面显示：维度提交成功,model.cy.js,PASS,林泽芳,20250508
模型管理,UC-MODEL-065,维度组内容进行排序并提交,功能测试,Cypress,已有单元测试维度组,"1.进入模型管理页面
2.点击维度管理
3.选择单元测试维度组
4.编辑单元测试维度组的维度
5.对单元测试维度组里面的维度进行上移和下移
6.点击确定",操作成功,model.cy.js,PASS,林泽芳,20250508
模型管理,UC-MODEL-066,模型管理授权选择按用户授权,功能测试,Cypress,无,"1.进入模型管理页面
2.选择单元测试模型
3.点击授权
4.选择按用户授权
5.点击确定",操作成功,model.cy.js,PASS,林泽芳,20250508
模型管理,UC-MODEL-067,模型管理授权选择按用户组授权,功能测试,Cypress,无,"1.进入模型管理页面
2.选择单元测试模型
3.点击授权
4.选择按用户组授权
5.点击确定",操作成功,model.cy.js,PASS,林泽芳,20250508
模型管理,UC-MODEL-068,模型管理批量授权,功能测试,Cypress,无,"1.进入模型管理页面
2.选择单元测试模型
3.点击批量授权
4.选择按用户授权
5.点击确定",操作成功,model.cy.js,PASS,林泽芳,20250508
模型管理,UC-MODEL-069,模型管理对模型进行复制,功能测试,Cypress,无,"1.进入模型管理页面
2.选择单元测试模型
3.点击复制",操作成功,model.cy.js,PASS,林泽芳,20250508
模型管理,UC-MODEL-070,模型管理新增模型时未设置数据源直接发布，应阻止并提示,功能测试,Cypress,无,"1.进入模型管理页面
2.点击新增模型
3.点击发布",页面提示：请先设置数据源,model.cy.js,PASS,林泽芳,20250513
模型管理,UC-MODEL-071,模型管理新增模型时无机构维度与时间维度直接发布，应阻止并提示,功能测试,Cypress,无,"1.进入模型管理页面
2.点击新增模型
3.选择数据源
4.点击发布",页面报错提示：CoralReef接口异常，异常信息为：机构维度和时间维度都不能为空!,model.cy.js,PASS,林泽芳,20250513
模型管理,UC-MODEL-072,模型管理新增模型时当维度名称为空应阻止并提示错误,功能测试,Cypress,无,"1.进入模型管理页面
2.点击新增模型
3.选择数据源
4.选择表格并添加维度
5.打开维度弹窗，维度名称为空
6.点击确定",输入框：提示请输入字段名,model.cy.js,PASS,林泽芳,20250513
模型管理,UC-MODEL-073,模型管理新增模型并保存时填写已有的名称，应阻止并提示错误,功能测试,Cypress,"存在“cypress-test""模型","1.进入模型管理页面
2.点击新增模型
3.选择数据源
4.选择表格
5.点击发布
6.保存模型时填写已有名称
7.点击确定",页面报错提示：CoralReef接口异常，异常信息为：添加失败:模型设计 物件名称已经存在！,model.cy.js,PASS,林泽芳,20250513
模型管理,UC-MODEL-074,模型管理新增模型并保存无名称，应阻止并提示错误,功能测试,Cypress,无,"1.进入模型管理页面
2.点击新增模型
3.选择数据源
4.选择表格
5.点击发布
6.保存模型时没有填名称
7.点击确定",输入框提示：请输入名称,model.cy.js,PASS,林泽芳,20250513
模型管理,UC-MODEL-075,模型管理新增模型并保存无时间维度，应阻止并提示错误,功能测试,Cypress,已有机构维度,"1.进入模型管理页面
2.点击新增模型
3.选择数据源
4.选择表格并添加维度
5.将维度设置为机构维度
6.点击发布",页面报错提示“CoralReef接口异常，异常信息为：时间维度不能为空!,model.cy.js,PASS,林泽芳,20250513
模型管理,UC-MODEL-076,模型管理新增模型无机构表，应阻止并提示错误,功能测试,Cypress,无,"1.进入模型管理页面
2.点击新增模型
3.选择数据源
4.点击保存并发布",页面报错提示：该模型无机构表，无法发布！,model.cy.js,PASS,林泽芳,20250514
模型管理,UC-MODEL-077,模型管理新增模型时间维度类型不对，应阻止并提示错误,功能测试,Cypress,无,"1.进入模型管理页面
2.点击新增模型
3.选择数据源
4.选择表格并添加维度
5.将维度设置为机构维度
6.选择表格添加时间维度且类型为非BIGNT
7.点击保存",页面报错提示：时间维度必须是BIGINT类型,model.cy.js,PASS,林泽芳,20250514
模型管理,UC-MODEL-078,模型管理新增模型时间维度类型不对并校验自定义sql,功能测试,Cypress,无,"1.进入模型管理页面
2.点击新增模型
3.选择数据源
4.选择表格并添加维度
5.将维度设置为机构维度
6.选择表格添加时间维度且类型为非BIGNT
7.点击自定义sql图标
8.点击校验",页面报错提示：CoralReef接口异常，异常信息为：Object reference not set to an instance of an object.,model.cy.js,PASS,林泽芳,20250514
模型管理,UC-MODEL-079,模型管理新增模型填写信息，无聚合函数，并保存模型和发布指定审批人,功能测试,Cypress,时间维度和机构维度已有，无聚合函数,"1.进入模型管理页面
2.点击新增模型
3.选择数据源
4.选择表格并添加维度
5.将维度设置为机构维度
6.选择表格添加时间维度且类型为BIGNT
7.点击保存并发布
8.选择审批人",操作成功,model.cy.js,PASS,林泽芳,20250514
模型管理,UC-MODEL-080,模型管理草稿版和未指定审批人模型的删除,功能测试,Cypress,"存在“cypress-test""模型","1.进入模型管理页面
2.搜索cypress
3.点击删除",操作成功,model.cy.js,PASS,林泽芳,20250514
模型管理,UC-MODEL-081,模型管理审批中的模型删除,功能测试,Cypress,"存在“cypress-test""模型","1.进入模型管理页面
2.搜索cypress
3.点击删除",页面显示：存在审批中的任务，待审批通过再操作,model.cy.js,PASS,林泽芳,20250516
模型管理,UC-MODEL-082,模型管理查看血缘分析,功能测试,Cypress,"存在“cypress-test""模型","1.进入模型管理页面
2.搜索cypress
3.选择血缘分析按钮",操作成功,model.cy.js,PASS,林泽芳,20250516
模型管理,UC-MODEL-083,模型管理查看添加自定义维度,功能测试,Cypress,无,"1.进入模型管理页面
2.编辑模型管理，在维度框选择添加自定义维度",操作成功,model.cy.js,PASS,林泽芳,20250512
模型管理,UC-MODEL-084,模型管理的添加自定义维度获取sql模版,功能测试,Cypress,无,"1.进入模型管理页面
2.编辑模型管理，在维度框选择添加自定义维度
3.选择sql模版
4.添加维度",操作成功,model.cy.js,PASS,林泽芳,20250512
模型管理,UC-MODEL-085,模型管理预览功能,功能测试,Cypress,"存在“cypress-test""模型","1.进入模型管理页面
2.搜索cypress
3.点击预览",操作成功,model.cy.js,PASS,林泽芳,20250515
模型管理,UC-MODEL-086,重新编辑模型，发布模型的时候，无聚合函数,功能测试,Cypress,已有时间维度和机构维度,进入模型管理，新增模型，修改模型，进行发布模型,前端报错:存在无聚合/无维度字段,model.cy.js,PASS,林泽芳,20250425
指标地图,UC-INDMAP-001,指标地图查询预警状态应该返回成功响应,功能测试,Junit,无,进入指标地图,操作成功,IndMapControllerTest#testIndMapQuery,PASS,林泽芳,20250418
指标地图,UC-INDMAP-002,指标地图查询预警状态-日期参数不对,异常测试,Junit,无,进入指标地图,后端返回码为500，信息:数据日期格式不正确（yyyyMMdd）,"IndMapControllerTest#testIndMapQueryIncorrect
IndMapManageServiceTest#testIndMapQueryIncorrect",PASS,林泽芳,20250425
指标地图,UC-INDMAP-003,指标地图查询预警状态-缺少日期的参数,异常测试,Junit,无,进入指标地图,后端返回码为500，信息:数据日期为空,"IndMapControllerTest#testIndMapQueryNullDateParams
 IndMapManageServiceTest#testIndMapQueryNullDateParams",PASS,林泽芳,20250425
指标地图,UC-INDMAP-004,指标地图查询预警状态-缺少必要的参数,异常测试,Junit,无,进入指标地图,后端返回码为500，信息无,"IndMapControllerTest#testIndMapQueryNullParams
 IndMapManageServiceTest#testIndMapQueryNullParams",PASS,林泽芳,20250425
指标地图,UC-INDMAP-005,创建派生指标时获取指标信息应该返回成功响应,功能测试,Junit,无,进入指标地图，点击创建派生指标，打开弹窗,操作成功,IndMapControllerTest#testGetIndInfo,PASS,林泽芳,20250418
指标地图,UC-INDMAP-006,创建派生指标时获取指标信息或者指标详情的可用维度信息-空参数,异常测试,Junit,无,进入指标地图，点击创建派生指标，打开弹窗,后端返回码为500，信息:CoralReef接口异常，异常信息为：bad request,"IndMapControllerTest#testGetIndInfoIncorrect
 IndMapManageServiceTest#testGetIndInfoIncorrect",PASS,林泽芳,20250425
指标地图,UC-INDMAP-007,创建派生指标时获取指标信息或者指标详情的可用维度信息应该返回成功响应,功能测试,Junit,无,进入指标地图，点击创建派生指标填写名称,操作成功,IndMapControllerTest#testGetSameName,PASS,林泽芳,20250418
指标地图,UC-INDMAP-008,创建派生指标时进行预览,功能测试,Junit,无,进入指标地图，点击创建派生指标，填写信息并点击预览,后端返回码为500，错误信息:CoralReef接口异常，异常信息为：未找到指标信息,IndMapControllerTest#testPreviewDeriveIndValue IndMapManageServiceTest#testPreviewDeriveIndValue,PASS,林泽芳,20250418
指标地图,UC-INDMAP-009,创建派生指标时新增过滤条件应该返回成功响应,功能测试,Junit,无,进入指标地图，进入指标地图，点击创建派生指标，填写过滤条件,后端返回200，数据为过滤条件信息,IndMapControllerTest#testQueryFilterData,PASS,林泽芳,20250418
指标地图,UC-INDMAP-010,创建派生指标时新增过滤条件-缺少必要的参数,异常测试,Junit,无,进入指标地图，进入指标地图，点击创建派生指标，拖拽过滤条件,后端返回200，数据为空,IndMapControllerTest#testQueryFilterDataNullParams,PASS,林泽芳,20250425
指标地图,UC-INDMAP-011,定义衍生指标时预览指标应该返回成功响应,功能测试,Junit,无,进入指标地图，进入指标地图，点击创建衍生指标，填写信息并点击预览,操作成功,IndMapControllerTest#testPreviewDerivativesIndValue,PASS,林泽芳,20250418
指标地图,UC-INDMAP-012,查看逻辑获取信息应该返回成功响应,功能测试,Junit,无,进入指标地图，选择指标的查看逻辑,操作成功,IndMapControllerTest#testGetDeriMapping,PASS,林泽芳,20250418
指标地图,UC-INDMAP-013,查看逻辑获取sql应该返回成功响应,功能测试,Junit,无,进入指标地图，选择指标的查看逻辑,操作成功,IndMapControllerTest#testGetIndicatorSql,PASS,林泽芳,20250418
指标地图,UC-INDMAP-014,定义衍生指标获取衍生信息应该返回成功响应,功能测试,Junit,无,进入指标地图，选择指标衍生，打开衍生指标弹窗,操作成功,IndMapControllerTest#testGetTypes,PASS,林泽芳,20250418
指标地图,UC-INDMAP-015,定义衍生指标时预览指标-指标id不存在,异常测试,Junit,model_id这个指标不存在,进入指标地图，选择指标衍生，打开衍生指标弹窗,后端返回码为500，信息：CoralReef接口异常，异常信息为：未找到原子指标[8c71648c-03c9-45ff-b30f-9c7a097a8283.ca8be3d9],"IndMapControllerTest#testPreviewDerivativesIndValueIncorrectParams
IndMapManageServiceTest#testPreviewDerivativesIndValueIncorrectParams",PASS,林泽芳,20250425
指标地图,UC-INDMAP-016,定义衍生指标时预览指标-缺少必要的参数,异常测试,Junit,无,进入指标地图，选择指标衍生，打开衍生指标弹窗,后端返回码为500，信息：CoralReef接口异常，异常信息为：Parameter 'MODEL_ID' must have its value set,"IndMapControllerTest#testPreviewDerivativesIndValueNullParams
IndMapManageServiceTest#testPreviewDeriveIndValue",PASS,林泽芳,20250425
指标地图,UC-INDMAP-017,透视分析获取数据日期应该返回成功响应,功能测试,Junit,无,进入指标地图，选择透视分析,操作成功,IndMapControllerTest#tesGetDefaultDataDate,PASS,林泽芳,20250418
指标地图,UC-INDMAP-018,定义派生指标获取归属机构应该返回成功响应,功能测试,Junit,无,进入指标地图，选择指标衍生，打开衍生指标弹窗,操作成功,IndMapControllerTest#testGetIndOrgName,PASS,林泽芳,20250418
指标地图,UC-INDMAP-019,指标地图点击指标分享获取分享过的信息应该返回成功响应,功能测试,Junit,无,进入指标地图，选择指标分享打开弹窗,操作成功,IndMapControllerTest#testGetIndOrgName,PASS,林泽芳,20250418
指标地图,UC-INDMAP-020,指标地图点击指标分享给用户应该返回成功响应,功能测试,Junit,无,进入指标地图，选择指标分享，选择用户,操作成功,IndMapControllerTest#testAddObjectAuthorize,PASS,林泽芳,20250418
指标地图,UC-INDMAP-021,指标预警获取预警信息应该返回成功响应,功能测试,Junit,无,进入指标地图，选择指标预警,操作成功,IndMapControllerTest#testGetWarningInfo,PASS,林泽芳,20250418
指标地图,UC-INDMAP-022,指标预警获取预警规则信息条件应该返回成功响应,功能测试,Junit,无,进入指标地图，选择指标预警并点击预警规则,操作成功,IndMapControllerTest#testGetWarningTree,PASS,林泽芳,20250418
指标地图,UC-INDMAP-023,指标预警保存预警信息应该返回成功响应,功能测试,Junit,无,进入指标地图，选择指标预警，录入信息，保存预警信息,操作成功,IndMapControllerTest#testSaveWarningInfo,PASS,林泽芳,20250418
指标地图,UC-INDMAP-024,指标预警更新预警信息应该返回成功响应,功能测试,Junit,无,进入指标地图，选择指标预警，修改信息，更新预警信息,操作成功,IndMapControllerTest#testUpdateWarningInfo,PASS,林泽芳,20250418
指标地图,UC-INDMAP-025,指标详情的查询次数数据应该返回成功响应,功能测试,Junit,无,进入指标地图，选择指标详情,操作成功,IndMapControllerTest#testGetQueryCount,PASS,林泽芳,20250418
指标地图,UC-INDMAP-026,指标详情获取指标路径应该返回成功响应,功能测试,Junit,无,进入指标地图，选择指标详情,操作成功,IndMapControllerTest#testQueryIndicatorParentPath,PASS,林泽芳,20250418
指标地图,UC-INDMAP-027,指标详情通过路径获取指标信息应该返回成功响应,功能测试,Junit,无,进入指标地图，选择指标详情,操作成功,IndMapControllerTest#testGetAllDir,PASS,林泽芳,20250418
指标地图,UC-INDMAP-028,指标详情获取指标卡片信息应该返回成功响应,功能测试,Junit,无,进入指标地图，选择指标详情,操作成功,IndMapControllerTest#testGetIndicatorData,PASS,林泽芳,20250418
指标地图,UC-INDMAP-029,指标详情查询血缘关系获取相关指标信息应该返回成功响应,功能测试,Junit,无,进入指标地图，选择指标详情,操作成功,IndMapControllerTest#testGetObjectMapping,PASS,林泽芳,20250418
指标地图,UC-INDMAP-030,指标地图进行搜索，对各个分页tab进行查询,功能测试,Cypress,无,进入指标地图，输入搜索条件进行搜索，对各个分页tab进行查询,操作成功,indMap.cy.js,PASS,林泽芳,20250515
指标地图,UC-INDMAP-031,指标地图创建衍生指标，定义复杂指标，缺少必填信息,功能测试,Cypress,无,指标地图创建衍生指标，定义复杂指标，缺少必填信息，进行验证,提示过滤条件未填,indMap.cy.js,PASS,林泽芳,20250515
指标地图,UC-INDMAP-032,定义派生指标时无填写派生逻辑,功能测试,Cypress,无,"1.进入指标地图
2.输入名称和说明
3.点击确定",提示：请构建过滤条件,indMap.cy.js,PASS,林泽芳,20250515
指标地图,UC-INDMAP-033,定义派生指标时填写派生逻辑,功能测试,Cypress,无,"1.进入指标地图
2.输入名称和说明
3.设置派生逻辑
4.点击确定",操作成功,indMap.cy.js,PASS,林泽芳,20250515
看板创作,UC-INDCARD-001,新增目录应该返回成功响应,功能测试,Junit,无,进入看板创作， 点击新增目录,操作成功,"IndCardControllerTest#testCreateFolder
",PASS,林泽芳,20250418
看板创作,UC-INDCARD-002,新增已存在的目录,异常测试,Junit,目录已经存在,进入看板创作， 点击新增目录,后端返回码为500，信息“CoralReef接口异常，异常信息为：添加失败，同级目录下不允许有相同名称存在！,"IndCardControllerTest#testCreateSameFolder
IndCardServiceTest#testCreateSameFolder",PASS,林泽芳,20250424
看板创作,UC-INDCARD-003,使用空字符串查询本地看板列表应该返回成功响应,功能测试,Junit,无,进入看板创作，输入搜索条件进行搜索,操作成功,IndCardControllerTest#testIndCardList,PASS,林泽芳,20250418
看板创作,UC-INDCARD-004,使用特定的条件查询看板列表应该返回成功响应,功能测试,Junit,无,进入看板创作，输入搜索条件进行搜索,操作成功,IndCardControllerTest#testIndCardQueryList,PASS,林泽芳,20250418
看板创作,UC-INDCARD-005,查询所有目录应该返回成功响应,功能测试,Junit,无,进入看板创作，获取所有目录,操作成功,IndCardControllerTest#testIndCardGetAllFolder,PASS,林泽芳,20250418
看板创作,UC-INDCARD-006,本地看板列表查询用户归属管理部门应该返回成功响应,功能测试,Junit,无,进入看板创作，获取用户所属机构,操作成功,IndCardControllerTest#testIndCardGetDeptList,PASS,林泽芳,20250418
看板创作,UC-INDCARD-007,本地看板列表获取收藏清单应该返回成功响应,功能测试,Junit,无,进入看板创作，获取本地看板收藏的列表,操作成功,IndCardControllerTest#testIndCardGetAllCollection,PASS,林泽芳,20250418
看板创作,UC-INDCARD-008,本地看板列表获取公共看板列表应该返回成功响应,功能测试,Junit,无,进入看板创作，点击本地看板，获取设为公共的看板,操作成功,IndCardControllerTest#testIndCardGetSpecialList,PASS,林泽芳,20250418
看板创作,UC-INDCARD-009,分享给我的列表查询应该返回成功响应,功能测试,Junit,无,进入看板创作，点击分享给我的页面,操作成功,IndCardControllerTest#testIndCardGetShareToMeList,PASS,林泽芳,20250418
看板创作,UC-INDCARD-010,我的收藏列表查询应该返回成功响应,功能测试,Junit,无,进入看板创作，点击我的收藏页面,操作成功,IndCardControllerTest#testIndCardGetIndCardCollection,PASS,林泽芳,20250418
看板创作,UC-INDCARD-011,我的分享列表查询应该返回成功响应,功能测试,Junit,无,进入看板创作，点击我的分享页面,操作成功,IndCardControllerTest#testIndCardGetShareToOtherList,PASS,林泽芳,20250418
看板创作,UC-INDCARD-012,看板授权查询角色列表应该返回成功响应,功能测试,Junit,无,进入看板创作，选择可授权的正式版，点击授权，获取角色列表,操作成功,IndCardControllerTest#testIndCardObjectRoleList,PASS,林泽芳,20250418
看板创作,UC-INDCARD-013,看板列表设为公共目录审批应该返回成功响应,功能测试,Junit,无,进入看板创作，找到设为公共的目录或者看板，点击取消，进入审批,操作成功,IndCardControllerTest#testIndCardIsApproveForAddPublicFolder,PASS,林泽芳,20250418
看板创作,UC-INDCARD-014,看板列表更新目录名称应该返回成功响应,功能测试,Junit,无,进入看板创作，修改目录名称,操作成功,IndCardControllerTest#testIndCardUpdateFolder,PASS,林泽芳,20250418
看板创作,UC-INDCARD-015,看板列表删除目录应该返回成功响应,功能测试,Junit,无,进入看板创作，删除目录,操作成功,IndCardControllerTest#testIndCardDelete,PASS,林泽芳,20250418
看板创作,UC-INDCARD-016,看板列表删除看板数据应该返回成功响应,功能测试,Junit,无,进入看板创作，删除看板,操作成功,IndCardControllerTest#testIndCardDeleteCard,PASS,林泽芳,20250418
看板创作,UC-INDCARD-017,看板列表导入文件获取文件信息应该返回成功响应,功能测试,Junit,无,进入看板创作，选择导入文件按钮，获取文件信息,操作成功,IndCardControllerTest#testIndCardFindObjectMsg,PASS,林泽芳,20250418
看板创作,UC-INDCARD-018,看板列表导入文件应该返回成功响应,功能测试,Junit,无,进入看板创作，选择导入文件按钮，获取文件信息，导入文件,操作成功,IndCardControllerTest#testIndCardImport,PASS,林泽芳,20250418
看板创作,UC-INDCARD-019,看板列表导出文件获取文件版本号应该返回成功响应,功能测试,Junit,无,进入看板创作，选择导出的看板数据，获取文件版本号,操作成功,IndCardControllerTest#testIndCardGetObjectVersionInfoByList,PASS,林泽芳,20250418
看板创作,UC-INDCARD-020,看板列表导出文件获取文件夹下可以导出的文件应该返回成功响应,功能测试,Junit,无,进入看板创作，选择导出的文件夹，获取文件夹下的看板,操作成功,IndCardControllerTest#testIndCardGetExportObjects,PASS,林泽芳,20250418
看板创作,UC-INDCARD-021,看板列表导出文件应该返回成功响应,功能测试,Junit,无,进入看板创作，选择导出的文件夹，获取文件夹下的看板，并进行导出,操作成功,IndCardControllerTest#testIndCardExport,PASS,林泽芳,20250418
看板创作,UC-INDCARD-022,看板列表复制看板数据应该返回成功响应,功能测试,Junit,无,进入看板创作，选择看板数据，点击复制,操作成功,IndCardControllerTest#testIndCardCopy,PASS,林泽芳,20250418
看板创作,UC-INDCARD-023,看板列表设置收藏应该返回成功响应,功能测试,Junit,无,进入看板创作，选择看板数据，点击收藏,操作成功,IndCardControllerTest#testIndCardObjectCollection,PASS,林泽芳,20250418
看板创作,UC-INDCARD-024,看板列表取消收藏应该返回成功响应,功能测试,Junit,无,进入看板创作，选择看板数据，点击取消收藏,操作成功,IndCardControllerTest#testIndCardCancelCollection,PASS,林泽芳,20250418
看板创作,UC-INDCARD-025,看板列表查看获取ID应该返回成功响应,功能测试,Junit,无,进入看板创作，选择看板数据，点击查看,操作成功,IndCardControllerTest#testIndCardGetObjectItemByGuid,PASS,林泽芳,20250418
看板创作,UC-INDCARD-026,本地看板查看获取日期应该返回成功响应,功能测试,Junit,无,进入看板创作，选择看板数据，点击查看，获取日期,操作成功,IndCardControllerTest#tesGetDefaultDataDate,PASS,林泽芳,20250418
看板创作,UC-INDCARD-027,本地看板查看时获取预警状态应该返回成功响应,功能测试,Junit,无,进入看板创作，选择看板数据，点击查看，根据卡片获取预警状态,操作成功,IndCardControllerTest#testIndCardView,PASS,林泽芳,20250418
看板创作,UC-INDCARD-028,本地看板授权获取机构数返回成功的响应,功能测试,Junit,无,进入看板创作，选择可授权的正式版，点击授权，获取机构,操作成功,IndCardControllerTest#testIndCardFindDeptByDeptId,PASS,林泽芳,20250418
看板创作,UC-INDCARD-029,本地看板创作目录设为公共看板审批过程中删除,功能测试,Cypress,无,"1.进入看板创作
2.设为公共目录
3.点击删除",页面显示：存在审批中的任务，待审批通过再操作,indCard.cy.js,PASS,林泽芳,20250516
KPI管理,UC-KPI-001,使用特定条件查询kpi列表应该返回成功响应,功能测试,Junit,无,进入kpi管理，输入搜索条件进行搜索,操作成功,KpiManageControllerTest#testKpiQuery,PASS,林泽芳,20250418
KPI管理,UC-KPI-002,新增kpi应该返回成功响应,功能测试,Junit,无,"进入kpi管理，点击新增kpi,输入信息",操作成功,KpiManageControllerTest#testKpiAdd,PASS,林泽芳,20250418
KPI管理,UC-KPI-003,删除kpi应该返回成功响应,功能测试,Junit,无,进入kpi管理，删除kpi,操作成功,KpiManageControllerTest#testKpiDelete,PASS,林泽芳,20250418
KPI管理,UC-KPI-004,获取kpi指标详情页面应该返回成功响应,功能测试,Junit,无,"选择一个kpi,进入kpi详情页面，",操作成功,KpiManageControllerTest#testGetKpiDetail,PASS,林泽芳,20250418
KPI管理,UC-KPI-005,kpi指标详情页获取指标应该返回成功响应,功能测试,Junit,无,"选择一个kpi,点击编辑，进入kpi详情页面",操作成功,KpiManageControllerTest#testKpiGetIndTreeData,PASS,林泽芳,20250418
KPI管理,UC-KPI-006,kpi指标详情新增目标值应该返回成功响应,功能测试,Junit,无,kpi详情页面新增目标值,操作成功,KpiManageControllerTest#testKpiSaveSingleTarget,PASS,林泽芳,20250418
KPI管理,UC-KPI-007,kpi指标详情保存应该返回成功响应,功能测试,Junit,无,kpi详情页面保存,操作成功,KpiManageControllerTest#testKpiSaveKpiDetail,PASS,林泽芳,20250418
KPI管理,UC-KPI-008,kpi指标详情导出模版应该返回成功响应,功能测试,Junit,无,kpi详情页面点击导出,操作成功,KpiManageControllerTest#testKpiDownloadExcel,PASS,林泽芳,20250418
KPI管理,UC-KPI-009,kpi指标详情修改kpi目标值应该返回成功响应,功能测试,Junit,无,kpi详情页面点击修改,操作成功,KpiManageControllerTest#testKpiUpdateKpiInfo,PASS,林泽芳,20250418
KPI管理,UC-KPI-010,kpi指标详情删除kpi目标值应该返回成功响应,功能测试,Junit,无,kpi详情页面点击删除,操作成功,KpiManageControllerTest#testKpiDeleteKpiTarget,PASS,林泽芳,20250418
KPI管理,UC-KPI-011,kpi指标详情导入模版应该返回成功响应,功能测试,Junit,无,kpi详情页面点击导入并上传,操作成功,KpiManageControllerTest#testKpiUploadExcel,PASS,林泽芳,20250418
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
