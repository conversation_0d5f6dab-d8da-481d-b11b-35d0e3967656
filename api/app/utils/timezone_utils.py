"""
时区转换工具模块
提供UTC时间到中国时区的转换功能
"""

from datetime import datetime, timezone, timedelta
from typing import Optional
import pytz

# 中国时区常量 (Asia/Shanghai)
CHINA_TIMEZONE = pytz.timezone('Asia/Shanghai')

def convert_utc_to_china_timezone(utc_datetime: Optional[datetime]) -> Optional[datetime]:
    """
    将UTC时间转换为中国时区时间
    
    Args:
        utc_datetime: UTC时间的datetime对象
        
    Returns:
        转换后的中国时区时间，如果输入为None则返回None
    """
    if utc_datetime is None:
        return None
    
    # 如果输入的datetime是naive（没有时区信息），假设它是UTC时间
    if utc_datetime.tzinfo is None:
        utc_datetime = utc_datetime.replace(tzinfo=timezone.utc)
    
    # 转换为中国时区
    china_datetime = utc_datetime.astimezone(CHINA_TIMEZONE)
    
    # 返回没有时区信息的datetime对象（保持与现有系统兼容）
    return china_datetime.replace(tzinfo=None)

def get_china_now() -> datetime:
    """
    获取当前中国时区的时间
    
    Returns:
        当前中国时区时间（不含时区信息）
    """
    china_now = datetime.now(CHINA_TIMEZONE)
    return china_now.replace(tzinfo=None)

def format_china_datetime(dt: Optional[datetime], format_str: str = "%Y-%m-%d %H:%M:%S") -> str:
    """
    格式化中国时区时间为字符串
    
    Args:
        dt: 要格式化的datetime对象
        format_str: 格式化字符串，默认为 "%Y-%m-%d %H:%M:%S"
        
    Returns:
        格式化后的时间字符串，如果输入为None则返回空字符串
    """
    if dt is None:
        return ""
    
    # 转换为中国时区时间
    china_dt = convert_utc_to_china_timezone(dt)
    
    if china_dt is None:
        return ""
    
    return china_dt.strftime(format_str) 