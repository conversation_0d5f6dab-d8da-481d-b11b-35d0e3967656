#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import time
import json
from typing import List, Dict, Any, Optional

class WeChatWorkClient:
    """企业微信客户端，用于发送通知消息"""
    
    def __init__(self, corpid: str, corpsecret: str, agentid: int):
        """
        初始化企业微信客户端
        
        Args:
            corpid: 企业ID
            corpsecret: 应用Secret
            agentid: 应用ID
        """
        self.corpid = corpid
        self.corpsecret = corpsecret
        self.agentid = agentid
        self.access_token = None
        self.token_expiry = 0
        self.token_cache_duration = 7000  # 缓存时间，单位秒，略低于实际过期时间7200秒

    def get_token(self, force_refresh=False):
        """
        获取企业微信access_token，支持缓存机制
        
        Args:
            force_refresh: 是否强制刷新token
            
        Returns:
            str: access_token或None（获取失败时）
        """
        current_time = time.time()
        if not force_refresh and self.access_token and current_time < self.token_expiry:
            return self.access_token
        
        try:
            response = requests.get(
                f"https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid={self.corpid}&corpsecret={self.corpsecret}"
            )
            token_data = response.json()
            if token_data.get('errcode') == 0:
                self.access_token = token_data.get('access_token')
                self.token_expiry = current_time + self.token_cache_duration
                print(f"获取企业微信Token成功: {self.access_token[:10]}..., 过期时间: {self.token_expiry}")
                return self.access_token
            else:
                print(f"获取企业微信Token失败: {token_data}")
                return None
        except Exception as e:
            print(f"获取企业微信Token时出错: {str(e)}")
            return None

    def send_text_message(self, user_ids: List[str], content: str) -> bool:
        """
        发送文本消息给指定用户
        
        Args:
            user_ids: 用户ID列表，多个用户用|分隔
            content: 消息内容
            
        Returns:
            bool: 是否发送成功
        """
        access_token = self.get_token()
        if not access_token:
            print("无法发送通知: 没有有效的access_token")
            return False
        
        try:
            # 将用户ID列表转换为企业微信需要的格式（用|分隔）
            touser = "|".join(user_ids)
            
            message_data = {
                "touser": touser,
                "msgtype": "text",
                "agentid": self.agentid,
                "text": {
                    "content": content
                },
                "safe": 0,
                "enable_id_trans": 0,
                "enable_duplicate_check": 0
            }
            
            response = requests.post(
                f"https://qyapi.weixin.qq.com/cgi-bin/message/send?access_token={access_token}",
                json=message_data
            )
            result = response.json()
            if result.get('errcode') == 0:
                print(f"成功发送消息给用户: {touser}")
                return True
            else:
                print(f"发送消息给用户 {touser} 失败: {result}")
                return False
        except Exception as e:
            print(f"发送消息给用户 {touser} 时出错: {str(e)}")
            return False

# 单例模式，确保只创建一个企业微信客户端实例
_wechat_work_client = None

def get_wechat_work_client(corpid: str, corpsecret: str, agentid: int) -> WeChatWorkClient:
    """
    获取企业微信客户端实例（单例模式）
    
    Args:
        corpid: 企业ID
        corpsecret: 应用Secret
        agentid: 应用ID
        
    Returns:
        WeChatWorkClient: 企业微信客户端实例
    """
    global _wechat_work_client
    if _wechat_work_client is None:
        _wechat_work_client = WeChatWorkClient(corpid, corpsecret, agentid)
    return _wechat_work_client
