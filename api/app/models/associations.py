from sqlalchemy import Table, <PERSON>umn, Integer, Foreign<PERSON>ey, DateTime
from app.core.database import Base
from datetime import datetime

# 定义项目-开发者关联表
project_developers_table = Table('project_developers',
    Base.metadata,
    Column('project_id', Integer, ForeignKey('projects.id'), primary_key=True),
    Column('user_id', Integer, ForeignKey('users.id'), primary_key=True),
    Column('join_date', DateTime, default=datetime.utcnow)
)

# 定义项目-测试者关联表
project_testers_table = Table('project_testers',
    Base.metadata,
    Column('project_id', Integer, ForeignKey('projects.id'), primary_key=True),
    Column('user_id', Integer, ForeignKey('users.id'), primary_key=True),
    Column('join_date', DateTime, default=datetime.utcnow)
) 