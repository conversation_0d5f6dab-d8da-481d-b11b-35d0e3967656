from sqlalchemy import Column, Integer, String, Text, DateTime, ForeignKey, Enum as SQLAlchemyEnum
from sqlalchemy.orm import relationship
from app.core.database import Base
import enum
from datetime import datetime, timezone

# 创建一个函数来提供当前的UTC时间
def get_utc_now():
    return datetime.now(timezone.utc)

# 测试类型枚举
class TestType(str, enum.Enum):
    FUNCTIONAL = "功能测试"
    EXCEPTION = "异常测试"

# 测试状态枚举
class TestStatus(str, enum.Enum):
    PASS = "PASS"
    FAIL = "FAIL"

# 测试端枚举
class TestSide(str, enum.Enum):
    FRONTEND = "前端测试"
    BACKEND = "后端测试"

# 表名：TestCase（测试用例表）
class TestCase(Base):
    __tablename__ = "test_cases"

    id = Column(Integer, primary_key=True, autoincrement=True, comment="主键，自动递增整数")
    project_id = Column(Integer, ForeignKey("projects.id"), nullable=False, comment="外键，关联项目表ID")
    module_id = Column(Integer, ForeignKey("test_function_modules.id"), nullable=False, comment="外键，关联测试功能模块表ID")
    test_number = Column(String(50), nullable=False, comment="测试编号，手动输入")
    test_name = Column(String(100), nullable=False, comment="测试名称")
    test_type = Column(SQLAlchemyEnum(TestType), nullable=False, comment="测试类型，功能测试或异常测试")
    test_side = Column(SQLAlchemyEnum(TestSide), nullable=False, default=TestSide.BACKEND, comment="测试端，前端测试或后端测试")
    precondition = Column(Text, nullable=True, comment="前提条件")
    test_steps = Column(Text, nullable=False, comment="测试步骤")
    expected_result = Column(Text, nullable=False, comment="预期结果")
    related_code = Column(Text, nullable=False, comment="关联代码")
    test_status = Column(SQLAlchemyEnum(TestStatus), nullable=False, comment="测试状态，PASS或FAIL")
    creator_id = Column(Integer, ForeignKey("users.id"), nullable=False, comment="创建人ID")
    created_at = Column(DateTime, nullable=False, default=get_utc_now, comment="创建时间")
    updater_id = Column(Integer, ForeignKey("users.id"), nullable=True, comment="最后修改人ID")
    updated_at = Column(DateTime, nullable=True, onupdate=get_utc_now, comment="最后修改时间")

    # 关联关系
    project = relationship("Project", back_populates="test_cases")
    module = relationship("TestFunctionModule", back_populates="test_cases")
    creator = relationship("User", foreign_keys=[creator_id])
    updater = relationship("User", foreign_keys=[updater_id])
