from sqlalchemy import Column, Integer, String, Bo<PERSON>an, DateTime
from sqlalchemy.orm import relationship
from datetime import datetime
from app.core.database import Base
# 从 .project 导入 Project 类
from .project import Project 
# 从 .associations 导入关联表
from .associations import project_developers_table, project_testers_table

class User(Base):
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    username = Column(String, unique=True, index=True)
    password = Column(String) # 注意：实际应用中应存储密码哈希值
    name = Column(String, nullable=False)
    qywx_id = Column(String, unique=True)
    is_admin = Column(Boolean, default=False)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)

    # 添加与 Project 的关系
    created_projects = relationship("Project", back_populates="creator")
    developed_projects = relationship("Project", secondary=project_developers_table, back_populates="developers")
    tested_projects = relationship("Project", secondary=project_testers_table, back_populates="testers")