from sqlalchemy import Column, Integer, String, Text, DateTime, Enum as SQLAlchemyEnum, ForeignKey
from sqlalchemy.orm import relationship
from app.core.database import Base
import enum
from datetime import datetime
from .associations import project_developers_table, project_testers_table

class ProjectStatus(str, enum.Enum):
    IN_PROGRESS = "进行中"
    COMPLETED = "已完成"
    ARCHIVED = "已归档"

Enum = SQLAlchemyEnum

class Project(Base):
    __tablename__ = "projects"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, nullable=False)
    git_repo_url = Column(String, nullable=False)
    description = Column(Text, nullable=True)
    creator_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)

    # 定义与 User 的关系 (使用字符串 "User")
    creator = relationship("User", back_populates="created_projects")
    developers = relationship("User", secondary=project_developers_table, back_populates="developed_projects")
    testers = relationship("User", secondary=project_testers_table, back_populates="tested_projects")

    # 定义与 TestFunctionModule 的关系
    test_function_modules = relationship("TestFunctionModule", backref="project", cascade="all, delete-orphan")

    # 定义与 TestCase 的关系
    test_cases = relationship("TestCase", back_populates="project", cascade="all, delete-orphan")

# ProjectMember 模型已移除