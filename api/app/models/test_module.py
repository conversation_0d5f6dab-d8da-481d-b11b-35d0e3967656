from sqlalchemy import Column, Integer, String, DateTime, ForeignKey
from sqlalchemy.orm import relationship
from app.core.database import Base
from datetime import datetime, timezone

# 创建一个函数来提供当前的UTC时间
def get_utc_now():
    return datetime.now(timezone.utc)

# 表名：TestFunctionModule（测试功能模块表）
class TestFunctionModule(Base):
    __tablename__ = "test_function_modules"

    id = Column(Integer, primary_key=True, autoincrement=True, comment="主键，自动递增整数")
    project_id = Column(Integer, ForeignKey("projects.id"), nullable=False, comment="外键，关联项目表ID")
    name = Column(String(100), nullable=False, comment="功能模块名称，中文")
    created_at = Column(DateTime, nullable=False, default=get_utc_now, comment="创建时间，自动记录")
    updated_at = Column(DateTime, nullable=True, onupdate=get_utc_now, comment="更新时间，自动记录")

    # 关联关系
    test_cases = relationship("TestCase", back_populates="module", cascade="all, delete-orphan")
