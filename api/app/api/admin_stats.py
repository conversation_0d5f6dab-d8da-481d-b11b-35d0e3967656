from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from sqlalchemy import func
from datetime import datetime, timedelta, timezone
from app.core.database import get_db
from app.core.security import get_current_user
from app.models.user import User as UserModel
from app.models.project import Project as ProjectModel
from app.models.requirement import Requirement as RequirementModel, RequirementPullRequest, RequirementStatus

router = APIRouter()

def check_admin(current_user):
    """检查用户是否为管理员"""
    if not current_user.is_admin:
        raise HTTPException(status_code=403, detail="仅管理员可访问此接口")

@router.get("/stats/users/total")
async def get_total_users(
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(get_current_user)
):
    """获取系统用户总数"""
    check_admin(current_user)
    total_users = db.query(func.count(UserModel.id)).scalar()
    return {"total": total_users}

@router.get("/stats/users/new")
async def get_new_users(
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(get_current_user)
):
    """获取近7天新增用户个数"""
    check_admin(current_user)
    seven_days_ago = datetime.now(timezone.utc) - timedelta(days=7)
    new_users = db.query(func.count(UserModel.id)).filter(
        UserModel.created_at >= seven_days_ago
    ).scalar()
    return {"total": new_users}

@router.get("/stats/projects/total")
async def get_total_projects(
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(get_current_user)
):
    """获取项目总数"""
    check_admin(current_user)
    total_projects = db.query(func.count(ProjectModel.id)).scalar()
    return {"total": total_projects}

@router.get("/stats/projects/new")
async def get_new_projects(
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(get_current_user)
):
    """获取近7天新增项目数"""
    check_admin(current_user)
    seven_days_ago = datetime.now(timezone.utc) - timedelta(days=7)
    new_projects = db.query(func.count(ProjectModel.id)).filter(
        ProjectModel.created_at >= seven_days_ago
    ).scalar()
    return {"total": new_projects}

@router.get("/stats/pull-requests/pending")
async def get_pending_pull_requests(
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(get_current_user)
):
    """获取待处理的git PR记录的个数"""
    check_admin(current_user)
    pending_prs = db.query(func.count(RequirementPullRequest.id)).filter(
        RequirementPullRequest.status == "开放中"
    ).scalar()
    return {"total": pending_prs}

@router.get("/stats/pull-requests/today")
async def get_today_pull_requests(
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(get_current_user)
):
    """获取今日新增的git PR个数"""
    check_admin(current_user)
    today = datetime.now(timezone.utc).date()
    today_start = datetime.combine(today, datetime.min.time(), tzinfo=timezone.utc)
    today_end = datetime.combine(today, datetime.max.time(), tzinfo=timezone.utc)

    today_prs = db.query(func.count(RequirementPullRequest.id)).filter(
        RequirementPullRequest.create_time >= today_start,
        RequirementPullRequest.create_time <= today_end
    ).scalar()
    return {"total": today_prs}

@router.get("/stats/requirements/pending")
async def get_pending_requirements(
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(get_current_user)
):
    """获取未完成处理需求的个数"""
    check_admin(current_user)
    pending_requirements = db.query(func.count(RequirementModel.id)).filter(
        RequirementModel.status != RequirementStatus.COMPLETED
    ).scalar()
    return {"total": pending_requirements}

@router.get("/stats/requirements/overdue")
async def get_overdue_requirements(
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(get_current_user)
):
    """获取逾期需求的个数"""
    check_admin(current_user)
    today = datetime.now(timezone.utc)
    overdue_requirements = db.query(func.count(RequirementModel.id)).filter(
        RequirementModel.end_date < today,
        RequirementModel.status != RequirementStatus.COMPLETED
    ).scalar()
    return {"total": overdue_requirements}

@router.get("/stats/requirements/my-created")
async def get_my_created_requirements(
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(get_current_user)
):
    """获取当前用户创建的需求数量"""
    my_requirements = db.query(func.count(RequirementModel.id)).filter(
        RequirementModel.submitter_id == current_user.id
    ).scalar()
    return {"total": my_requirements}

@router.get("/stats/requirements/by-status/{status}")
async def get_requirements_by_status(
    status: str,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(get_current_user)
):
    """获取特定状态的需求数量"""
    check_admin(current_user)

    # 将英文状态代码转换为中文状态值
    status_mapping = {
        "PENDING": RequirementStatus.PENDING,
        "DEVELOPING": RequirementStatus.DEVELOPING,
        "TESTING": RequirementStatus.TESTING,
        "VALIDATING": RequirementStatus.VALIDATING,
        "COMPLETED": RequirementStatus.COMPLETED
    }

    # 特殊处理"ALL"状态，表示所有状态的需求
    if status.upper() == "ALL":
        requirements_count = db.query(func.count(RequirementModel.id)).scalar()
        return {"total": requirements_count}

    # 特殊处理"UNCOMPLETE"状态，表示所有未完成的需求
    if status.upper() == "UNCOMPLETE":
        requirements_count = db.query(func.count(RequirementModel.id)).filter(
            RequirementModel.status != RequirementStatus.COMPLETED
        ).scalar()
        return {"total": requirements_count}

    # 检查状态是否有效
    if status.upper() not in status_mapping:
        valid_statuses = list(status_mapping.keys()) + ["ALL", "UNCOMPLETE"]
        raise HTTPException(status_code=400, detail=f"无效的状态值: {status}，有效值为: {', '.join(valid_statuses)}")

    # 获取对应的中文状态值
    status_value = status_mapping[status.upper()]

    # 查询特定状态的需求数量
    requirements_count = db.query(func.count(RequirementModel.id)).filter(
        RequirementModel.status == status_value
    ).scalar()

    return {"total": requirements_count}

@router.get("/stats/dashboard")
async def get_dashboard_stats(
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(get_current_user)
):
    """获取管理员首页所需的所有统计数据"""
    check_admin(current_user)

    # 当前时间和7天前的时间
    now = datetime.now(timezone.utc)
    seven_days_ago = now - timedelta(days=7)
    today = now.date()
    today_start = datetime.combine(today, datetime.min.time(), tzinfo=timezone.utc)
    today_end = datetime.combine(today, datetime.max.time(), tzinfo=timezone.utc)

    # 用户统计
    total_users = db.query(func.count(UserModel.id)).scalar()
    new_users = db.query(func.count(UserModel.id)).filter(
        UserModel.created_at >= seven_days_ago
    ).scalar()

    # 项目统计
    total_projects = db.query(func.count(ProjectModel.id)).scalar()
    new_projects = db.query(func.count(ProjectModel.id)).filter(
        ProjectModel.created_at >= seven_days_ago
    ).scalar()

    # PR统计
    pending_prs = db.query(func.count(RequirementPullRequest.id)).filter(
        RequirementPullRequest.status == "开放中"
    ).scalar()
    today_prs = db.query(func.count(RequirementPullRequest.id)).filter(
        RequirementPullRequest.create_time >= today_start,
        RequirementPullRequest.create_time <= today_end
    ).scalar()

    # 需求统计
    pending_requirements = db.query(func.count(RequirementModel.id)).filter(
        RequirementModel.status != RequirementStatus.COMPLETED
    ).scalar()
    overdue_requirements = db.query(func.count(RequirementModel.id)).filter(
        RequirementModel.end_date < now,
        RequirementModel.status != RequirementStatus.COMPLETED
    ).scalar()

    # 当前用户创建的需求
    my_requirements = db.query(func.count(RequirementModel.id)).filter(
        RequirementModel.submitter_id == current_user.id
    ).scalar()

    return {
        "users": {
            "total": total_users,
            "new_last_7_days": new_users
        },
        "projects": {
            "total": total_projects,
            "new_last_7_days": new_projects
        },
        "pull_requests": {
            "pending": pending_prs,
            "new_today": today_prs
        },
        "requirements": {
            "pending": pending_requirements,
            "overdue": overdue_requirements,
            "my_created": my_requirements
        }
    }
