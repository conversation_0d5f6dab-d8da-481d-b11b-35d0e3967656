"""
API路由模块
"""

from fastapi import APIRouter
from .auth import router as auth_router
from .project import router as project_router
from .requirement import router as requirement_router, get_requirements_list
from .user import router as user_router
from .pull_request import router as pull_request_router
from .test_module import router as test_module_router
from .test_case import router as test_case_router
from .tester import router as tester_router
from .test_case_public import router as test_case_public_router
from .admin_stats import router as admin_stats_router

router = APIRouter()

# 认证相关路由，无额外前缀 (由前端代理处理 /api)
router.include_router(auth_router, tags=["auth"])
router.include_router(tester_router, tags=["tester"])
router.include_router(test_case_public_router, tags=["test-cases-public"])

# 非管理员需求列表接口，单独注册到根路径
router.get("/requirements-list", tags=["requirements"])(get_requirements_list)

# 管理员功能路由，前缀 /admin (由前端代理处理 /api)
router.include_router(user_router, prefix="/admin", tags=["users"])
router.include_router(project_router, prefix="/admin", tags=["projects"])
router.include_router(requirement_router, prefix="/admin", tags=["requirements"])
router.include_router(pull_request_router, prefix="/admin", tags=["pull-requests"])
router.include_router(test_module_router, prefix="/admin", tags=["test-modules"])
router.include_router(test_case_router, prefix="/admin", tags=["test-cases"])
router.include_router(admin_stats_router, prefix="/admin", tags=["admin-stats"])