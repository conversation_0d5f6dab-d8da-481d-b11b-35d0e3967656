from fastapi import Depends, APIRouter, HTTPException, status
from sqlalchemy.orm import Session
from typing import List

from app.core.database import get_db
from app.models.user import User
from app.core.security import get_current_user

router = APIRouter()

@router.get("/tester-projects", response_model=List[dict])
def get_tester_projects(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取当前用户作为测试者的项目列表"""
    # 获取当前用户作为测试者的项目
    projects = current_user.tested_projects

    # 构建响应
    result = []
    for project in projects:
        result.append({
            "id": project.id,
            "name": project.name,
            "description": project.description,
            "git_repo_url": project.git_repo_url,
            "created_at": project.created_at
        })

    return result
