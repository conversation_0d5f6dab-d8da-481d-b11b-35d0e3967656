from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List
from app.core.database import get_db
from app.core.security import get_password_hash, get_current_user
from app.models.user import User as UserModel
from app.schemas.user import UserCreate, UserUpdate, User

router = APIRouter()

@router.post("/users", response_model=User)
async def create_user(
    user: UserCreate,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(get_current_user)
):
    if not current_user.is_admin:
        raise HTTPException(status_code=403, detail="Not authorized to create users")
    db_user = db.query(UserModel).filter(UserModel.username == user.username).first()
    if db_user:
        raise HTTPException(status_code=400, detail="Username already registered")
    hashed_password = get_password_hash(user.password)
    # Assuming User model has 'name' field, adjust if necessary
    db_user = UserModel(
        username=user.username,
        password=hashed_password,
        name=user.name,
        is_admin=user.is_admin if hasattr(user, 'is_admin') else False # Handle potential is_admin field
    )
    db.add(db_user)
    db.commit()
    db.refresh(db_user)
    return db_user

@router.get("/users", response_model=List[User])
async def read_users(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(get_current_user)
):
    if not current_user.is_admin:
        raise HTTPException(status_code=403, detail="Not authorized to view users")
    users = db.query(UserModel).offset(skip).limit(limit).all()
    return users

@router.get("/users/{user_id}", response_model=User)
async def read_user(
    user_id: int,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(get_current_user)
):
    if not current_user.is_admin and current_user.id != user_id: # Allow user to view their own profile? Decide based on requirements. Assuming admin only for now.
         raise HTTPException(status_code=403, detail="Not authorized to view user details")
    user = db.query(UserModel).filter(UserModel.id == user_id).first()
    if user is None:
        raise HTTPException(status_code=404, detail="User not found")
    return user

@router.put("/users/{user_id}", response_model=User)
async def update_user(
    user_id: int,
    user_update: UserUpdate,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(get_current_user)
):
    if not current_user.is_admin:
        raise HTTPException(status_code=403, detail="Not authorized to update users")
    db_user = db.query(UserModel).filter(UserModel.id == user_id).first()
    if db_user is None:
        raise HTTPException(status_code=404, detail="User not found")

    update_data = user_update.dict(exclude_unset=True)

    # 只有当密码字段存在且不为None时才更新密码
    if 'password' in update_data and update_data['password'] is not None:
        hashed_password = get_password_hash(update_data['password'])
        update_data['password'] = hashed_password
    else:
        # 如果密码为None或不存在，从更新数据中移除密码字段
        update_data.pop('password', None)

    # 记录要更新的字段，用于调试
    import logging
    logger = logging.getLogger(__name__)
    logger.info(f"更新用户 {db_user.username} 的字段: {list(update_data.keys())}")

    for key, value in update_data.items():
        setattr(db_user, key, value)

    db.commit()
    db.refresh(db_user)
    return db_user

@router.delete("/users/{user_id}")
async def delete_user(
    user_id: int,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(get_current_user)
):
    if not current_user.is_admin:
        raise HTTPException(status_code=403, detail="Not authorized to delete users")
    db_user = db.query(UserModel).filter(UserModel.id == user_id).first()
    if db_user is None:
        raise HTTPException(status_code=404, detail="User not found")
    # Prevent deleting oneself? Optional check
    # if current_user.id == user_id:
    #     raise HTTPException(status_code=400, detail="Cannot delete own account")
    db.delete(db_user)
    db.commit()
    return {"message": "User deleted successfully"}