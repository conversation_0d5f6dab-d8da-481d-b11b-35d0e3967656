from fastapi import Depends, APIRouter, HTTPException, status, Body
from sqlalchemy.orm import Session
from typing import List, Dict, Any
from datetime import datetime

from app.core.database import get_db
from app.models.requirement import (
    RequirementPullRequest,
    PullRequestStatus,
    get_utc_now
)
from app.models.user import User
from app.models.project import Project
from app.core.security import get_current_user
from app.utils.gitlab_api import gitlab_api

router = APIRouter()

# 获取所有PR列表
@router.get("/pull-requests", response_model=List[Dict[str, Any]])
def get_pull_requests(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    获取所有PR列表，仅管理员可访问
    """
    # 检查用户权限
    if not current_user.is_admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="只有管理员可以访问PR列表"
        )

    # 查询所有PR
    pull_requests = db.query(RequirementPullRequest).all()

    # 构造响应数据
    result = []
    for pr in pull_requests:
        # 获取项目信息
        project = db.query(Project).filter(Project.id == pr.project_id).first()
        project_name = project.name if project else "未知项目"

        # 获取需求信息
        from app.models.requirement import Requirement
        requirement = db.query(Requirement).filter(Requirement.id == pr.requirement_id).first()
        requirement_info = {
            "id": requirement.id,
            "code": requirement.requirement_code,
            "title": requirement.title
        } if requirement else {"id": None, "code": "未知", "title": "未知需求"}

        # 处理状态，确保返回格式一致
        status_info = {
            "name": pr.status.name,
            "value": pr.status.value
        }

        result.append({
            "id": pr.id,
            "requirement_id": pr.requirement_id,
            "requirement": requirement_info,
            "project_id": pr.project_id,
            "project_name": project_name,
            "source_branch": pr.source_branch,
            "target_branch": pr.target_branch,
            "pr_iid": pr.pr_iid,
            "pr_url": pr.pr_url,
            "status": status_info,
            "create_time": pr.create_time,
            "update_time": pr.update_time
        })

    return result

# 获取按需求分组的PR列表
@router.get("/pull-requests/grouped", response_model=List[Dict[str, Any]])
def get_grouped_pull_requests(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    获取按需求分组的PR列表，仅管理员可访问
    """
    # 检查用户权限
    if not current_user.is_admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="只有管理员可以访问PR列表"
        )

    # 查询所有PR
    pull_requests = db.query(RequirementPullRequest).all()

    # 按需求ID分组
    grouped_prs = {}
    for pr in pull_requests:
        if pr.requirement_id not in grouped_prs:
            # 获取项目信息
            project = db.query(Project).filter(Project.id == pr.project_id).first()
            project_name = project.name if project else "未知项目"

            # 获取需求信息
            from app.models.requirement import Requirement
            requirement = db.query(Requirement).filter(Requirement.id == pr.requirement_id).first()

            if not requirement:
                continue

            requirement_info = {
                "id": requirement.id,
                "code": requirement.requirement_code,
                "title": requirement.title,
                "content": requirement.content,
                "status": requirement.status.value,
                "type": requirement.type.value,
                "priority": requirement.priority.value,
                "git_branch": requirement.git_branch
            }

            # 初始化需求分组
            grouped_prs[pr.requirement_id] = {
                "requirement_id": pr.requirement_id,
                "requirement": requirement_info,
                "project_id": pr.project_id,
                "project_name": project_name,
                "pull_requests": []
            }

        # 处理状态，确保返回格式一致
        status_info = {
            "name": pr.status.name,
            "value": pr.status.value
        }

        # 添加PR到对应需求的列表中
        grouped_prs[pr.requirement_id]["pull_requests"].append({
            "id": pr.id,
            "source_branch": pr.source_branch,
            "target_branch": pr.target_branch,
            "pr_iid": pr.pr_iid,
            "pr_url": pr.pr_url,
            "status": status_info,
            "create_time": pr.create_time,
            "update_time": pr.update_time
        })

    # 转换为列表格式返回
    result = list(grouped_prs.values())

    return result

# 批量合并需求的所有PR
@router.post("/requirements/{requirement_id}/merge-all", response_model=Dict[str, Any])
def merge_all_pull_requests(
    requirement_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    批量合并需求的所有PR，仅管理员可操作
    """
    # 检查用户权限
    if not current_user.is_admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="只有管理员可以合并PR"
        )

    # 查询需求的所有PR
    pull_requests = db.query(RequirementPullRequest).filter(
        RequirementPullRequest.requirement_id == requirement_id,
        RequirementPullRequest.status == PullRequestStatus.OPEN
    ).all()

    if not pull_requests:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="没有找到该需求的开放中PR"
        )

    # 记录合并结果
    merge_results = []
    success_count = 0
    error_count = 0

    # 逐个合并PR
    for pr in pull_requests:
        operation_log = {
            "operation": "merge",
            "pr_id": pr.id,
            "user_id": current_user.id,
            "user_name": current_user.name,
            "time": get_utc_now().isoformat(),
            "status": "pending"
        }

        try:
            # 调用GitLab API合并PR
            merge_result = gitlab_api.merge_pull_request(
                project_id=pr.project_id,
                merge_request_iid=pr.pr_iid
            )

            # 更新PR状态
            pr.status = PullRequestStatus.MERGED
            pr.update_time = get_utc_now()

            # 更新操作日志
            operation_log["status"] = "success"
            operation_log["result"] = merge_result

            success_count += 1

        except Exception as e:
            # 记录错误信息
            error_message = str(e)
            operation_log["status"] = "error"
            operation_log["error"] = error_message

            error_count += 1

        merge_results.append(operation_log)

    # 提交数据库更改
    db.commit()

    # 返回合并结果
    return {
        "message": f"批量合并完成: {success_count}个成功, {error_count}个失败",
        "requirement_id": requirement_id,
        "success_count": success_count,
        "error_count": error_count,
        "merge_results": merge_results
    }

# 拒绝需求并回退状态
@router.post("/requirements/{requirement_id}/reject", response_model=Dict[str, Any])
def reject_requirement(
    requirement_id: int,
    data: dict = Body(...),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    拒绝需求并回退状态到开发中，仅管理员可操作
    """
    # 检查用户权限
    if not current_user.is_admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="只有管理员可以拒绝需求"
        )

    # 验证拒绝理由
    reject_reason = data.get("reason")
    if not reject_reason:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="拒绝理由不能为空"
        )

    # 查询需求
    from app.models.requirement import Requirement, RequirementStatus, RequirementHistory, RequirementAction
    requirement = db.query(Requirement).filter(Requirement.id == requirement_id).first()

    if not requirement:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="需求不存在"
        )

    # 更新需求状态为开发中
    requirement.status = RequirementStatus.DEVELOPING
    requirement.update_time = get_utc_now()

    # 记录状态变更历史
    history = RequirementHistory(
        requirement_id=requirement_id,
        current_status_code=RequirementStatus.DEVELOPING.name,
        action_code=RequirementAction.REJECT_TEST.name,
        user_id=current_user.id,
        remark=reject_reason
    )
    db.add(history)

    # 查询需求的所有PR
    pull_requests = db.query(RequirementPullRequest).filter(
        RequirementPullRequest.requirement_id == requirement_id,
        RequirementPullRequest.status == PullRequestStatus.OPEN
    ).all()

    # 关闭所有开放中的PR
    close_results = []
    for pr in pull_requests:
        try:
            # 调用GitLab API关闭PR
            close_result = gitlab_api.close_pull_request(
                project_id=pr.project_id,
                merge_request_iid=pr.pr_iid
            )

            # 更新PR状态
            pr.status = PullRequestStatus.CLOSED
            pr.update_time = get_utc_now()

            close_results.append({
                "pr_id": pr.id,
                "status": "success"
            })
        except Exception as e:
            close_results.append({
                "pr_id": pr.id,
                "status": "error",
                "error": str(e)
            })

    # 提交数据库更改
    db.commit()

    # 返回结果
    return {
        "message": "需求已拒绝并回退到开发中状态",
        "requirement_id": requirement_id,
        "reject_reason": reject_reason,
        "closed_prs_count": len(close_results),
        "close_results": close_results
    }

# 合并单个PR
@router.post("/pull-requests/{pr_id}/merge", response_model=Dict[str, Any])
def merge_pull_request(
    pr_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    合并PR，仅管理员可操作
    """
    # 检查用户权限
    if not current_user.is_admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="只有管理员可以合并PR"
        )

    # 查询PR
    pr = db.query(RequirementPullRequest).filter(RequirementPullRequest.id == pr_id).first()
    if not pr:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="PR不存在"
        )

    # 检查PR状态
    if pr.status != PullRequestStatus.OPEN:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"只有开放中状态的PR可以合并，当前状态: {pr.status.value}"
        )

    # 记录操作日志
    operation_log = {
        "operation": "merge",
        "pr_id": pr_id,
        "user_id": current_user.id,
        "user_name": current_user.name,
        "time": get_utc_now().isoformat(),
        "status": "pending"
    }

    try:
        # 调用GitLab API合并PR
        merge_result = gitlab_api.merge_pull_request(
            project_id=pr.project_id,
            merge_request_iid=pr.pr_iid
        )

        # 更新PR状态
        pr.status = PullRequestStatus.MERGED
        pr.update_time = get_utc_now()
        db.commit()

        # 更新操作日志
        operation_log["status"] = "success"
        operation_log["result"] = merge_result

        return {
            "message": "PR合并成功",
            "pr_id": pr_id,
            "merge_result": merge_result,
            "operation_log": operation_log
        }
    except Exception as e:
        # 记录错误信息
        error_message = str(e)
        operation_log["status"] = "error"
        operation_log["error"] = error_message

        # 返回错误信息
        return {
            "message": "PR合并失败",
            "pr_id": pr_id,
            "error": error_message,
            "operation_log": operation_log
        }

# 关闭PR
@router.post("/pull-requests/{pr_id}/close", response_model=Dict[str, Any])
def close_pull_request(
    pr_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    关闭PR，仅管理员可操作
    """
    # 检查用户权限
    if not current_user.is_admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="只有管理员可以关闭PR"
        )

    # 查询PR
    pr = db.query(RequirementPullRequest).filter(RequirementPullRequest.id == pr_id).first()
    if not pr:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="PR不存在"
        )

    # 检查PR状态
    if pr.status != PullRequestStatus.OPEN:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"只有开放中状态的PR可以关闭，当前状态: {pr.status.value}"
        )

    # 记录操作日志
    operation_log = {
        "operation": "close",
        "pr_id": pr_id,
        "user_id": current_user.id,
        "user_name": current_user.name,
        "time": get_utc_now().isoformat(),
        "status": "pending"
    }

    try:
        # 调用GitLab API关闭PR
        close_result = gitlab_api.close_pull_request(
            project_id=pr.project_id,
            merge_request_iid=pr.pr_iid
        )

        # 更新PR状态
        pr.status = PullRequestStatus.CLOSED
        pr.update_time = get_utc_now()
        db.commit()

        # 更新操作日志
        operation_log["status"] = "success"
        operation_log["result"] = close_result

        return {
            "message": "PR关闭成功",
            "pr_id": pr_id,
            "close_result": close_result,
            "operation_log": operation_log
        }
    except Exception as e:
        # 记录错误信息
        error_message = str(e)
        operation_log["status"] = "error"
        operation_log["error"] = error_message

        # 返回错误信息
        return {
            "message": "PR关闭失败",
            "pr_id": pr_id,
            "error": error_message,
            "operation_log": operation_log
        }
