from fastapi import Depends, APIRouter, HTTPException, status, Query
from sqlalchemy.orm import Session
from typing import List, Optional
from sqlalchemy import func, select

from app.core.database import get_db
from app.models.test_case import TestCase
from app.models.test_module import TestFunctionModule
from app.models.project import Project
from app.models.user import User
from app.schemas.test_case import (
    TestCaseCreate,
    TestCaseUpdate,
    TestCaseResponse
)
from app.core.security import get_current_user

router = APIRouter()

@router.get("/test-modules/{project_id}", response_model=List[dict])
def get_test_modules(
    project_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取项目的测试功能模块列表"""
    # 检查项目是否存在
    project = db.query(Project).filter(Project.id == project_id).first()
    if not project:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="项目不存在")

    # 检查用户权限（管理员或项目的测试者可以查看测试功能模块）
    is_tester = any(tst.id == current_user.id for tst in project.testers)
    if not current_user.is_admin and not is_tester:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="无权限查看此项目的测试功能模块")

    # 获取测试功能模块列表
    modules = db.query(TestFunctionModule).filter(TestFunctionModule.project_id == project_id).all()

    # 构建响应
    result = []
    for module in modules:
        result.append({
            "id": module.id,
            "name": module.name,
            "project_id": module.project_id,
            "created_at": module.created_at,
            "updated_at": module.updated_at
        })

    return result

@router.post("/test-cases", response_model=TestCaseResponse)
def create_test_case(
    test_case: TestCaseCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """创建测试用例"""
    # 检查项目是否存在
    project = db.query(Project).filter(Project.id == test_case.project_id).first()
    if not project:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="项目不存在")

    # 检查测试功能模块是否存在
    module = db.query(TestFunctionModule).filter(
        TestFunctionModule.id == test_case.module_id,
        TestFunctionModule.project_id == test_case.project_id
    ).first()
    if not module:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="测试功能模块不存在")

    # 检查用户权限（管理员或项目的测试者可以添加测试用例）
    is_tester = any(tst.id == current_user.id for tst in project.testers)

    if not current_user.is_admin and not is_tester:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="无权限添加测试用例")

    # 创建测试用例
    db_test_case = TestCase(
        project_id=test_case.project_id,
        module_id=test_case.module_id,
        test_number=test_case.test_number,
        test_name=test_case.test_name,
        test_type=test_case.test_type,
        test_side=test_case.test_side,
        precondition=test_case.precondition,
        test_steps=test_case.test_steps,
        expected_result=test_case.expected_result,
        related_code=test_case.related_code,
        test_status=test_case.test_status,
        creator_id=current_user.id
    )
    db.add(db_test_case)
    db.commit()
    db.refresh(db_test_case)

    # 构建响应
    response = TestCaseResponse(
        id=db_test_case.id,
        project_id=db_test_case.project_id,
        module_id=db_test_case.module_id,
        module_name=module.name,
        test_number=db_test_case.test_number,
        test_name=db_test_case.test_name,
        test_type=db_test_case.test_type,
        test_side=db_test_case.test_side,
        precondition=db_test_case.precondition,
        test_steps=db_test_case.test_steps,
        expected_result=db_test_case.expected_result,
        related_code=db_test_case.related_code,
        test_status=db_test_case.test_status,
        creator_id=db_test_case.creator_id,
        creator_name=db_test_case.creator.name,
        created_at=db_test_case.created_at,
        updater_id=db_test_case.updater_id,
        updater_name=db_test_case.updater.name if db_test_case.updater else None,
        updated_at=db_test_case.updated_at
    )

    return response

@router.get("/test-cases")
def read_test_cases(
    project_id: Optional[int] = None,
    module_id: Optional[int] = None,
    page: int = Query(1, ge=1, description="页码，从1开始"),
    pageSize: int = Query(10, ge=1, le=100, description="每页条数"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取测试用例列表，支持分页"""
    print(f"测试用例列表查询 - 当前用户ID: {current_user.id}, 是否管理员: {current_user.is_admin}")
    print(f"筛选条件 - 项目ID: {project_id}, 模块ID: {module_id}")
    print(f"分页参数 - 页码: {page}, 每页条数: {pageSize}")

    base_query = db.query(TestCase)

    # 按项目ID过滤
    if project_id:
        base_query = base_query.filter(TestCase.project_id == project_id)

        # 检查用户权限（管理员或项目的测试者可以查看测试用例）
        project = db.query(Project).filter(Project.id == project_id).first()
        if not project:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="项目不存在")

        is_tester = any(tst.id == current_user.id for tst in project.testers)
        if not current_user.is_admin and not is_tester:
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="无权限查看此项目的测试用例")

    # 按模块ID过滤
    if module_id:
        base_query = base_query.filter(TestCase.module_id == module_id)

    # 如果不是管理员，只能查看自己创建的测试用例
    if not current_user.is_admin and not project_id:
        base_query = base_query.filter(TestCase.creator_id == current_user.id)

    # 计算总数 - 使用兼容不同SQLAlchemy版本的方式
    try:
        # 直接使用count()方法
        total_count = base_query.count()
    except:
        try:
            # 尝试新版本的API
            count_query = select(func.count()).select_from(base_query.subquery())
            total_count = db.execute(count_query).scalar()
        except:
            # 回退到旧版本的用法
            count_query = base_query.statement.with_only_columns([func.count()]).order_by(None)
            total_count = db.execute(count_query).scalar()

    # 分页
    skip = (page - 1) * pageSize
    query = base_query.offset(skip).limit(pageSize)

    # 获取测试用例列表
    test_cases = query.all()

    # 构建响应
    items = []
    for tc in test_cases:
        module = db.query(TestFunctionModule).filter(TestFunctionModule.id == tc.module_id).first()
        items.append(
            TestCaseResponse(
                id=tc.id,
                project_id=tc.project_id,
                module_id=tc.module_id,
                module_name=module.name if module else "",
                test_number=tc.test_number,
                test_name=tc.test_name,
                test_type=tc.test_type,
                test_side=tc.test_side,
                precondition=tc.precondition,
                test_steps=tc.test_steps,
                expected_result=tc.expected_result,
                related_code=tc.related_code,
                test_status=tc.test_status,
                creator_id=tc.creator_id,
                creator_name=tc.creator.name,
                created_at=tc.created_at,
                updater_id=tc.updater_id,
                updater_name=tc.updater.name if tc.updater else None,
                updated_at=tc.updated_at
            )
        )

    # 返回分页结果
    return {
        "data": items,
        "total": total_count,
        "page": page,
        "pageSize": pageSize
    }

@router.get("/test-cases/{test_case_id}", response_model=TestCaseResponse)
def read_test_case(
    test_case_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取测试用例详情"""
    # 获取测试用例
    test_case = db.query(TestCase).filter(TestCase.id == test_case_id).first()
    if not test_case:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="测试用例不存在")

    # 检查用户权限（管理员或项目的测试者可以查看测试用例）
    project = db.query(Project).filter(Project.id == test_case.project_id).first()
    is_tester = any(tst.id == current_user.id for tst in project.testers)

    if not current_user.is_admin and not is_tester and test_case.creator_id != current_user.id:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="无权限查看此测试用例")

    # 获取模块信息
    module = db.query(TestFunctionModule).filter(TestFunctionModule.id == test_case.module_id).first()

    # 构建响应
    response = TestCaseResponse(
        id=test_case.id,
        project_id=test_case.project_id,
        module_id=test_case.module_id,
        module_name=module.name if module else "",
        test_number=test_case.test_number,
        test_name=test_case.test_name,
        test_type=test_case.test_type,
        test_side=test_case.test_side,
        precondition=test_case.precondition,
        test_steps=test_case.test_steps,
        expected_result=test_case.expected_result,
        related_code=test_case.related_code,
        test_status=test_case.test_status,
        creator_id=test_case.creator_id,
        creator_name=test_case.creator.name,
        created_at=test_case.created_at,
        updater_id=test_case.updater_id,
        updater_name=test_case.updater.name if test_case.updater else None,
        updated_at=test_case.updated_at
    )

    return response

@router.put("/test-cases/{test_case_id}", response_model=TestCaseResponse)
def update_test_case(
    test_case_id: int,
    test_case_update: TestCaseUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """更新测试用例"""
    # 获取测试用例
    db_test_case = db.query(TestCase).filter(TestCase.id == test_case_id).first()
    if not db_test_case:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="测试用例不存在")

    # 检查用户权限（管理员、测试用例创建者或项目的测试者可以更新测试用例）
    project = db.query(Project).filter(Project.id == db_test_case.project_id).first()
    is_tester = any(tst.id == current_user.id for tst in project.testers)

    if not current_user.is_admin and not is_tester and db_test_case.creator_id != current_user.id:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="无权限更新此测试用例")

    # 如果更新了模块ID，检查模块是否存在
    if test_case_update.module_id is not None:
        module = db.query(TestFunctionModule).filter(
            TestFunctionModule.id == test_case_update.module_id,
            TestFunctionModule.project_id == db_test_case.project_id
        ).first()
        if not module:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="测试功能模块不存在")
        db_test_case.module_id = test_case_update.module_id

    # 更新测试用例字段
    if test_case_update.test_number is not None:
        db_test_case.test_number = test_case_update.test_number
    if test_case_update.test_name is not None:
        db_test_case.test_name = test_case_update.test_name
    if test_case_update.test_type is not None:
        db_test_case.test_type = test_case_update.test_type
    if test_case_update.test_side is not None:
        db_test_case.test_side = test_case_update.test_side
    if test_case_update.precondition is not None:
        db_test_case.precondition = test_case_update.precondition
    if test_case_update.test_steps is not None:
        db_test_case.test_steps = test_case_update.test_steps
    if test_case_update.expected_result is not None:
        db_test_case.expected_result = test_case_update.expected_result
    if test_case_update.related_code is not None:
        db_test_case.related_code = test_case_update.related_code
    if test_case_update.test_status is not None:
        db_test_case.test_status = test_case_update.test_status

    # 更新修改人和修改时间
    db_test_case.updater_id = current_user.id

    db.commit()
    db.refresh(db_test_case)

    # 获取模块信息
    module = db.query(TestFunctionModule).filter(TestFunctionModule.id == db_test_case.module_id).first()

    # 构建响应
    response = TestCaseResponse(
        id=db_test_case.id,
        project_id=db_test_case.project_id,
        module_id=db_test_case.module_id,
        module_name=module.name if module else "",
        test_number=db_test_case.test_number,
        test_name=db_test_case.test_name,
        test_type=db_test_case.test_type,
        test_side=db_test_case.test_side,
        precondition=db_test_case.precondition,
        test_steps=db_test_case.test_steps,
        expected_result=db_test_case.expected_result,
        related_code=db_test_case.related_code,
        test_status=db_test_case.test_status,
        creator_id=db_test_case.creator_id,
        creator_name=db_test_case.creator.name,
        created_at=db_test_case.created_at,
        updater_id=db_test_case.updater_id,
        updater_name=db_test_case.updater.name if db_test_case.updater else None,
        updated_at=db_test_case.updated_at
    )

    return response