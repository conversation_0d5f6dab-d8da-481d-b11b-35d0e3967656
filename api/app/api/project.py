from fastapi import Depends, APIRouter, HTTPException, status
from sqlalchemy.orm import Session, joinedload
from typing import List, Optional, Dict, Any
from datetime import datetime

from app.core.database import get_db
from app.models.project import Project as ProjectModel
from app.models.user import User
from app.models.associations import project_developers_table, project_testers_table
from app.schemas.project import ProjectCreate, ProjectUpdate, Project, GitLabProject, ProjectMemberCreate, MergeRequestCreate
from app.schemas.user import User as UserSchema
from app.core.security import get_current_user
from app.utils.gitlab_api import gitlab_api
import random
import string

router = APIRouter()

def generate_project_id() -> str:
    # 生成一个简单的随机字符串作为临时 ID
    return 'P' + ''.join(random.choices(string.ascii_uppercase + string.digits, k=7))

@router.post("/projects", response_model=Project)
def create_project(project: ProjectCreate, db: Session = Depends(get_db), current_user: User = Depends(get_current_user)):
    """创建新项目"""
    if not current_user.is_admin:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="只有管理员可以创建项目")

    # 检查GitLab项目是否存在
    try:
        # 这里可以添加对git_repo_url的验证逻辑
        pass
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=f"验证GitLab仓库失败: {str(e)}")

    # 设置项目ID
    db_project = ProjectModel(
        name=project.name,
        git_repo_url=project.git_repo_url,
        description=project.description,
        creator_id=current_user.id
    )

    # 如果前端传入了GitLab项目ID，直接使用该ID
    if project.id:
        db_project.id = project.id

    # 处理开发人员
    if project.developer_ids:
        developers = db.query(User).filter(User.id.in_(project.developer_ids)).all()
        if len(developers) != len(project.developer_ids):
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="一个或多个开发人员ID未找到")
        db_project.developers = developers

    # 处理测试人员
    if project.tester_ids:
        testers = db.query(User).filter(User.id.in_(project.tester_ids)).all()
        if len(testers) != len(project.tester_ids):
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="一个或多个测试人员ID未找到")
        db_project.testers = testers

    db.add(db_project)
    db.commit()
    db.refresh(db_project)
    return db_project

@router.get("/projects", response_model=List[Project])
def read_projects(db: Session = Depends(get_db), current_user: User = Depends(get_current_user)):
    """获取所有项目"""
    if current_user.is_admin:
        # 管理员可以看到所有项目
        return db.query(ProjectModel).all()
    else:
        # 普通用户只能看到自己参与的项目
        my_projects = []
        # 添加用户作为开发者的项目
        dev_projects = current_user.developed_projects
        # 添加用户作为测试者的项目
        test_projects = current_user.tested_projects
        # 合并并去重
        for p in dev_projects + test_projects:
            if p not in my_projects:  # 这里假设 Project 对象有合适的 __eq__ 方法
                my_projects.append(p)
        return my_projects

@router.get("/projects/{project_id}", response_model=Project)
def read_project(project_id: int, db: Session = Depends(get_db), current_user: User = Depends(get_current_user)):
    """获取特定项目的详细信息"""
    db_project = db.query(ProjectModel).filter(ProjectModel.id == project_id).first()
    if db_project is None:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="项目未找到")

    # 检查用户访问权限
    if not current_user.is_admin and current_user.id != db_project.creator_id and current_user not in db_project.developers and current_user not in db_project.testers:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="无权访问该项目")

    return db_project

@router.put("/projects/{project_id}", response_model=Project)
def update_project(
    project_id: int,
    project_update: ProjectUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """更新项目信息"""
    db_project = db.query(ProjectModel).filter(ProjectModel.id == project_id).first()
    if db_project is None:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="项目未找到")

    # 只有管理员或项目创建者可以更新项目信息
    if not current_user.is_admin and current_user.id != db_project.creator_id:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="无权更新该项目")

    # 更新项目基本信息
    if project_update.name is not None:
        db_project.name = project_update.name
    if project_update.git_repo_url is not None:
        db_project.git_repo_url = project_update.git_repo_url
    if project_update.description is not None:
        db_project.description = project_update.description

    # 更新开发人员
    if project_update.developer_ids is not None:
        developers = db.query(User).filter(User.id.in_(project_update.developer_ids)).all()
        if len(developers) != len(project_update.developer_ids):
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="一个或多个开发人员ID未找到")
        db_project.developers = developers

    # 更新测试人员
    if project_update.tester_ids is not None:
        testers = db.query(User).filter(User.id.in_(project_update.tester_ids)).all()
        if len(testers) != len(project_update.tester_ids):
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="一个或多个测试人员ID未找到")
        db_project.testers = testers

    db.commit()
    db.refresh(db_project)
    return db_project

@router.delete("/projects/{project_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_project(project_id: int, db: Session = Depends(get_db), current_user: User = Depends(get_current_user)):
    """删除项目"""
    if not current_user.is_admin:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="只有管理员可以删除项目")

    db_project = db.query(ProjectModel).filter(ProjectModel.id == project_id).first()
    if db_project is None:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="项目未找到")

    db.delete(db_project)
    db.commit()
    return {"detail": "项目已删除"}

@router.get("/projects/{id}/members", response_model=List[UserSchema])
def read_project_members(id: int, role: Optional[str] = None, db: Session = Depends(get_db), current_user: User = Depends(get_current_user)):
    # 检查项目是否存在
    project = db.query(ProjectModel).filter(ProjectModel.id == id).first()
    if not project:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="项目未找到")

    # 权限检查：管理员或项目的创建者、开发者、测试者可以查看
    is_developer = any(dev.id == current_user.id for dev in project.developers)
    is_tester = any(tst.id == current_user.id for tst in project.testers)

    if not current_user.is_admin and project.creator_id != current_user.id and not is_developer and not is_tester:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="无权限查看此项目成员")

    # 根据role参数过滤成员
    all_members = []

    # 获取开发人员
    if role is None or role == "developer":
        # 从project_developers_table中获取join_date
        dev_joins = db.query(project_developers_table).filter(
            project_developers_table.c.project_id == id
        ).all()

        # 为每个开发人员创建带有join_date的字典
        for dev in project.developers:
            # 查找对应的join_date
            join_date = None
            for dj in dev_joins:
                if dj.user_id == dev.id:
                    join_date = dj.join_date
                    break

            member_dict = {
                "id": dev.id,
                "username": dev.username,
                "name": dev.name,
                "is_admin": dev.is_admin,
                "is_active": dev.is_active,
                "role": "developer",
                "created_at": dev.created_at,
                "join_date": join_date
            }
            all_members.append(member_dict)

    # 获取测试人员
    if role is None or role == "tester":
        # 从project_testers_table中获取join_date
        tester_joins = db.query(project_testers_table).filter(
            project_testers_table.c.project_id == id
        ).all()

        # 为每个测试人员创建带有join_date的字典
        for tester in project.testers:
            # 查找对应的join_date
            join_date = None
            for tj in tester_joins:
                if tj.user_id == tester.id:
                    join_date = tj.join_date
                    break

            member_dict = {
                "id": tester.id,
                "username": tester.username,
                "name": tester.name,
                "is_admin": tester.is_admin,
                "is_active": tester.is_active,
                "role": "tester",
                "created_at": tester.created_at,
                "join_date": join_date
            }
            all_members.append(member_dict)

    return all_members

@router.get("/projects/{id}/developers", response_model=List[UserSchema])
def read_project_developers(id: int, db: Session = Depends(get_db), current_user: User = Depends(get_current_user)):
    """获取项目的开发人员列表"""
    # 检查项目是否存在
    project = db.query(ProjectModel).filter(ProjectModel.id == id).first()
    if not project:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="项目未找到")

    # 权限检查：管理员或项目的创建者、开发者、测试者可以查看
    is_developer = any(dev.id == current_user.id for dev in project.developers)
    is_tester = any(tst.id == current_user.id for tst in project.testers)

    if not current_user.is_admin and project.creator_id != current_user.id and not is_developer and not is_tester:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="无权限查看此项目开发人员")

    # 使用提供的SQL逻辑获取项目开发人员
    # select pd.* from projects p inner join project_developers pd on p.id = pd.project_id where p.id = ?
    developers = []

    # 从project_developers_table中获取join_date
    dev_joins = db.query(project_developers_table).filter(
        project_developers_table.c.project_id == id
    ).all()

    # 为每个开发人员创建带有join_date的字典
    for dev in project.developers:
        # 查找对应的join_date
        join_date = None
        for dj in dev_joins:
            if dj.user_id == dev.id:
                join_date = dj.join_date
                break

        member_dict = {
            "id": dev.id,
            "username": dev.username,
            "name": dev.name,
            "is_admin": dev.is_admin,
            "is_active": dev.is_active,
            "role": "developer",
            "created_at": dev.created_at,
            "join_date": join_date
        }
        developers.append(member_dict)

    return developers

@router.post("/projects/{id}/members", status_code=status.HTTP_201_CREATED)
def add_project_member(id: int, member_data: ProjectMemberCreate, db: Session = Depends(get_db), current_user: User = Depends(get_current_user)):
    if not current_user.is_admin:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="只有管理员可以添加项目成员")

    # 检查项目是否存在
    project = db.query(ProjectModel).filter(ProjectModel.id == id).first()
    if not project:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="项目未找到")

    # 检查用户是否存在
    user_id = member_data.user_id
    role = member_data.role

    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="用户未找到")

    # 验证角色参数
    if role not in ["developer", "tester"]:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="无效的角色，必须是 'developer' 或 'tester'")

    # 根据角色添加到开发人员或测试人员
    if role == "developer":
        # 检查用户是否已经是开发人员
        if user in project.developers:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="该用户已经是项目开发人员")
        project.developers.append(user)
    elif role == "tester":
        # 检查用户是否已经是测试人员
        if user in project.testers:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="该用户已经是项目测试人员")
        project.testers.append(user)

    db.commit()
    return {"status": "success", "message": "成员添加成功"}

@router.delete("/projects/{id}/members/{user_id}", status_code=status.HTTP_204_NO_CONTENT)
def remove_project_member(id: int, user_id: int, role: str, db: Session = Depends(get_db), current_user: User = Depends(get_current_user)):
    if not current_user.is_admin:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="只有管理员可以移除项目成员")

    # 检查项目是否存在
    project = db.query(ProjectModel).filter(ProjectModel.id == id).first()
    if not project:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="项目未找到")

    # 检查用户是否存在
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="用户未找到")

    # 验证角色参数
    if role not in ["developer", "tester"]:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="无效的角色，必须是 'developer' 或 'tester'")

    # 根据角色移除用户
    if role == "developer":
        if user not in project.developers:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="该用户不是项目开发人员")
        project.developers.remove(user)
    elif role == "tester":
        if user not in project.testers:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="该用户不是项目测试人员")
        project.testers.remove(user)

    db.commit()
    return None

# 添加获取GitLab项目列表的接口
@router.get("/gitlab-projects", response_model=List[GitLabProject])
def get_gitlab_projects(current_user: User = Depends(get_current_user)):
    """获取GitLab项目列表"""
    if not current_user.is_admin:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="只有管理员可以查看GitLab项目列表")

    try:
        projects = gitlab_api.get_projects()
        return projects
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"获取GitLab项目列表失败: {str(e)}")

# 添加获取GitLab项目分支列表的接口
@router.get("/projects/{project_id}/dev_branches", response_model=List[Dict[str, Any]])
def get_project_branches(project_id: int, current_user: User = Depends(get_current_user)):
    """获取GitLab项目的分支列表"""
    # 检查项目是否存在
    db = next(get_db())
    project = db.query(ProjectModel).filter(ProjectModel.id == project_id).first()
    if not project:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="项目未找到")

    # 权限检查：管理员或项目的创建者、开发者、测试者可以查看
    is_developer = any(dev.id == current_user.id for dev in project.developers)
    is_tester = any(tst.id == current_user.id for tst in project.testers)

    if not current_user.is_admin and project.creator_id != current_user.id and not is_developer and not is_tester:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="无权限查看此项目分支")

    try:
        # 获取GitLab项目分支列表
        branches = gitlab_api.get_dev_branches(project_id)
        # 格式化返回数据，只返回分支名称和其他必要信息
        formatted_branches = []
        for branch in branches:
            formatted_branches.append({
                "name": branch.get("name", ""),
                "value": branch.get("name", ""),
                "commit": branch.get("commit", {})
            })
        return formatted_branches
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"获取GitLab项目分支列表失败: {str(e)}")

# 添加创建合并请求的接口
@router.post("/projects/{project_id}/merge_requests", response_model=Dict[str, Any])
def create_merge_request(
    project_id: int,
    merge_request: MergeRequestCreate,
    current_user: User = Depends(get_current_user)
):
    """
    在GitLab项目中创建合并请求(PR)

    Args:
        project_id: GitLab项目ID
        merge_request: 合并请求数据，包含源分支、目标分支、标题和描述

    Returns:
        创建的合并请求信息
    """
    # 检查项目是否存在
    db = next(get_db())
    project = db.query(ProjectModel).filter(ProjectModel.id == project_id).first()
    if not project:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="项目未找到")

    # 权限检查：管理员或项目的创建者、开发者可以创建合并请求
    is_developer = any(dev.id == current_user.id for dev in project.developers)

    if not current_user.is_admin and project.creator_id != current_user.id and not is_developer:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="无权限为此项目创建合并请求")

    try:
        # 调用GitLab API创建合并请求
        result = gitlab_api.create_merge_request(
            project_id=project_id,
            source_branch=merge_request.source_branch,
            target_branch=merge_request.target_branch,
            title=merge_request.title,
            description=merge_request.description
        )
        return result
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"创建GitLab合并请求失败: {str(e)}")