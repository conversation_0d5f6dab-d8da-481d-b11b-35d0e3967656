from fastapi import Depends, APIRouter, HTTPException, status
from sqlalchemy.orm import Session
from typing import List, Optional

from app.core.database import get_db
from app.models.test_module import TestFunctionModule
from app.models.project import Project
from app.models.user import User
from app.schemas.test_module import (
    TestFunctionModuleCreate,
    TestFunctionModuleUpdate,
    TestFunctionModuleResponse
)
from app.core.security import get_current_user

router = APIRouter()

@router.post("/projects/{project_id}/test-modules", response_model=TestFunctionModuleResponse)
def create_test_module(
    project_id: int,
    test_module: TestFunctionModuleCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """创建测试功能模块"""
    # 检查项目是否存在
    project = db.query(Project).filter(Project.id == project_id).first()
    if not project:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="项目不存在")

    # 检查用户权限（管理员或项目的创建者、开发者、测试者可以添加测试功能模块）
    is_developer = any(dev.id == current_user.id for dev in project.developers)
    is_tester = any(tst.id == current_user.id for tst in project.testers)

    if not current_user.is_admin and project.creator_id != current_user.id and not is_developer and not is_tester:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="无权限添加测试功能模块")

    # 创建测试功能模块
    db_test_module = TestFunctionModule(
        project_id=project_id,
        name=test_module.name
    )
    db.add(db_test_module)
    db.commit()
    db.refresh(db_test_module)
    return db_test_module

@router.get("/projects/{project_id}/test-modules", response_model=List[TestFunctionModuleResponse])
def read_test_modules(
    project_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取项目的测试功能模块列表"""
    # 检查项目是否存在
    project = db.query(Project).filter(Project.id == project_id).first()
    if not project:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="项目不存在")

    # 检查用户权限（管理员或项目的创建者、开发者、测试者可以查看测试功能模块）
    is_developer = any(dev.id == current_user.id for dev in project.developers)
    is_tester = any(tst.id == current_user.id for tst in project.testers)

    if not current_user.is_admin and project.creator_id != current_user.id and not is_developer and not is_tester:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="无权限查看测试功能模块")

    # 获取测试功能模块列表
    test_modules = db.query(TestFunctionModule).filter(TestFunctionModule.project_id == project_id).all()
    return test_modules

@router.put("/projects/{project_id}/test-modules/{test_module_id}", response_model=TestFunctionModuleResponse)
def update_test_module(
    project_id: int,
    test_module_id: int,
    test_module: TestFunctionModuleUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """更新测试功能模块"""
    # 检查项目是否存在
    project = db.query(Project).filter(Project.id == project_id).first()
    if not project:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="项目不存在")

    # 检查测试功能模块是否存在
    db_test_module = db.query(TestFunctionModule).filter(
        TestFunctionModule.id == test_module_id,
        TestFunctionModule.project_id == project_id
    ).first()
    if not db_test_module:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="测试功能模块不存在")

    # 检查用户权限（管理员或项目的创建者、开发者、测试者可以更新测试功能模块）
    is_developer = any(dev.id == current_user.id for dev in project.developers)
    is_tester = any(tst.id == current_user.id for tst in project.testers)

    if not current_user.is_admin and project.creator_id != current_user.id and not is_developer and not is_tester:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="无权限更新测试功能模块")

    # 更新测试功能模块
    if test_module.name is not None:
        db_test_module.name = test_module.name

    db.commit()
    db.refresh(db_test_module)
    return db_test_module

@router.delete("/projects/{project_id}/test-modules/{test_module_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_test_module(
    project_id: int,
    test_module_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """删除测试功能模块"""
    # 检查项目是否存在
    project = db.query(Project).filter(Project.id == project_id).first()
    if not project:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="项目不存在")

    # 检查测试功能模块是否存在
    db_test_module = db.query(TestFunctionModule).filter(
        TestFunctionModule.id == test_module_id,
        TestFunctionModule.project_id == project_id
    ).first()
    if not db_test_module:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="测试功能模块不存在")

    # 检查用户权限（管理员或项目的创建者可以删除测试功能模块）
    if not current_user.is_admin and project.creator_id != current_user.id:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="无权限删除测试功能模块")

    # 删除测试功能模块
    db.delete(db_test_module)
    db.commit()
    return
