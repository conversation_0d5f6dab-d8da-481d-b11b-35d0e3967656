from typing import Optional
from pydantic import BaseModel, Field
from datetime import datetime
from app.schemas.base import BaseResponseModel

# 测试功能模块基本信息
class TestFunctionModuleBase(BaseModel):
    name: str = Field(..., description="功能模块名称，中文")

# 创建测试功能模块时的数据模型
class TestFunctionModuleCreate(TestFunctionModuleBase):
    project_id: int = Field(..., description="关联的项目ID")

# 更新测试功能模块时的数据模型
class TestFunctionModuleUpdate(BaseModel):
    name: Optional[str] = Field(None, description="功能模块名称")

# 测试功能模块响应模型
class TestFunctionModuleResponse(TestFunctionModuleBase, BaseResponseModel):
    id: int
    project_id: int
    created_at: datetime
    updated_at: Optional[datetime] = None
