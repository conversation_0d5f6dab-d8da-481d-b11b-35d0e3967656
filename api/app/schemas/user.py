from pydantic import BaseModel
from datetime import datetime
from typing import Optional
from app.schemas.base import BaseResponseModel

class UserLogin(BaseModel):
    username: str
    password: str

class PasswordChange(BaseModel):
    current_password: str
    new_password: str

class Token(BaseModel):
    access_token: str
    token_type: str

class TokenData(BaseModel):
    username: str | None = None

class UserBase(BaseModel):
    username: str
    name: str

class UserCreate(UserBase):
    password: str

class UserUpdate(UserBase):
    password: Optional[str] = None
    is_admin: Optional[bool] = None
    is_active: Optional[bool] = None

class User(UserBase, BaseResponseModel):
    id: int
    is_admin: bool
    is_active: bool
    created_at: datetime
    join_date: Optional[datetime] = None
    role: Optional[str] = None

    class Config:
        from_attributes = True