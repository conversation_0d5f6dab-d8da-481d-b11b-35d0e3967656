from typing import List, Optional
from pydantic import BaseModel, Field
from datetime import datetime
from app.schemas.base import BaseResponseModel

# 引入 User schema 用于关联字段
from .user import User

class ProjectBase(BaseModel):
    name: str # 项目名称
    git_repo_url: str # Git仓库地址
    description: Optional[str] = None

class ProjectCreate(ProjectBase):
    developer_ids: List[int] = []
    tester_ids: List[int] = []
    id: Optional[int] = None

class ProjectUpdate(BaseModel):
    name: Optional[str] = None
    git_repo_url: Optional[str] = None
    description: Optional[str] = None
    developer_ids: Optional[List[int]] = None
    tester_ids: Optional[List[int]] = None

class Project(BaseResponseModel):
    id: int
    name: str
    git_repo_url: str
    description: Optional[str] = None
    creator_id: int
    created_at: datetime
    developers: List[User] = []
    testers: List[User] = []

# 添加用于项目成员管理的模型
class ProjectMemberCreate(BaseModel):
    user_id: int
    role: str  # 'developer' 或 'tester'

# 添加用于GitLab项目列表的模型
class GitLabProject(BaseModel):
    id: int
    name: str
    description: Optional[str] = None
    http_url_to_repo: str
    web_url: str
    path_with_namespace: str
    name_with_namespace: str

# 添加用于创建合并请求的模型
class MergeRequestCreate(BaseModel):
    source_branch: str = Field(..., description="源分支名称")
    target_branch: str = Field(..., description="目标分支名称")
    title: Optional[str] = Field(None, description="合并请求标题，默认为'Merge {source_branch} to {target_branch}'")
    description: Optional[str] = Field(None, description="合并请求描述")