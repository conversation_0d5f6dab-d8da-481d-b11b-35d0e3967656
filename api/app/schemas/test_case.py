from typing import Optional
from pydantic import BaseModel, Field
from datetime import datetime
from enum import Enum
from app.schemas.base import BaseResponseModel

# 测试类型枚举
class TestType(str, Enum):
    FUNCTIONAL = "功能测试"
    EXCEPTION = "异常测试"

# 测试状态枚举
class TestStatus(str, Enum):
    PASS = "PASS"
    FAIL = "FAIL"

# 测试端枚举
class TestSide(str, Enum):
    FRONTEND = "前端测试"
    BACKEND = "后端测试"

# 测试用例基本信息
class TestCaseBase(BaseModel):
    test_number: str = Field(..., description="测试编号，手动输入")
    test_name: str = Field(..., description="测试名称")
    test_type: TestType = Field(..., description="测试类型，功能测试或异常测试")
    test_side: TestSide = Field(default=TestSide.BACKEND, description="测试端，前端测试或后端测试")
    precondition: Optional[str] = Field(None, description="前提条件")
    test_steps: str = Field(..., description="测试步骤")
    expected_result: str = Field(..., description="预期结果")
    related_code: str = Field(..., description="关联代码")
    test_status: TestStatus = Field(..., description="测试状态，PASS或FAIL")

# 创建测试用例时的数据模型
class TestCaseCreate(TestCaseBase):
    project_id: int = Field(..., description="关联的项目ID")
    module_id: int = Field(..., description="关联的测试功能模块ID")

# 更新测试用例时的数据模型
class TestCaseUpdate(BaseModel):
    test_number: Optional[str] = Field(None, description="测试编号")
    test_name: Optional[str] = Field(None, description="测试名称")
    test_type: Optional[TestType] = Field(None, description="测试类型")
    test_side: Optional[TestSide] = Field(None, description="测试端，前端测试或后端测试")
    precondition: Optional[str] = Field(None, description="前提条件")
    test_steps: Optional[str] = Field(None, description="测试步骤")
    expected_result: Optional[str] = Field(None, description="预期结果")
    related_code: Optional[str] = Field(None, description="关联代码")
    test_status: Optional[TestStatus] = Field(None, description="测试状态")
    module_id: Optional[int] = Field(None, description="关联的测试功能模块ID")

# 测试用例响应模型
class TestCaseResponse(TestCaseBase, BaseResponseModel):
    id: int
    project_id: int
    module_id: int
    module_name: str
    creator_id: int
    creator_name: str
    created_at: datetime
    updater_id: Optional[int] = None
    updater_name: Optional[str] = None
    updated_at: Optional[datetime] = None

# 测试用例列表查询参数
class TestCaseQueryParams(BaseModel):
    project_id: Optional[int] = None
    module_id: Optional[int] = None
    test_type: Optional[TestType] = None
    test_side: Optional[TestSide] = None
    test_status: Optional[TestStatus] = None
    creator_id: Optional[int] = None
