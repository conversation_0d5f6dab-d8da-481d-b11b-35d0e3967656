"""
基础Schema模块
提供带有时区转换功能的基础Pydantic模型
"""

from datetime import datetime
from typing import Any, Optional
from pydantic import BaseModel, field_validator
from app.utils.timezone_utils import convert_utc_to_china_timezone

class TimezoneAwareBaseModel(BaseModel):
    """
    带有时区转换功能的基础Pydantic模型
    自动将所有datetime字段从UTC转换为中国时区
    """
    
    @field_validator('*', mode='before')
    @classmethod
    def convert_datetime_to_china_timezone(cls, value: Any, info) -> Any:
        """
        通用的datetime字段验证器，自动转换时区
        
        Args:
            value: 字段值
            info: 字段信息
            
        Returns:
            转换后的值（如果是datetime则转换为中国时区）
        """
        # 如果是datetime实例，直接进行时区转换
        if isinstance(value, datetime):
            return convert_utc_to_china_timezone(value)
        
        return value

class BaseResponseModel(TimezoneAwareBaseModel):
    """
    响应模型基类
    继承时区转换功能
    """
    
    class Config:
        from_attributes = True 