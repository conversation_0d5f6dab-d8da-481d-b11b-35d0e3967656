from datetime import datetime, timedelta
from typing import Optional
import logging
from jose import JWTError, jwt
from passlib.context import CryptContext
from app.core.config import settings

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("auth.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 密码加密
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# JWT相关配置
SECRET_KEY = "your-secret-key-here"  # 在生产环境中应该使用环境变量
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30

def verify_password(plain_password: str, hashed_password: str) -> bool:
    logger.info(f"验证密码: 明文密码长度={len(plain_password)}, 哈希密码={hashed_password}")
    try:
        result = pwd_context.verify(plain_password, hashed_password)
        logger.info(f"密码验证结果: {result}")
        return result
    except Exception as e:
        logger.error(f"密码验证出错: {str(e)}")
        return False

def get_password_hash(password: str) -> str:
    hashed = pwd_context.hash(password)
    logger.info(f"生成密码哈希: 明文密码长度={len(password)}, 生成的哈希={hashed}")
    return hashed

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None) -> str:
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=15)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

from fastapi import Depends, HTTPException, status
from fastapi.security import OAuth2PasswordBearer
from jose import JWTError, jwt
from sqlalchemy.orm import Session
from app.core.database import get_db
from app.models.user import User
from app.schemas.user import TokenData

oauth2_scheme = OAuth2PasswordBearer(tokenUrl="login")

async def get_current_user(token: str = Depends(oauth2_scheme), db: Session = Depends(get_db)):
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )

    logger.info("验证用户令牌")
    try:
        # 解码JWT令牌
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        logger.info(f"令牌解码成功，用户名: {username}")

        if username is None:
            logger.error("令牌中没有用户名")
            raise credentials_exception

        token_data = TokenData(username=username)
    except JWTError as e:
        logger.error(f"JWT解码错误: {str(e)}")
        raise credentials_exception

    # 从数据库获取用户
    user = db.query(User).filter(User.username == token_data.username).first()

    if user is None:
        logger.error(f"数据库中未找到用户: {token_data.username}")
        raise credentials_exception

    logger.info(f"用户验证成功: {user.username}, ID: {user.id}")
    return user