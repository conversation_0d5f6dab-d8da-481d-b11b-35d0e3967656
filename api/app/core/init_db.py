from sqlalchemy.orm import Session
from app.core.database import SessionLocal
from app.models.user import User
from app.core.security import get_password_hash

def init_db():
    db = SessionLocal()
    try:
        # 检查是否已存在测试用户
        test_user = db.query(User).filter(User.username == "admin").first()
        if not test_user:
            # 创建测试用户
            test_user = User(
                username="admin",
                password=get_password_hash("admin123")
            )
            db.add(test_user)
            db.commit()
            print("测试用户创建成功")
        else:
            print("测试用户已存在")
    finally:
        db.close()

if __name__ == "__main__":
    init_db() 