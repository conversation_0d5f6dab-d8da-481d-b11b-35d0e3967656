from pydantic_settings import BaseSettings
from typing import Optional, List

class Settings(BaseSettings):
    API_V1_STR: str = "/api/v1"
    PROJECT_NAME: str = "Scan Git API"

    # 数据库连接URL
    DATABASE_URL: str = "sqlite:///./site.db"
    # CORS配置
    BACKEND_CORS_ORIGINS: list = ["*"]

    # GitLab配置
    GITLAB_ACCESS_TOKEN: str = "**************************"
    GITLAB_API_URL: str = "http://10.0.20.2:13000/api/v4"

    # 企业微信配置
    WECHAT_WORK_CORPID: str = "wx328cf526c625fff6"
    WECHAT_WORK_SECRET: str = "fzObuAV8blU4aXlOm8J0rNuHE7L1b0hZxaVJUkrze5Q"
    WECHAT_WORK_AGENTID: int = 1000022
    WECHAT_WORK_ENABLED: bool = True  # 是否启用企业微信通知

    class Config:
        case_sensitive = True

settings = Settings()