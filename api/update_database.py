from sqlalchemy import create_engine, inspect
from sqlalchemy.orm import sessionmaker
from app.core.config import settings
from app.core.database import Base
from app.models.user import User
from app.core.security import get_password_hash
import logging
import csv
import os

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def sync_users_from_csv(session):
    """
    从CSV文件同步用户数据到数据库：
    1. 读取data/users.csv文件中的用户数据
    2. 检查数据库中是否存在对应用户
    3. 如果不存在，则添加用户，密码默认为加密后的'123321'
    """
    csv_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'data', 'users.csv')

    if not os.path.exists(csv_path):
        logger.warning(f"用户CSV文件不存在: {csv_path}")
        return

    try:
        with open(csv_path, 'r', encoding='utf-8') as f:
            csv_reader = csv.DictReader(f)
            for row in csv_reader:
                username = row.get('username')
                if not username:
                    continue

                # 检查用户是否已存在
                existing_user = session.query(User).filter(User.username == username).first()
                if not existing_user:
                    # 创建新用户
                    new_user = User(
                        username=username,
                        name=row.get('name', username),
                        password=get_password_hash('123321'),
                        qywx_id=row.get('qywx_id', ''),
                        is_admin=bool(int(row.get('is_admin', 0))),
                        is_active=bool(int(row.get('is_active', 1)))
                    )
                    session.add(new_user)
                    logger.info(f"从CSV添加新用户: {username}")

        # 提交所有新用户
        session.commit()
        logger.info("用户同步完成")
    except Exception as e:
        logger.error(f"从CSV同步用户时出错: {str(e)}")
        session.rollback()

def update_database():
    """
    智能更新数据库结构：
    1. 如果表不存在，则创建表
    2. 如果表存在，检查是否有新的列，并添加这些列
    3. 从CSV文件同步用户数据
    """
    # 创建数据库引擎
    engine = create_engine(settings.DATABASE_URL, echo=True)

    # 创建会话
    Session = sessionmaker(bind=engine)
    session = Session()

    try:
        # 获取检查器对象
        inspector = inspect(engine)

        # 获取现有数据库中的所有表
        existing_tables = inspector.get_table_names()
        logger.info(f"现有数据库表: {existing_tables}")

        # 获取模型定义的所有表
        model_tables = Base.metadata.tables
        logger.info(f"模型定义的表: {list(model_tables.keys())}")

        # 对每个模型表进行处理
        for table_name, table in model_tables.items():
            if table_name not in existing_tables:
                # 表不存在，创建表
                logger.info(f"创建新表: {table_name}")
                table.create(engine, checkfirst=True)
            else:
                # 表已存在，检查列
                existing_columns = {col['name'] for col in inspector.get_columns(table_name)}
                model_columns = {col.name for col in table.columns}

                # 找出缺少的列
                missing_columns = model_columns - existing_columns

                if missing_columns:
                    logger.info(f"表 {table_name} 缺少列: {missing_columns}")

                    # 为每个缺少的列创建 ALTER TABLE 语句
                    for col_name in missing_columns:
                        # 从模型中获取列定义
                        column = next(col for col in table.columns if col.name == col_name)

                        # 构建 ALTER TABLE 语句
                        # 注意：SQLite 对 ALTER TABLE 的支持有限，可能需要更复杂的处理
                        try:
                            # 尝试添加列
                            column_type = str(column.type)
                            nullable = "" if column.nullable else "NOT NULL"

                            # 处理默认值，特别是枚举类型
                            default = ""
                            if column.default is not None and hasattr(column.default, 'arg'):
                                default_value = column.default.arg
                                # 如果默认值是枚举引用，需要转换为字符串值
                                if '.' in str(default_value):
                                    logger.info(f"检测到可能的枚举默认值: {default_value}")
                                    # 尝试从模型中获取实际的枚举值
                                    try:
                                        # 解析枚举类和值
                                        enum_parts = str(default_value).split('.')
                                        if len(enum_parts) == 2:
                                            enum_class_name, enum_value_name = enum_parts
                                            # 从模型中导入枚举类
                                            import importlib
                                            # 假设枚举类在 app.models 包中
                                            for module_name in ['test_case', 'requirement', 'project']:
                                                try:
                                                    module = importlib.import_module(f'app.models.{module_name}')
                                                    if hasattr(module, enum_class_name):
                                                        enum_class = getattr(module, enum_class_name)
                                                        enum_value = getattr(enum_class, enum_value_name)
                                                        logger.info(f"找到枚举值: {enum_value}")
                                                        default = f"DEFAULT '{enum_value}'"
                                                        break
                                                except (ImportError, AttributeError) as e:
                                                    continue
                                            else:
                                                # 如果没有找到枚举值，使用原始字符串
                                                default = f"DEFAULT '{default_value}'"
                                        else:
                                            default = f"DEFAULT '{default_value}'"
                                    except Exception as e:
                                        logger.error(f"处理枚举默认值时出错: {str(e)}")
                                        default = f"DEFAULT '{default_value}'"
                                else:
                                    default = f"DEFAULT {default_value}"

                            alter_stmt = f"ALTER TABLE {table_name} ADD COLUMN {col_name} {column_type} {nullable} {default}"
                            logger.info(f"执行: {alter_stmt}")
                            # SQLAlchemy 2.0 使用 text 执行原始 SQL
                            from sqlalchemy import text
                            with engine.connect() as conn:
                                conn.execute(text(alter_stmt))
                                conn.commit()
                            logger.info(f"已添加列 {col_name} 到表 {table_name}")
                        except Exception as e:
                            logger.error(f"添加列 {col_name} 到表 {table_name} 时出错: {str(e)}")
                else:
                    logger.info(f"表 {table_name} 结构已是最新")

        # 检查并插入默认用户
        existing_user = session.query(User).filter(User.username == 'taowl').first()
        if not existing_user:
            # 插入默认用户
            default_user = User(
                username='taowl',
                password=get_password_hash('123321'),
                name='陶文亮',
                qywx_id='taowenliang',
                is_admin=True
            )
            session.add(default_user)
            session.commit()
            logger.info("已插入默认用户 'taowl'")
        else:
            logger.info("用户 'taowl' 已存在，未进行插入操作")

        # 从CSV文件同步用户
        logger.info("开始从CSV文件同步用户数据...")
        sync_users_from_csv(session)

        logger.info("数据库 schema 已更新，包含了新的字段和表结构")
    except Exception as e:
        logger.error(f"更新数据库时出错: {str(e)}")
        session.rollback()
        raise
    finally:
        session.close()

if __name__ == "__main__":
    update_database()