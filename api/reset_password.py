#!/usr/bin/env python3
"""
重置用户密码的脚本
"""

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from app.models.user import User
from app.core.security import get_password_hash
import logging
import sys

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("password_reset.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def reset_password(username, new_password="123321"):
    """
    重置指定用户的密码
    
    Args:
        username: 用户名
        new_password: 新密码，默认为 "123321"
    """
    # 创建数据库引擎
    engine = create_engine("sqlite:///site.db", echo=False)
    
    # 创建会话
    Session = sessionmaker(bind=engine)
    session = Session()
    
    try:
        # 查找用户
        user = session.query(User).filter(User.username == username).first()
        
        if not user:
            logger.error(f"用户 {username} 不存在")
            return False
        
        # 记录旧密码哈希
        old_password_hash = user.password
        logger.info(f"用户 {username} 的旧密码哈希: {old_password_hash}")
        
        # 生成新密码哈希
        new_password_hash = get_password_hash(new_password)
        logger.info(f"用户 {username} 的新密码哈希: {new_password_hash}")
        
        # 更新密码
        user.password = new_password_hash
        session.commit()
        
        logger.info(f"用户 {username} 的密码已重置")
        return True
    except Exception as e:
        logger.error(f"重置密码时出错: {str(e)}")
        session.rollback()
        return False
    finally:
        session.close()

def reset_all_passwords(new_password="123321"):
    """
    重置所有用户的密码
    
    Args:
        new_password: 新密码，默认为 "123321"
    """
    # 创建数据库引擎
    engine = create_engine("sqlite:///site.db", echo=False)
    
    # 创建会话
    Session = sessionmaker(bind=engine)
    session = Session()
    
    try:
        # 获取所有用户
        users = session.query(User).all()
        
        if not users:
            logger.error("数据库中没有用户")
            return False
        
        # 重置每个用户的密码
        for user in users:
            # 记录旧密码哈希
            old_password_hash = user.password
            logger.info(f"用户 {user.username} 的旧密码哈希: {old_password_hash}")
            
            # 生成新密码哈希
            new_password_hash = get_password_hash(new_password)
            logger.info(f"用户 {user.username} 的新密码哈希: {new_password_hash}")
            
            # 更新密码
            user.password = new_password_hash
        
        # 提交所有更改
        session.commit()
        logger.info(f"所有用户的密码已重置为 '{new_password}'")
        return True
    except Exception as e:
        logger.error(f"重置所有密码时出错: {str(e)}")
        session.rollback()
        return False
    finally:
        session.close()

if __name__ == "__main__":
    if len(sys.argv) > 1:
        # 重置特定用户的密码
        username = sys.argv[1]
        new_password = sys.argv[2] if len(sys.argv) > 2 else "123321"
        
        if reset_password(username, new_password):
            print(f"用户 {username} 的密码已重置为 '{new_password}'")
        else:
            print(f"重置用户 {username} 的密码失败")
    else:
        # 重置所有用户的密码
        if reset_all_passwords():
            print("所有用户的密码已重置为 '123321'")
        else:
            print("重置所有用户的密码失败")
