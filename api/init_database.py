#!/usr/bin/env python3
"""
数据库初始化脚本 - 用于预埋数据

该脚本用于初始化数据库中的基础数据，包括：
1. 从 data/new/users.csv 导入用户数据
2. 从 data/new/test-unit.csv 导入测试用例数据
3. 从 data/new/requirements.csv 导入需求数据

注意：此脚本应该在系统初始化时单独运行，而不是在每次系统启动时运行。
"""

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from app.core.config import settings
from app.models.user import User
from app.models.test_module import TestFunctionModule
from app.models.test_case import TestCase, TestType, TestStatus, TestSide
from app.models.project import Project
from app.models.requirement import (
    Requirement, RequirementAssignment, RequirementHistory,
    RequirementType, Priority, RequirementStatus, RequirementAction
)
from app.core.security import get_password_hash
import logging
import csv
import os
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def import_users_from_csv(session, csv_path):
    """
    从CSV文件导入用户数据到数据库：
    1. 读取CSV文件中的用户数据
    2. 检查数据库中是否存在对应用户
    3. 如果不存在，则添加用户，密码默认为加密后的'123321'
    """
    if not os.path.exists(csv_path):
        logger.warning(f"用户CSV文件不存在: {csv_path}")
        return

    try:
        with open(csv_path, 'r', encoding='utf-8') as f:
            csv_reader = csv.DictReader(f)
            for row in csv_reader:
                username = row.get('username')
                if not username:
                    continue

                # 检查用户是否已存在
                existing_user = session.query(User).filter(User.username == username).first()
                if not existing_user:
                    # 创建新用户
                    new_user = User(
                        username=username,
                        name=row.get('name', username),
                        password=get_password_hash('123321'),
                        qywx_id=row.get('qywx_id', ''),
                        is_admin=bool(int(row.get('is_admin', 0))),
                        is_active=bool(int(row.get('is_active', 1)))
                    )
                    session.add(new_user)
                    logger.info(f"从CSV添加新用户: {username}")

        # 提交所有新用户
        session.commit()
        logger.info("用户导入完成")
    except Exception as e:
        logger.error(f"从CSV导入用户时出错: {str(e)}")
        session.rollback()

def import_test_cases_from_csv(session, csv_path):
    """
    从CSV文件导入测试用例数据到数据库：
    1. 读取CSV文件中的测试用例数据
    2. 为每个测试用例找到或创建对应的测试功能模块
    3. 根据测试人员名字查找对应的用户ID
    4. 创建测试用例记录
    """
    if not os.path.exists(csv_path):
        logger.warning(f"测试用例CSV文件不存在: {csv_path}")
        return

    try:
        # 获取默认项目（如果没有项目，创建一个默认项目）
        default_project = session.query(Project).first()
        if not default_project:
            logger.warning("数据库中没有项目，无法导入测试用例")
            return

        # 获取默认用户（用于找不到对应测试人员时使用）
        default_user = session.query(User).filter(User.is_admin == True).first()
        if not default_user:
            logger.warning("数据库中没有管理员用户，无法导入测试用例")
            return

        # 创建用户名称到ID的映射
        user_map = {}
        users = session.query(User).all()
        for user in users:
            user_map[user.name] = user.id

        # 创建测试功能模块映射（模块名称 -> 模块ID）
        module_map = {}

        with open(csv_path, 'r', encoding='utf-8-sig') as f:  # 使用 utf-8-sig 处理可能的 BOM
            csv_reader = csv.DictReader(f)
            for row in csv_reader:
                module_name = row.get('测试功能模块')
                if not module_name:
                    continue

                # 检查模块是否已在映射中
                if module_name not in module_map:
                    # 检查数据库中是否存在该模块
                    module = session.query(TestFunctionModule).filter(
                        TestFunctionModule.name == module_name,
                        TestFunctionModule.project_id == default_project.id
                    ).first()

                    if not module:
                        # 创建新模块
                        module = TestFunctionModule(
                            project_id=default_project.id,
                            name=module_name
                        )
                        session.add(module)
                        session.flush()  # 获取新创建模块的ID
                        logger.info(f"创建新测试功能模块: {module_name}")

                    module_map[module_name] = module.id

        # 提交模块创建
        session.commit()

        # 重新打开CSV文件，创建测试用例
        with open(csv_path, 'r', encoding='utf-8-sig') as f:
            csv_reader = csv.DictReader(f)
            for row in csv_reader:
                module_name = row.get('测试功能模块')
                if not module_name or module_name not in module_map:
                    continue

                # 获取测试类型
                test_type_str = row.get('测试类型', '')
                if '功能测试' in test_type_str:
                    test_type = TestType.FUNCTIONAL
                elif '异常测试' in test_type_str:
                    test_type = TestType.EXCEPTION
                else:
                    test_type = TestType.FUNCTIONAL  # 默认为功能测试

                # 获取测试端
                test_side = TestSide.BACKEND  # 默认为后端测试
                if row.get('代码类型') == 'Cypress':
                    test_side = TestSide.FRONTEND

                # 获取测试状态
                test_status = TestStatus.PASS if row.get('测试状态') == 'PASS' else TestStatus.FAIL

                # 根据测试人员名字查找对应的用户ID
                tester_name = row.get('测试人员', '')
                creator_id = user_map.get(tester_name)

                # 如果找不到对应的用户ID，使用默认用户ID
                if not creator_id:
                    logger.warning(f"找不到测试人员 '{tester_name}' 对应的用户ID，使用默认用户ID")
                    creator_id = default_user.id

                # 创建测试用例
                test_case = TestCase(
                    project_id=default_project.id,
                    module_id=module_map[module_name],
                    test_number=row.get('测试编号', ''),
                    test_name=row.get('测试名称', ''),
                    test_type=test_type,
                    test_side=test_side,
                    precondition=row.get('前提条件', ''),
                    test_steps=row.get('测试步骤', ''),
                    expected_result=row.get('预期结果', ''),
                    related_code=row.get('关联JUnit测试类', ''),
                    test_status=test_status,
                    creator_id=creator_id
                )
                session.add(test_case)
                logger.info(f"添加测试用例: {row.get('测试编号')} - {row.get('测试名称')} (创建者: {tester_name})")

        # 提交所有测试用例
        session.commit()
        logger.info("测试用例导入完成")
    except Exception as e:
        logger.error(f"从CSV导入测试用例时出错: {str(e)}")
        session.rollback()
        raise

def import_requirements_from_csv(session, csv_path):
    """
    从CSV文件导入需求数据到数据库：
    1. 读取CSV文件中的需求数据
    2. 创建需求记录
    3. 创建需求指派记录
    4. 创建需求分支记录
    5. 创建需求历史记录
    """
    if not os.path.exists(csv_path):
        logger.warning(f"需求CSV文件不存在: {csv_path}")
        return

    try:
        # 获取默认项目（如果没有项目，创建一个默认项目）
        default_project = session.query(Project).first()
        if not default_project:
            logger.warning("数据库中没有项目，创建一个默认项目")
            default_project = Project(
                name="默认项目",
                git_repo_url="https://gitlab.example.com/default/project.git",
                description="默认项目，用于导入需求数据",
                creator_id=1  # 假设ID为1的用户存在
            )
            session.add(default_project)
            session.flush()

        # 创建用户名称到ID的映射
        user_map = {}
        users = session.query(User).all()
        for user in users:
            user_map[user.name] = user.id

        # 读取CSV文件
        with open(csv_path, 'r', encoding='utf-8-sig') as f:
            csv_reader = csv.DictReader(f)

            # 记录已处理的需求编号，避免重复导入
            processed_codes = set()

            for row in csv_reader:
                # 跳过空行或无需求类型的行
                if not row.get('需求类型') or not row.get('需求ID'):
                    continue

                # 跳过已处理的需求编号
                requirement_code = row.get('需求ID')
                if requirement_code in processed_codes:
                    continue
                processed_codes.add(requirement_code)

                # 检查需求是否已存在
                existing_requirement = session.query(Requirement).filter(
                    Requirement.requirement_code == requirement_code
                ).first()

                if existing_requirement:
                    # 如果需求已存在，但状态为COMPLETED且update_time为空，则更新update_time
                    if existing_requirement.status == RequirementStatus.COMPLETED and existing_requirement.update_time is None:
                        existing_requirement.update_time = existing_requirement.submit_time
                        session.commit()
                        logger.info(f"需求 {requirement_code} 已存在，已更新update_time字段")
                    else:
                        logger.info(f"需求 {requirement_code} 已存在，跳过")
                    continue

                # 解析需求类型
                req_type_str = row.get('需求类型')
                if req_type_str == 'B':
                    req_type = RequirementType.BUG_FIX
                elif req_type_str == 'H':
                    req_type = RequirementType.HOT_FIX_BUG
                elif req_type_str == 'F':
                    req_type = RequirementType.NEW_FEATURE
                else:
                    logger.warning(f"未知的需求类型: {req_type_str}，跳过")
                    continue

                # 解析优先级
                priority_str = row.get('优先级')
                if priority_str == 'P0':
                    priority = Priority.P0
                elif priority_str == 'P1':
                    priority = Priority.P1
                elif priority_str == 'P2':
                    priority = Priority.P2
                else:
                    logger.warning(f"未知的优先级: {priority_str}，使用默认值P0")
                    priority = Priority.P0

                # 解析状态
                status_str = row.get('状态')
                if status_str == '已完成':
                    status = RequirementStatus.COMPLETED
                elif status_str == '未开始':
                    status = RequirementStatus.PENDING
                elif status_str == '进行中':
                    status = RequirementStatus.DEVELOPING
                else:
                    logger.warning(f"未知的状态: {status_str}，使用默认值COMPLETED")
                    status = RequirementStatus.COMPLETED

                # 解析日期
                start_date_str = row.get('开始日期')
                end_date_str = row.get('结束日期')

                try:
                    # 尝试解析日期格式 "2025年2月24日"
                    start_date = datetime.strptime(start_date_str, '%Y年%m月%d日')
                    end_date = datetime.strptime(end_date_str, '%Y年%m月%d日')
                except (ValueError, TypeError):
                    logger.warning(f"日期格式错误: {start_date_str} 或 {end_date_str}，使用当前日期")
                    start_date = datetime.now()
                    end_date = datetime.now()

                # 解析开发人员
                developers_str = row.get('研发人员', '')
                developer_names = [name.strip() for name in developers_str.split(',') if name.strip()]
                developer_ids = []

                for dev_name in developer_names:
                    if dev_name in user_map:
                        developer_ids.append(user_map[dev_name])
                    else:
                        logger.warning(f"找不到开发人员: {dev_name}")

                # 如果没有找到任何开发人员，使用默认用户
                if not developer_ids and user_map:
                    default_user_id = next(iter(user_map.values()))
                    developer_ids.append(default_user_id)

                # 创建需求记录
                title = row.get('需求', '')
                content = row.get('需求描述', '')
                if not content:
                    content = title  # 如果没有描述，使用标题作为内容

                # 确定提交者ID（使用第一个开发人员或默认用户）
                submitter_id = developer_ids[0] if developer_ids else 1

                # 不再生成Git分支名称，因为不需要写入git_branch字段
                # 原代码：
                # if req_type == RequirementType.BUG_FIX:
                #     prefix = "bugfix/"
                # elif req_type == RequirementType.HOT_FIX_BUG:
                #     prefix = "hotfix/"
                # else:
                #     prefix = "feature/"
                #
                # branch_name = f"{prefix}{requirement_code.lower()}-{re.sub(r'[^a-zA-Z0-9]', '_', title.lower())}"
                # if len(branch_name) > 100:
                #     branch_name = branch_name[:100]  # 限制长度

                # 创建需求记录
                # 如果状态是已完成，则设置update_time与submit_time相同
                update_time = start_date if status == RequirementStatus.COMPLETED else None

                # 创建需求记录（不包含git_branch字段）
                new_requirement = Requirement(
                    project_id=default_project.id,
                    requirement_code=requirement_code,
                    title=title,
                    content=content,
                    type=req_type,
                    priority=priority,
                    status=status,
                    start_date=start_date,
                    end_date=end_date,
                    submitter_id=submitter_id,
                    submit_time=start_date,
                    update_time=update_time
                )
                session.add(new_requirement)
                session.flush()  # 获取自增ID

                # 创建需求指派记录
                for dev_id in developer_ids:
                    assignment = RequirementAssignment(
                        requirement_id=new_requirement.id,
                        user_id=dev_id,
                        assign_time=start_date
                    )
                    session.add(assignment)

                # 不创建需求分支记录，因为分支数据已经丢失
                # 原代码：
                # main_branch = "master"  # 默认主分支
                # branch_record = RequirementBranch(
                #     requirement_id=new_requirement.id,
                #     branch_name=main_branch,
                #     is_main=True
                # )
                # session.add(branch_record)

                # 创建需求历史记录
                history = RequirementHistory(
                    requirement_id=new_requirement.id,
                    current_status_code=status.name,
                    action_code=RequirementAction.SUBMIT.name,
                    user_id=submitter_id,
                    action_time=start_date
                )
                session.add(history)

                logger.info(f"导入需求: {requirement_code} - {title}")

            # 提交所有更改
            session.commit()
            logger.info("需求导入完成")
    except Exception as e:
        logger.error(f"从CSV导入需求时出错: {str(e)}")
        session.rollback()
        raise

def clear_git_branch_field(session):
    """
    清除所有需求记录中的git_branch字段值
    """
    try:
        # 更新所有需求记录，将git_branch设置为NULL
        count = session.query(Requirement).update({Requirement.git_branch: None})
        session.commit()
        logger.info(f"已清除 {count} 条需求记录中的git_branch字段值")
    except Exception as e:
        logger.error(f"清除git_branch字段值时出错: {str(e)}")
        session.rollback()
        raise

def init_database():
    """
    初始化数据库数据：
    1. 从CSV文件导入用户数据
    2. 从CSV文件导入测试用例数据
    3. 从CSV文件导入需求数据
    4. 清除所有需求记录中的git_branch字段值
    """
    # 创建数据库引擎
    engine = create_engine(settings.DATABASE_URL, echo=True)

    # 创建会话
    Session = sessionmaker(bind=engine)
    session = Session()

    try:
        # 获取项目根目录
        root_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

        # 导入用户数据
        users_csv_path = os.path.join(root_dir, 'data', 'new', 'users.csv')
        logger.info(f"开始从CSV文件导入用户数据: {users_csv_path}")
        import_users_from_csv(session, users_csv_path)

        # 导入测试用例数据
        test_cases_csv_path = os.path.join(root_dir, 'data', 'new', 'test-unit.csv')
        logger.info(f"开始从CSV文件导入测试用例数据: {test_cases_csv_path}")
        import_test_cases_from_csv(session, test_cases_csv_path)

        # 导入需求数据
        requirements_csv_path = os.path.join(root_dir, 'data', 'new', 'requirements.csv')
        logger.info(f"开始从CSV文件导入需求数据: {requirements_csv_path}")
        import_requirements_from_csv(session, requirements_csv_path)

        # 清除所有需求记录中的git_branch字段值
        logger.info("开始清除所有需求记录中的git_branch字段值")
        clear_git_branch_field(session)

        logger.info("数据库初始化完成")
    except Exception as e:
        logger.error(f"初始化数据库时出错: {str(e)}")
        session.rollback()
        raise
    finally:
        session.close()

if __name__ == "__main__":
    init_database()
