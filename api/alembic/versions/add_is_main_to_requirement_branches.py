"""add is_main to requirement_branches

Revision ID: add_is_main_to_requirement_branches
Revises: 
Create Date: 2023-12-01 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'add_is_main_to_requirement_branches'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # 添加is_main字段到requirement_branches表
    op.add_column('requirement_branches', sa.Column('is_main', sa.<PERSON>(), nullable=False, server_default='0'))


def downgrade():
    # 删除is_main字段
    op.drop_column('requirement_branches', 'is_main')
