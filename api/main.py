from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from app.core.database import engine
from app.models import user, project, requirement, test_module, test_case
from app import api as api_router

# 创建数据库表
user.Base.metadata.create_all(bind=engine)
project.Base.metadata.create_all(bind=engine)
requirement.Base.metadata.create_all(bind=engine)
test_module.Base.metadata.create_all(bind=engine)
test_case.Base.metadata.create_all(bind=engine)

app = FastAPI(
    title="Scan Git API",
    description="API for scanning git repositories",
    version="1.0.0"
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 在生产环境中应该设置具体的域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 包含 API 路由 (这个 router 内部已经配置了所有子路由和前缀)
app.include_router(api_router.router)

@app.get("/")
async def root():
    return {"message": "Welcome to Scan Git API"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run("main:app", host="0.0.0.0", port=8000, reload=True)