#!/bin/bash

# 查找并终止后端 FastAPI 服务
echo "终止后端 FastAPI 服务..."
BACKEND_PID=$(ps aux | grep "uvicorn main:app" | grep -v grep | awk '{print $2}')
if [ -n "$BACKEND_PID" ]; then
    kill -9 $BACKEND_PID
    echo "后端服务 (PID: $BACKEND_PID) 已终止"
else
    echo "未找到运行中的后端服务"
fi

# 查找并终止前端 Vite 服务
echo "终止前端 Vite 服务..."
FRONTEND_PID=$(ps aux | grep "vite" | grep -v grep | awk '{print $2}')
if [ -n "$FRONTEND_PID" ]; then
    kill -9 $FRONTEND_PID
    echo "前端服务 (PID: $FRONTEND_PID) 已终止"
else
    echo "未找到运行中的前端服务"
fi
# 查找并终止占用 8000 端口的进程
echo "终止占用 8000 端口的进程..."
PORT_PID=$(lsof -i:8000 | grep LISTEN | awk '{print $2}')
if [ -n "$PORT_PID" ]; then
    kill -9 $PORT_PID
    echo "占用 8000 端口的进程 (PID: $PORT_PID) 已终止"
else
    echo "未找到占用 8000 端口的进程"
fi

echo "项目服务已全部终止！"