
## 角色
 - 管理员
 - 开发人员
 - 测试人员
 - 项目管理部

## 需求（任务）状态
 - 草稿
 - 待认领
 - 开发中
 - 测试中
 - 验证中
 - 已完成

## 草稿
管理员尚未正式发布的需求，这时候除了管理员，谁都无法看到该需求。保存需求草稿的时候，仅仅保存数据库相关字段即可，不需要去创建git分支，不需要去发起企业微信通知，不需要去创建需求编号等等。管理员可以对草稿进行编辑、发布或删除操作。删除草稿时，只需要从数据库中删除相关记录即可，无需其他额外操作。

## 待认领
管理员正式发布的需求，这个时候需要去创建git分支，发起企业微信通知，创建需求编号等等。状态为待认领的需求，跟该需求相关的开发人员、测试人员以及管理员才能看到。
开发人员点击查看的时候，页面是不显示需求详情的，只显示基本需求信息，如需求标题、时间范围、开发人员列表、测试人员列表等等。只有等开发人员认领完需求后，才能看得到具体的需求详情内容。

## 开发中
开发人员认领完需求后，需求就进入开发中状态，同时需要发起开发人员已经认领了任务的企业微信通知给管理员。这时候依然是管理员和测试人员只能查看操作。当开发人员正式完成开发工作后，可以进行提交测试操作，让任务进入到测试中状态。

## 测试中
开发人员将需求提交测试之后，需求的状态就变为测试中，同时需要发起开发人员已经将需求提交测试的企业微信通知给管理员和相关的测试人员。
这时候管理员只能进行查看，开发人员除了查看之外，还能进行撤回测试按钮（譬如发现开发工作还没做完，需要继续开发）。撤回测试的时候，也需要发起企业微信通知给管理员和相关的测试人员。
测试人员进行完测试工作后，可以根据测试结果决定是通过测试还是驳回测试。
如果是驳回测试的话，状态需要回到开发中，同时需要发起测试人员已经驳回了测试的企业微信通知给管理员和相关的开发人员。

## 验证中
测试人员通过测试之后，需求的状态就变为验证中，同时需要发起测试人员已经通过了测试的企业微信通知给管理员。
这时候开发人员和测试人员，只能进行查看操作。管理员除了可以进行查看，同时还能进行通过验证和驳回验证操作。
如果是通过验证的话，后台要开始执行对应的操作逻辑：
1、根据与需求相关的分支，创建出对应的PR请求；
2、同意这批创建的PR请求；
3、删除这个需求创建出来的feature/bugfix/hotfix分支。
如果是驳回验证的话，状态需要回到开发中，同时要发起企业微信给开发人员和测试人员。

## 权限矩阵表格

| 状态/角色 | 管理员 | 开发人员 | 测试人员 | 项目管理部 |
|-----------|--------|----------|----------|------------|
| **草稿** | 查看、编辑、发布、删除 | 无权限 | 无权限 | 无权限 |
| **待认领** | 查看 | 查看基本信息、认领任务 | 查看 | 查看 |
| **开发中** | 查看 | 查看、提交测试 | 查看 | 查看 |
| **测试中** | 查看 | 查看、撤回测试 | 查看、通过测试、驳回测试 | 查看 |
| **验证中** | 查看、通过验证、驳回验证 | 查看 | 查看 | 查看 |
| **已完成** | 查看 | 查看 | 查看 | 查看 |

### 权限说明

- **草稿状态**：只有管理员可以操作（查看、编辑、发布、删除），其他角色无法看到草稿状态的需求
- **待认领状态**：相关人员可以查看，但开发人员只能看到基本信息，需要认领后才能看到详细内容
- **开发中状态**：开发人员可以提交测试，其他角色只能查看
- **测试中状态**：开发人员可以撤回测试，测试人员可以进行测试操作，管理员只能查看
- **验证中状态**：只有管理员可以进行验证操作，其他角色只能查看
- **已完成状态**：所有角色都只能查看

### 特殊权限说明

1. **开发人员在待认领状态的限制**：只能查看基本需求信息（标题、时间范围、人员列表等），无法查看具体需求详情内容，直到认领任务后才能查看完整内容。

2. **企业微信通知触发**：
   - 需求发布（草稿→待认领）
   - 开发人员认领任务
   - 开发人员提交测试
   - 开发人员撤回测试
   - 测试人员驳回测试
   - 测试人员通过测试
   - 管理员驳回验证

3. **系统自动操作**（管理员通过验证时）：
   - 创建PR请求
   - 同意PR请求
   - 删除feature/bugfix/hotfix分支