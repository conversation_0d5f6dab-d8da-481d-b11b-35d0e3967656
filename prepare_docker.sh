#!/bin/bash

# 设置颜色输出
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# 显示带颜色的消息
echo_success() {
    echo -e "${GREEN}$1${NC}"
}

echo_warning() {
    echo -e "${YELLOW}$1${NC}"
}

echo_error() {
    echo -e "${RED}$1${NC}"
}

# 获取脚本所在目录的绝对路径
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
cd "$SCRIPT_DIR"

# 创建docker目录（如果不存在）
if [ ! -d "docker" ]; then
    mkdir -p docker
    echo_success "创建docker目录"
fi

# 清空docker目录
echo_warning "清空docker目录..."
rm -rf docker/*

# 编译前端UI代码
echo_warning "开始编译前端UI代码..."
cd ui
npm install
if [ $? -ne 0 ]; then
    echo_error "前端依赖安装失败！"
    exit 1
fi

npm run build
if [ $? -ne 0 ]; then
    echo_error "前端编译失败！"
    exit 1
fi
echo_success "前端UI编译完成"

# 回到脚本目录
cd "$SCRIPT_DIR"

# 压缩API目录
echo_warning "压缩API目录..."
tar -czf docker/api.tar.gz -C . api
if [ $? -eq 0 ]; then
    echo_success "API目录压缩完成: docker/api.tar.gz"
else
    echo_error "API目录压缩失败！"
    exit 1
fi

# 压缩编译后的UI目录
echo_warning "压缩编译后的UI目录..."
tar -czf docker/ui-dist.tar.gz -C ui dist
if [ $? -eq 0 ]; then
    echo_success "UI编译目录压缩完成: docker/ui-dist.tar.gz"
else
    echo_error "UI编译目录压缩失败！"
    exit 1
fi

# 复制nginx配置
cp nginx.conf docker/
echo_success "已复制nginx配置文件"

# 注意：不再在docker目录中创建site.db，因为将使用映射

# 创建新的Dockerfile
cat > docker/Dockerfile << 'EOF'
# 使用官方的基础镜像，这里选择ubuntu作为例子
FROM ubuntu:22.04

# 设置环境变量，避免交互式配置
ENV DEBIAN_FRONTEND=noninteractive
ENV PATH="/usr/local/bin:${PATH}"
ENV PYTHONPATH="/app:/app/api:${PYTHONPATH}"

# 替换为国内的软件源（以阿里云为例）
RUN sed -i '<EMAIL>@mirrors.aliyun.com@g' /etc/apt/sources.list \
    && sed -i '<EMAIL>@mirrors.aliyun.com@g' /etc/apt/sources.list

# 更新软件包列表并安装必要的依赖
RUN apt-get update \
    && apt-get install -y wget software-properties-common curl nginx python3-pip git \
    build-essential libssl-dev libffi-dev python3-dev

# 安装Python 3.11
RUN add-apt-repository ppa:deadsnakes/ppa \
    && apt-get update \
    && apt-get install -y python3.11 python3.11-distutils python3.11-dev \
    && wget https://bootstrap.pypa.io/get-pip.py  \
    && python3.11 get-pip.py -i https://mirrors.aliyun.com/pypi/simple/ --trusted-host mirrors.aliyun.com \
    && rm get-pip.py

# 创建软链接确保 python 命令可用
RUN ln -sf /usr/bin/python3.11 /usr/bin/python

# 配置pip使用国内镜像源
RUN mkdir -p /root/.pip && \
    echo "[global]" > /root/.pip/pip.conf && \
    echo "index-url = https://mirrors.aliyun.com/pypi/simple/" >> /root/.pip/pip.conf && \
    echo "trusted-host = mirrors.aliyun.com" >> /root/.pip/pip.conf

# 创建app目录
WORKDIR /app

# 复制压缩的API和UI文件
COPY ./api.tar.gz /tmp/api.tar.gz
COPY ./ui-dist.tar.gz /tmp/ui-dist.tar.gz
COPY ./nginx.conf /etc/nginx/sites-available/default

# 解压API目录
RUN tar -xzf /tmp/api.tar.gz -C /app && rm /tmp/api.tar.gz

# 清理默认的nginx站点内容
RUN rm -rf /var/www/html/* /var/www/html/.* 2>/dev/null || true

# 解压UI编译目录到nginx目录
RUN tar -xzf /tmp/ui-dist.tar.gz -C /tmp && \
    cp -r /tmp/dist/* /var/www/html/ && \
    rm -rf /tmp/ui-dist.tar.gz /tmp/dist

# 设置nginx目录权限
RUN chown -R www-data:www-data /var/www/html && \
    chmod -R 755 /var/www/html

# 安装 Python 后端依赖
WORKDIR /app/api
RUN pip install --upgrade pip && \
    pip install --no-cache-dir -i https://mirrors.aliyun.com/pypi/simple/ --trusted-host mirrors.aliyun.com wheel setuptools && \
    pip install --no-cache-dir -i https://mirrors.aliyun.com/pypi/simple/ --trusted-host mirrors.aliyun.com -r requirements.txt

# 备份默认的nginx配置
RUN cp /etc/nginx/sites-available/default /etc/nginx/sites-available/default.bak

# 暴露80端口和8000端口
EXPOSE 80 8000

# 创建数据目录
RUN mkdir -p /app/api/data

# 创建容器内部的启动脚本（这个脚本在容器启动时执行）
RUN echo '#!/bin/bash\n\
echo "=== 容器启动调试信息 ==="\n\
echo "检查API目录："\n\
ls -la /app/api/ || echo "API目录不存在"\n\
echo "检查前端文件："\n\
ls -la /var/www/html/ || echo "前端文件不存在"\n\
echo "检查nginx配置："\n\
nginx -t\n\
\n\
echo "启动 Nginx..."\n\
nginx\n\
\n\
echo "启动 Python 后端..."\n\
cd /app/api\n\
\n\
# 确保数据目录存在\n\
mkdir -p /app/api/data\n\
\n\
# 首次运行时初始化数据库\n\
DB_FILE="./site.db"\n\
if [ ! -f "$DB_FILE" ]; then\n\
    echo "数据库文件不存在，执行初始化..."\n\
    # 尝试初始化数据库\n\
    if [ -f "./init_database.py" ]; then\n\
        python init_database.py || echo "数据库初始化失败，尝试更新数据库"\n\
    else\n\
        echo "init_database.py 不存在，跳过初始化"\n\
    fi\n\
    \n\
    # 尝试更新数据库\n\
    if [ -f "./update_database.py" ]; then\n\
        python update_database.py || echo "数据库更新失败，但继续启动服务"\n\
    else\n\
        echo "update_database.py 不存在，跳过更新"\n\
    fi\n\
else\n\
    echo "数据库文件已存在，执行更新..."\n\
    if [ -f "./update_database.py" ]; then\n\
        python update_database.py || echo "数据库更新失败，但继续启动服务"\n\
    else\n\
        echo "update_database.py 不存在，跳过更新"\n\
    fi\n\
fi\n\
\n\
# 启动主应用\n\
python main.py\n\
' > /app/container_start.sh && chmod +x /app/container_start.sh

# 启动命令：使用容器内部的启动脚本
CMD ["/app/container_start.sh"]
EOF

# 创建启动脚本
cat > docker/start_docker.sh << 'EOF'
#!/bin/bash

# 构建docker镜像
echo "构建 Docker 镜像..."
docker build -t twl-project-manage .

# 创建数据目录
echo "创建数据目录..."
mkdir -p data

# 确保site.db文件存在
if [ ! -f "site.db" ]; then
    echo "创建空的site.db文件..."
    touch site.db
fi

# 运行docker容器
echo "启动 Docker 容器..."
# 检查是否有旧容器，如果有则停止并删除
if [ "$(docker ps -a | grep twl-project-manage)" ]; then
    echo "发现旧的容器，停止并删除..."
    docker stop twl-project-manage
    docker rm twl-project-manage
fi

# 运行docker容器
docker run --name twl-project-manage \
  -p 20080:80 \
  -p 28000:8000 \
  -v $(pwd)/data:/app/api/data \
  -v $(pwd)/site.db:/app/api/site.db \
  -d twl-project-manage

echo "容器已启动，等待5秒..."
sleep 5

# 检查容器是否运行
if [ "$(docker ps | grep twl-project-manage)" ]; then
    echo "容器运行成功！"
    echo "前端地址: http://localhost:20080"
    echo "后端API地址: http://localhost:28000"

    # 显示容器日志
    echo "容器日志:"
    docker logs twl-project-manage

    echo "使用以下命令查看实时日志:"
    echo "docker logs -f twl-project-manage"

    echo "使用以下命令进入容器内部调试:"
    echo "docker exec -it twl-project-manage /bin/bash"
else
    echo "容器启动失败！查看详细错误日志:"
    docker logs twl-project-manage
fi
EOF

# 确保启动脚本有执行权限
chmod +x docker/start_docker.sh
chmod +x test_docker_build.sh

# 不再需要docker-compose.yml文件，使用直接的docker命令

echo_success "已复制Dockerfile和启动脚本"

# 显示压缩文件信息
echo_warning "压缩文件信息："
ls -lh docker/*.tar.gz 2>/dev/null || echo "未找到压缩文件"

# 验证压缩文件内容
echo_warning "验证压缩文件内容："
if [ -f "docker/api.tar.gz" ]; then
    echo "API压缩文件内容："
    tar -tzf docker/api.tar.gz | head -10
else
    echo_error "API压缩文件不存在！"
fi

if [ -f "docker/ui-dist.tar.gz" ]; then
    echo "UI压缩文件内容："
    tar -tzf docker/ui-dist.tar.gz | head -10
else
    echo_error "UI压缩文件不存在！"
fi

echo_success "Docker部署准备完成！"

echo_warning "测试构建（可选）："
echo "bash test_docker_build.sh"
echo ""

echo_warning "部署到服务器："
echo "1. 将docker目录复制到服务器"
echo "2. 在服务器上执行：cd docker && bash start_docker.sh"
echo ""

echo_warning "注意事项："
echo "1. API目录已压缩为 api.tar.gz"
echo "2. UI编译目录已压缩为 ui-dist.tar.gz"
echo "3. 这些压缩文件会在Docker构建时自动解压到容器中"
echo "4. 数据库文件site.db和data目录通过映射持久化"
echo "5. 更新代码时需要重新运行此脚本并重新构建Docker镜像"
echo "6. docker目录包含了所有部署所需的文件"
echo "7. 如果遇到403错误，检查前端文件是否正确解压到/var/www/html"
echo "8. 如果API无法访问，检查Python应用是否在8000端口启动"
