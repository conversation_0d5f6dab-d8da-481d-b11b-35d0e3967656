{"name": "admin-system", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@quasar/extras": "^1.17.0", "axios": "^1.6.2", "element-plus": "^2.4.3", "marked": "^15.0.11", "mavon-editor": "^3.0.2", "pinia": "^3.0.2", "quasar": "^2.18.1", "vue": "^3.3.9", "vue-router": "^4.2.5"}, "devDependencies": {"@vitejs/plugin-vue": "^4.5.1", "vite": "^5.0.4"}}