import { createRouter, createWebHistory } from 'vue-router'

const routes = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('../views/Login.vue'),
    meta: {
      title: '登录',
      requiresAuth: false
    }
  },
  {
    path: '/',
    component: () => import('../layouts/MainLayout.vue'),
    meta: {
      requiresAuth: true
    },
    children: [
      {
        path: '',
        redirect: '/home'
      },
      {
        path: '/home',
        name: 'Home',
        component: () => import('../views/Home.vue'),
        meta: {
          title: '首页',
          requiresAuth: true
        }
      },
      {
        path: '/user-management',
        name: 'User-management',
        component: () => import('../views/UserManagement.vue'),
        meta: {
          title: '用户管理',
          requiresAuth: true,
          adminOnly: true
        }
      },
      {
        path: '/project-management',
        name: 'Project-management',
        component: () => import('../views/ProjectManagement.vue'),
        meta: {
          title: '项目管理',
          requiresAuth: true,
          adminOnly: true
        }
      },
      {
        path: '/todo',
        name: 'Todo',
        component: () => import('../views/Todo.vue'),
        meta: {
          title: '待办事项',
          requiresAuth: true
        }
      },
      {
        path: '/requirements-list',
        name: 'Requirements-list',
        component: () => import('../views/RequirementsList.vue'),
        meta: {
          title: '需求列表',
          requiresAuth: true
        }
      },
      {
        path: '/project/:id',
        name: 'Project-detail',
        component: () => import('../views/ProjectDetail.vue'),
        meta: {
          title: '项目详情',
          requiresAuth: true
        }
      },
      {
        path: '/pull-request-management',
        name: 'Pull-request-management',
        component: () => import('../views/PullRequestManagement.vue'),
        meta: {
          title: '合并分支管理',
          requiresAuth: true,
          adminOnly: true
        }
      },
      {
        path: '/test-case-projects',
        name: 'Test-case-projects',
        component: () => import('../views/TestCaseProjects.vue'),
        meta: {
          title: '测试用例项目列表',
          requiresAuth: true
        }
      },
      {
        path: '/test-case-list/:projectId',
        name: 'Test-case-list',
        component: () => import('../views/TestCaseList.vue'),
        meta: {
          title: '测试用例列表',
          requiresAuth: true
        }
      }
    ]
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const token = localStorage.getItem('token')
  const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}')

  if (to.meta.requiresAuth && !token) {
    next('/login')
  } else if (to.path === '/login' && token) {
    next('/')
  } else if (to.meta.adminOnly && !userInfo.is_admin) {
    next('/home')
  } else {
    next()
  }
})

export default router