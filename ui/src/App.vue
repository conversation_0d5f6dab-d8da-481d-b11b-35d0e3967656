<script setup>
import { onMounted } from 'vue';
import { useUserStore } from './stores/user';

const userStore = useUserStore();

onMounted(() => {
  // 应用启动时从localStorage加载用户信息
  userStore.loadUserFromLocalStorage();
  console.log('App mounted, user info loaded from localStorage');
});
</script>

<template>
  <div id="app">
    <router-view />
  </div>
</template>

<style>
#app {
  height: 100%;
  width: 100%;
  font-family: "Microsoft YaHei", -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Arial, sans-serif;
}
</style>
