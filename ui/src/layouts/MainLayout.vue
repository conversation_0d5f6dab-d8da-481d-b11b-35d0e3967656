<template>
  <div class="layout">
    <!-- 侧边栏 -->
    <div class="sidebar">
      <div class="sidebar-header">
        <div class="logo">项目管理系统</div>
      </div>
      <div class="menu">
        <!-- 通用菜单部分 -->
        <div class="menu-title">通用</div>
        <router-link to="/" class="menu-item" :class="{ 'active': $route.path === '/' || $route.path === '/home' }">
          <div class="menu-icon" style="mask-image: url('data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 width=%2224%22 height=%2224%22 viewBox=%220 0 24 24%22 fill=%22none%22 stroke=%22currentColor%22 stroke-width=%222%22 stroke-linecap=%22round%22 stroke-linejoin=%22round%22><path d=%22m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z%22/><polyline points=%229 22 9 12 15 12 15 22%22/></svg>'); -webkit-mask-image: url('data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 width=%2224%22 height=%2224%22 viewBox=%220 0 24 24%22 fill=%22none%22 stroke=%22currentColor%22 stroke-width=%222%22 stroke-linecap=%22round%22 stroke-linejoin=%22round%22><path d=%22m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z%22/><polyline points=%229 22 9 12 15 12 15 22%22/></svg>');"></div>
          首页
        </router-link>

        <!-- 项目菜单部分 -->
        <div class="menu-title">项目</div>
        <router-link to="/project-management" class="menu-item" :class="{ 'active': $route.path.startsWith('/project-management') || $route.path.startsWith('/project/') }">
          <div class="menu-icon" style="mask-image: url('data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 width=%2224%22 height=%2224%22 viewBox=%220 0 24 24%22 fill=%22none%22 stroke=%22currentColor%22 stroke-width=%222%22 stroke-linecap=%22round%22 stroke-linejoin=%22round%22><path d=%22M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5l2 3h9a2 2 0 0 1 2 2z%22></path></svg>'); -webkit-mask-image: url('data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 width=%2224%22 height=%2224%22 viewBox=%220 0 24 24%22 fill=%22none%22 stroke=%22currentColor%22 stroke-width=%222%22 stroke-linecap=%22round%22 stroke-linejoin=%22round%22><path d=%22M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5l2 3h9a2 2 0 0 1 2 2z%22></path></svg>');"></div>
          项目管理
        </router-link>
        <router-link to="/requirements-list" class="menu-item" :class="{ 'active': $route.path.startsWith('/requirements') || $route.path.startsWith('/requirements-list') }">
          <div class="menu-icon" style="mask-image: url('data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 width=%2224%22 height=%2224%22 viewBox=%220 0 24 24%22 fill=%22none%22 stroke=%22currentColor%22 stroke-width=%222%22 stroke-linecap=%22round%22 stroke-linejoin=%22round%22><path d=%22M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z%22></path><polyline points=%2214 2 14 8 20 8%22></polyline><line x1=%2216%22 y1=%2213%22 x2=%228%22 y2=%2213%22></line><line x1=%2216%22 y1=%2217%22 x2=%228%22 y2=%2217%22></line><polyline points=%2210 9 9 9 8 9%22></polyline></svg>'); -webkit-mask-image: url('data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 width=%2224%22 height=%2224%22 viewBox=%220 0 24 24%22 fill=%22none%22 stroke=%22currentColor%22 stroke-width=%222%22 stroke-linecap=%22round%22 stroke-linejoin=%22round%22><path d=%22M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z%22></path><polyline points=%2214 2 14 8 20 8%22></polyline><line x1=%2216%22 y1=%2213%22 x2=%228%22 y2=%2213%22></line><line x1=%2216%22 y1=%2217%22 x2=%228%22 y2=%2217%22></line><polyline points=%2210 9 9 9 8 9%22></polyline></svg>');"></div>
          需求管理
        </router-link>
        <router-link to="/pull-request-management" class="menu-item" :class="{ 'active': $route.path.startsWith('/pull-request') }">
          <div class="menu-icon" style="mask-image: url('data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 width=%2224%22 height=%2224%22 viewBox=%220 0 24 24%22 fill=%22none%22 stroke=%22currentColor%22 stroke-width=%222%22 stroke-linecap=%22round%22 stroke-linejoin=%22round%22><polyline points=%2221 8 21 21 3 21 3 8%22></polyline><rect x=%221%22 y=%223%22 width=%2222%22 height=%225%22></rect><line x1=%2210%22 y1=%2212%22 x2=%2214%22 y2=%2212%22></line></svg>'); -webkit-mask-image: url('data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 width=%2224%22 height=%2224%22 viewBox=%220 0 24 24%22 fill=%22none%22 stroke=%22currentColor%22 stroke-width=%222%22 stroke-linecap=%22round%22 stroke-linejoin=%22round%22><polyline points=%2221 8 21 21 3 21 3 8%22></polyline><rect x=%221%22 y=%223%22 width=%2222%22 height=%225%22></rect><line x1=%2210%22 y1=%2212%22 x2=%2214%22 y2=%2212%22></line></svg>');"></div>
          合并请求
        </router-link>

        <!-- 测试菜单部分 -->
        <div class="menu-title">测试</div>
        <router-link to="/test-case-projects" class="menu-item" :class="{ 'active': $route.path.startsWith('/test-case') }">
          <div class="menu-icon" style="mask-image: url('data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 width=%2224%22 height=%2224%22 viewBox=%220 0 24 24%22 fill=%22none%22 stroke=%22currentColor%22 stroke-width=%222%22 stroke-linecap=%22round%22 stroke-linejoin=%22round%22><path d=%22M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z%22></path><polyline points=%2214 2 14 8 20 8%22></polyline><path d=%22M16 13.58A2 2 0 0 0 15.3 12a2 2 0 0 0 .7-1.58A2 2 0 0 0 14 8.5h-6v8h6a2 2 0 0 0 2-1.92A2 2 0 0 0 16 13.58Z%22></path></svg>'); -webkit-mask-image: url('data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 width=%2224%22 height=%2224%22 viewBox=%220 0 24 24%22 fill=%22none%22 stroke=%22currentColor%22 stroke-width=%222%22 stroke-linecap=%22round%22 stroke-linejoin=%22round%22><path d=%22M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z%22></path><polyline points=%2214 2 14 8 20 8%22></polyline><path d=%22M16 13.58A2 2 0 0 0 15.3 12a2 2 0 0 0 .7-1.58A2 2 0 0 0 14 8.5h-6v8h6a2 2 0 0 0 2-1.92A2 2 0 0 0 16 13.58Z%22></path></svg>');"></div>
          测试用例
        </router-link>

        <!-- 管理员菜单部分（仅对管理员可见） -->
        <template v-if="isAdmin">
          <div class="menu-title">系统</div>
          <router-link to="/user-management" class="menu-item" :class="{ 'active': $route.path.startsWith('/user-management') }">
            <div class="menu-icon" style="mask-image: url('data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 width=%2224%22 height=%2224%22 viewBox=%220 0 24 24%22 fill=%22none%22 stroke=%22currentColor%22 stroke-width=%222%22 stroke-linecap=%22round%22 stroke-linejoin=%22round%22><path d=%22M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2%22></path><circle cx=%2212%22 cy=%227%22 r=%224%22></circle></svg>'); -webkit-mask-image: url('data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 width=%2224%22 height=%2224%22 viewBox=%220 0 24 24%22 fill=%22none%22 stroke=%22currentColor%22 stroke-width=%222%22 stroke-linecap=%22round%22 stroke-linejoin=%22round%22><path d=%22M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2%22></path><circle cx=%2212%22 cy=%227%22 r=%224%22></circle></svg>');"></div>
            用户管理
          </router-link>
        </template>
      </div>
    </div>

    <!-- 主内容区域 -->
    <!-- 页面内容 -->
    <div class="main">
      <div class="header">
        <div class="page-title">{{ currentPageTitle }}</div>
        <!-- 用户信息下拉菜单 -->
        <el-dropdown @command="handleDropdownCommand" trigger="click" placement="bottom-end">
          <div class="user-info">
            <div class="avatar">
              {{ userInitial }}
            </div>
            <span class="username">{{ userName }}</span>
            <div v-if="!isAdmin && userRoles.length > 0" class="role-badge-header">
              {{ userRoles.join('/') }}
            </div>
            <div class="dropdown-arrow">
              <el-icon><arrow-down /></el-icon>
            </div>
          </div>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="changePassword">
                <el-icon><lock /></el-icon>
                修改密码
              </el-dropdown-item>
              <el-dropdown-item command="logout" divided>
                <el-icon><switch-button /></el-icon>
                退出登录
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
      <div class="content">
        <router-view @update-title="updatePageTitle" />
      </div>
    </div>

    <!-- 修改密码对话框 -->
    <el-dialog
      v-model="changePasswordDialogVisible"
      title="修改密码"
      width="520px"
      :close-on-click-modal="false"
      class="change-password-dialog"
    >
      <el-form
        ref="passwordFormRef"
        :model="passwordForm"
        :rules="passwordRules"
        label-width="100px"
      >
        <el-form-item label="当前密码" prop="currentPassword">
          <el-input
            v-model="passwordForm.currentPassword"
            type="password"
            show-password
            placeholder="请输入当前密码"
            size="large"
          />
        </el-form-item>
        <el-form-item label="新密码" prop="newPassword">
          <el-input
            v-model="passwordForm.newPassword"
            type="password"
            show-password
            placeholder="请输入新密码"
            size="large"
          />
        </el-form-item>
        <el-form-item label="确认新密码" prop="confirmPassword">
          <el-input
            v-model="passwordForm.confirmPassword"
            type="password"
            show-password
            placeholder="请再次输入新密码"
            size="large"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="changePasswordDialogVisible = false" size="large">取消</el-button>
          <el-button type="primary" @click="handleChangePasswordSubmit" :loading="changePasswordLoading" size="large">
            确认
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, reactive } from 'vue';
import { useUserStore } from '../stores/user';
import { useRouter } from 'vue-router';
import { SwitchButton, ArrowDown, Lock } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { http } from '../utils/http';

const leftDrawerOpen = ref(true); // 默认打开
const miniState = ref(false); // 用于未来的迷你侧边栏实现
const currentPageTitle = ref('首页');
const userStore = useUserStore();
const router = useRouter();

// 修改密码相关数据
const changePasswordDialogVisible = ref(false);
const changePasswordLoading = ref(false);
const passwordFormRef = ref(null);

// 密码表单数据
const passwordForm = reactive({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
});

// 密码表单验证规则
const passwordRules = {
  currentPassword: [
    { required: true, message: '请输入当前密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6个字符', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请再次输入新密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6个字符', trigger: 'blur' },
    {
      validator: (_, value, callback) => {
        if (value !== passwordForm.newPassword) {
          callback(new Error('两次输入的密码不一致'));
        } else {
          callback();
        }
      },
      trigger: 'blur'
    }
  ]
};

const isAdmin = computed(() => userStore.isAdmin);
const userRoles = computed(() => userStore.userRoles);
const userName = computed(() => userStore.userName || '用户');
const userInitial = computed(() => {
  if (userName.value) {
    return userName.value.charAt(0);
  }
  return '用';
});

const toggleLeftDrawer = () => {
  leftDrawerOpen.value = !leftDrawerOpen.value;
};

const updatePageTitle = (newTitle) => {
  currentPageTitle.value = newTitle;
};

const logout = () => {
  userStore.logout();
  router.push('/login');
  console.log('用户已登出');
};

const handleDropdownCommand = (command) => {
  if (command === 'logout') {
    logout();
  } else if (command === 'changePassword') {
    handleChangePassword();
  }
};

const handleChangePassword = () => {
  changePasswordDialogVisible.value = true;
  // 重置表单
  if (passwordFormRef.value) {
    passwordFormRef.value.resetFields();
  }
  // 重置表单数据
  passwordForm.currentPassword = '';
  passwordForm.newPassword = '';
  passwordForm.confirmPassword = '';
};

// 处理修改密码提交
const handleChangePasswordSubmit = () => {
  passwordFormRef.value.validate(async (valid) => {
    if (!valid) return;

    changePasswordLoading.value = true;
    try {
      await http.post('/change-password', {
        current_password: passwordForm.currentPassword,
        new_password: passwordForm.newPassword
      });

      ElMessage.success('密码修改成功');
      changePasswordDialogVisible.value = false;

      // 密码修改成功后，清除登录信息并跳转到登录页
      setTimeout(() => {
        localStorage.removeItem('token');
        localStorage.removeItem('userInfo');
        userStore.logout();
        router.push('/login');
      }, 1500);
    } catch (error) {
      console.error('修改密码失败:', error);
      ElMessage.error('修改密码失败，请检查当前密码是否正确');
    } finally {
      changePasswordLoading.value = false;
    }
  });
};
</script>

<style scoped>
/* 布局样式 */
.sidebar {
  width: 220px;
  background-color: #fff;
  border-right: 1px solid #e5e6eb;
  box-shadow: 2px 0 8px rgba(0,0,0,0.03);
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  z-index: 100;
}

.sidebar-header {
  height: 60px;
  border-bottom: 1px solid #e5e6eb;
  display: flex;
  align-items: center;
  padding: 0 20px;
}

.logo {
  font-size: 18px;
  font-weight: bold;
  color: #1877f2;
}

.menu {
  padding: 20px 0;
}

.menu-title {
  font-size: 13px;
  color: #999;
  padding: 0 20px;
  margin-bottom: 8px;
}

.menu-item {
  height: 46px;
  display: flex;
  align-items: center;
  padding: 0 20px;
  color: #666;
  font-size: 15px;
  margin-bottom: 2px;
  cursor: pointer;
  position: relative;
  transition: all 0.3s;
  text-decoration: none;
}

.menu-item:hover {
  color: #1877f2;
  background-color: rgba(24, 119, 242, 0.04);
}

.menu-item.active {
  color: #1877f2;
  background-color: rgba(24, 119, 242, 0.08);
  font-weight: 500;
}

.menu-item.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 12px;
  bottom: 12px;
  width: 3px;
  background-color: #1877f2;
  border-radius: 0 2px 2px 0;
}

.menu-icon {
  width: 20px;
  height: 20px;
  margin-right: 8px;
  background-color: currentColor;
  opacity: 0.6;
  mask-size: cover;
  -webkit-mask-size: cover;
}

.menu-item.active .menu-icon {
  opacity: 1; /* 活动状态下图标不透明 */
}

.el-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.layout {
  display: flex;
  min-height: 100vh;
}

.main {
  flex: 1;
  margin-left: 220px;
  background-color: #f5f7fa;
}

.header {
  height: 60px;
  background-color: #fff;
  border-bottom: 1px solid #e5e6eb;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  position: sticky;
  top: 0;
  z-index: 99;
  box-shadow: 0 2px 4px rgba(0,0,0,0.03);
}

.page-title {
  font-size: 18px;
  font-weight: 500;
  color: #222;
}

.user-info {
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s;
  padding: 4px;
  border-radius: 6px;
}

.user-info:hover {
  background-color: rgba(24, 119, 242, 0.04);
}

.avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: #1877f2;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
  margin-right: 8px;
}

.username {
  font-size: 14px;
  color: #666;
}

.role-badge-header {
  margin-left: 8px;
  font-size: 12px;
  background-color: rgba(24, 119, 242, 0.1);
  color: #1877f2;
  padding: 2px 6px;
  border-radius: 4px;
}

.dropdown-arrow {
  margin-left: 8px;
  color: #666;
  opacity: 0.7;
  transition: all 0.3s;
  padding: 0;
  height: 20px;
  width: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.dropdown-arrow:hover {
  opacity: 1;
  color: #1877f2;
}

.dropdown-arrow .el-icon {
  font-size: 16px;
}

.content {
  padding: 24px;
}

/* 自定义Element Plus下拉菜单样式 */
:deep(.el-dropdown-menu) {
  border: 1px solid #e5e6eb;
  border-radius: 6px;
  box-shadow: 0 4px 16px rgba(0,0,0,0.1);
  padding: 8px 0;
  min-width: 160px;
}

:deep(.el-dropdown-menu__item) {
  height: 40px;
  line-height: 40px;
  padding: 0 16px;
  font-size: 14px;
  color: #222;
  display: flex;
  align-items: center;
  transition: all 0.3s;
}

:deep(.el-dropdown-menu__item:hover) {
  background-color: rgba(24, 119, 242, 0.04);
  color: #1877f2;
}

:deep(.el-dropdown-menu__item.is-divided) {
  border-top: 1px solid #e5e6eb;
  margin-top: 4px;
  padding-top: 8px;
}

:deep(.el-dropdown-menu__item .el-icon) {
  margin-right: 8px;
  font-size: 16px;
}

:deep(.el-dropdown-menu__item--divided::before) {
  background-color: #e5e6eb;
}

/* 修改密码弹窗样式 */
:deep(.change-password-dialog) {
  border-radius: 10px;
}

:deep(.change-password-dialog .el-dialog__header) {
  padding: 16px 24px;
  border-bottom: 1px solid #e5e6eb;
}

:deep(.change-password-dialog .el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #222;
}

:deep(.change-password-dialog .el-dialog__body) {
  padding: 24px;
}

:deep(.change-password-dialog .el-form-item__label) {
  font-size: 14px;
  color: #666;
  font-weight: normal;
}

:deep(.change-password-dialog .el-input__wrapper) {
  height: 40px;
  border-radius: 6px;
  border: 1px solid #e5e6eb;
  transition: all 0.3s;
}

:deep(.change-password-dialog .el-input__wrapper:hover) {
  border-color: #1877f2;
}

:deep(.change-password-dialog .el-input__wrapper.is-focus) {
  border-color: #1877f2;
  box-shadow: 0 0 0 2px rgba(24, 119, 242, 0.1);
}

:deep(.change-password-dialog .el-input__inner) {
  font-size: 14px;
  color: #222;
  height: 38px;
  line-height: 38px;
}

:deep(.change-password-dialog .el-form-item) {
  margin-bottom: 24px;
}

:deep(.change-password-dialog .el-dialog__footer) {
  padding: 16px 24px;
  border-top: 1px solid #e5e6eb;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.change-password-dialog .el-button) {
  height: 40px;
  padding: 0 24px;
  border-radius: 6px;
  font-size: 15px;
  font-weight: 500;
  transition: all 0.3s;
}

:deep(.change-password-dialog .el-button--default) {
  border: 1px solid #e5e6eb;
  color: #666;
}

:deep(.change-password-dialog .el-button--default:hover) {
  border-color: #1877f2;
  color: #1877f2;
  background-color: rgba(24, 119, 242, 0.04);
}

:deep(.change-password-dialog .el-button--primary) {
  background-color: #1877f2;
  border-color: #1877f2;
}

:deep(.change-password-dialog .el-button--primary:hover) {
  background-color: #0d6efd;
  border-color: #0d6efd;
}
</style>
