<template>
  <div class="top-menu">
    <div class="system-name">{{ systemName }}</div>
    <el-menu :default-active="activeIndex" mode="horizontal" @select="handleSelect">
      <el-menu-item index="home">首页</el-menu-item>
      <!-- <el-menu-item index="todo">待办事项</el-menu-item> -->
      <el-menu-item index="requirements-list">需求列表</el-menu-item>
      <el-menu-item index="test-case-projects">测试用例</el-menu-item>
      <el-menu-item v-if="isAdmin" index="user-management">用户管理</el-menu-item>
      <el-menu-item v-if="isAdmin" index="project-management">项目管理</el-menu-item>
      <el-menu-item v-if="isAdmin" index="pull-request-management">合并分支管理</el-menu-item>
    </el-menu>
    <div class="change-password-btn" @click="openChangePasswordDialog">修改密码</div>
    <div class="logout-btn" @click="handleLogout">退出登录</div>
  </div>

  <!-- 修改密码对话框 -->
  <el-dialog
    v-model="changePasswordDialogVisible"
    title="修改密码"
    width="400px"
    :close-on-click-modal="false"
  >
    <el-form
      ref="passwordFormRef"
      :model="passwordForm"
      :rules="passwordRules"
      label-width="100px"
      status-icon
    >
      <el-form-item label="当前密码" prop="currentPassword">
        <el-input
          v-model="passwordForm.currentPassword"
          type="password"
          show-password
          placeholder="请输入当前密码"
        />
      </el-form-item>
      <el-form-item label="新密码" prop="newPassword">
        <el-input
          v-model="passwordForm.newPassword"
          type="password"
          show-password
          placeholder="请输入新密码"
        />
      </el-form-item>
      <el-form-item label="确认新密码" prop="confirmPassword">
        <el-input
          v-model="passwordForm.confirmPassword"
          type="password"
          show-password
          placeholder="请再次输入新密码"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="changePasswordDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleChangePassword" :loading="changePasswordLoading">
          确认
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { ref, onMounted, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import config from '../utils/config'
import { http } from '../utils/http'

export default {
  name: 'TopMenu',
  setup() {
    const activeIndex = ref('home')
    const isAdmin = ref(false)
    const userName = ref('')
    const systemName = ref(config.systemName)
    const router = useRouter()
    const passwordFormRef = ref(null)
    const changePasswordDialogVisible = ref(false)
    const changePasswordLoading = ref(false)

    // 密码表单数据
    const passwordForm = reactive({
      currentPassword: '',
      newPassword: '',
      confirmPassword: ''
    })

    // 密码表单验证规则
    const passwordRules = {
      currentPassword: [
        { required: true, message: '请输入当前密码', trigger: 'blur' },
        { min: 6, message: '密码长度不能少于6个字符', trigger: 'blur' }
      ],
      newPassword: [
        { required: true, message: '请输入新密码', trigger: 'blur' },
        { min: 6, message: '密码长度不能少于6个字符', trigger: 'blur' }
      ],
      confirmPassword: [
        { required: true, message: '请再次输入新密码', trigger: 'blur' },
        { min: 6, message: '密码长度不能少于6个字符', trigger: 'blur' },
        {
          validator: (_, value, callback) => {
            if (value !== passwordForm.newPassword) {
              callback(new Error('两次输入的密码不一致'))
            } else {
              callback()
            }
          },
          trigger: 'blur'
        }
      ]
    }

    const handleSelect = (key) => {
      router.push({ name: key.charAt(0).toUpperCase() + key.slice(1) })
    }

    const handleLogout = () => {
      localStorage.removeItem('token')
      localStorage.removeItem('userInfo')
      router.push('/login')
    }

    // 打开修改密码对话框
    const openChangePasswordDialog = () => {
      changePasswordDialogVisible.value = true
      // 重置表单
      if (passwordFormRef.value) {
        passwordFormRef.value.resetFields()
      }
    }

    // 处理修改密码
    const handleChangePassword = () => {
      passwordFormRef.value.validate(async (valid) => {
        if (!valid) return

        changePasswordLoading.value = true
        try {
          await http.post('/change-password', {
            current_password: passwordForm.currentPassword,
            new_password: passwordForm.newPassword
          })

          ElMessage.success('密码修改成功')
          changePasswordDialogVisible.value = false

          // 密码修改成功后，清除登录信息并跳转到登录页
          setTimeout(() => {
            localStorage.removeItem('token')
            localStorage.removeItem('userInfo')
            router.push('/login')
          }, 1500)
        } catch (error) {
          console.error('修改密码失败:', error)
        } finally {
          changePasswordLoading.value = false
        }
      })
    }

    onMounted(() => {
      const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}')
      isAdmin.value = userInfo.is_admin || false
      userName.value = userInfo.name || '用户'
      const currentRoute = router.currentRoute.value.name ? router.currentRoute.value.name.toLowerCase() : 'home'
      if (currentRoute === 'home') {
        activeIndex.value = 'home'
      } else if (currentRoute === 'usermanagement') {
        activeIndex.value = 'user-management'
      } else if (currentRoute === 'projectmanagement') {
        activeIndex.value = 'project-management'
      } else if (currentRoute === 'project-detail') {
        activeIndex.value = 'project-management'
      } else if (currentRoute === 'todo') {
        activeIndex.value = 'todo'
      } else if (currentRoute === 'requirements-list') {
        activeIndex.value = 'requirements-list'
      } else if (currentRoute === 'pull-request-management') {
        activeIndex.value = 'pull-request-management'
      } else if (currentRoute === 'test-case-projects') {
        activeIndex.value = 'test-case-projects'
      } else if (currentRoute === 'test-case-list') {
        activeIndex.value = 'test-case-projects'
      }
    })

    return {
      activeIndex,
      isAdmin,
      userName,
      systemName,
      handleSelect,
      handleLogout,
      changePasswordDialogVisible,
      passwordForm,
      passwordRules,
      passwordFormRef,
      openChangePasswordDialog,
      handleChangePassword,
      changePasswordLoading
    }
  }
}
</script>

<style scoped>
.top-menu {
  display: flex;
  align-items: center;
  background-color: #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 0 20px;
  height: 60px;
}

.system-name {
  font-size: 24px;
  font-weight: bold;
  color: #1877f2;
  margin-right: 40px;
}

.el-menu {
  flex: 1;
  border: none;
  background-color: transparent;
}

.el-menu-item {
  font-size: 16px;
  color: #1c1e21 !important;
  height: 60px;
  line-height: 60px;
}

.el-menu-item:hover {
  background-color: #f0f2f5 !important;
  color: #1877f2 !important;
}

.el-menu-item.is-active {
  color: #1877f2 !important;
  border-bottom: 3px solid #1877f2;
}

.change-password-btn {
  padding: 0 16px;
  height: 36px;
  line-height: 36px;
  background-color: #4CAF50;
  color: white;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  margin-left: 16px;
}

.change-password-btn:hover {
  background-color: #45a049;
}

.logout-btn {
  padding: 0 16px;
  height: 36px;
  line-height: 36px;
  background-color: #1877f2;
  color: white;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  margin-left: 16px;
}

.logout-btn:hover {
  background-color: #166fe5;
}
</style>