# RequirementFormDialog 统一需求/漏洞表单组件

## 概述

`RequirementFormDialog` 是一个高度统一的表单对话框组件，专为"添加需求"和"上报漏洞"两种业务场景设计。采用现代化的分区域布局，确保相同字段在不同模式下保持完全一致的外观和交互体验。

## 🎨 设计理念

### 统一性原则
- **视觉统一**: 相同字段在两种模式下具有完全一致的样式、大小和布局
- **交互统一**: 统一的悬停效果、焦点状态和过渡动画
- **逻辑统一**: 一致的验证规则和数据处理流程

### 分区域布局
表单按功能逻辑分为五个清晰的区域：
1. **基本信息区域** - 项目、标题、类型、优先级
2. **详细内容区域** - Markdown编辑器
3. **分配信息区域** - 开发人员选择
4. **分支信息区域** - 主目标分支和其他目标分支
5. **时间计划区域** - 计划时间或自动时间设置

## 🚀 功能特性

- **🎯 统一布局**: 解决了原有两个独立对话框布局不一致的问题
- **📋 分区域设计**: 清晰的功能分组和视觉层次
- **🔄 智能模式切换**: 根据模式自动显示/隐藏字段，提供清晰的自动设置提示
- **📱 响应式设计**: 支持移动端适配，在小屏幕设备上自动调整布局
- **🎨 现代化UI**: 卡片式设计、圆角边框、阴影效果
- **⚡ 交互优化**: 流畅的悬停效果、焦点状态、过渡动画
- **🖼️ 图片处理**: 支持图片上传并转换为HTML标签
- **🌿 分支管理**: 智能处理主目标分支和其他目标分支的关系

## Props

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| visible | Boolean | false | 控制对话框显示/隐藏 |
| mode | String | 'requirement' | 模式：'requirement'(添加需求) 或 'bug'(上报漏洞) |
| projectId | String/Number | null | 项目ID（需求模式下使用） |
| projectsList | Array | [] | 项目列表（漏洞模式下使用） |
| projectDevelopers | Array | [] | 项目开发人员列表 |
| projectBranches | Array | [] | 项目分支列表 |

## Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| update:visible | Boolean | 更新对话框显示状态 |
| submit | Object | 表单提交事件，返回表单数据 |
| project-change | Number | 项目选择变更事件（仅漏洞模式） |

## 使用示例

### 上报漏洞模式

```vue
<RequirementFormDialog
  v-model:visible="showBugDialog"
  mode="bug"
  :projects-list="projectsList"
  :project-developers="projectDevelopers"
  :project-branches="projectBranches"
  @submit="handleBugSubmit"
  @project-change="handleProjectChange"
/>
```

### 添加需求模式

```vue
<RequirementFormDialog
  v-model:visible="showRequirementDialog"
  mode="requirement"
  :project-id="currentProjectId"
  :project-developers="projectDevelopers"
  :project-branches="projectBranches"
  @submit="handleRequirementSubmit"
/>
```

## 🔍 布局对比

### ❌ 之前的问题
- **布局不一致**: 两个独立对话框，相同字段在不同场景下样式差异明显
- **结构混乱**: 表单字段排列无逻辑，缺乏清晰的功能分组
- **视觉层次不清**: 所有字段平铺，没有重点突出
- **维护困难**: 两套代码，修改时需要同步更新

### ✅ 现在的优势
- **完全统一**: 一个组件，相同字段在两种模式下完全一致
- **逻辑清晰**: 按功能分为五个区域，每个区域有明确的图标和标题
- **视觉现代**: 卡片式设计，清晰的层次结构和视觉引导
- **易于维护**: 单一组件，统一的样式和逻辑

## 📋 字段差异

### 上报漏洞模式 (mode="bug")
- **基本信息区域**: 显示项目选择下拉框，类型使用统一的下拉选择器
- **优先级**: 显示自动设置的P0标签，附带说明文字
- **时间计划**: 显示自动设置提示，说明将自动计算30天期限

### 添加需求模式 (mode="requirement")
- **基本信息区域**: 不显示项目选择（通过projectId传入），类型使用相同的下拉选择器
- **优先级**: 显示可选择的优先级下拉框
- **时间计划**: 显示日期范围选择器，支持开始和结束日期选择

## 数据处理

### 提交数据格式

组件会根据模式自动处理数据格式：

**漏洞模式**:
```javascript
{
  project_id: Number,
  title: String,
  content: String,
  type: String, // 'Bug Fixed' 或 'Hot Bug Fixed'
  priority: 'P0', // 自动设置
  developer_ids: Array,
  main_branch: String,
  other_branches: Array,
  start_date: String, // 自动设置为当前时间
  end_date: String    // 自动设置为30天后
}
```

**需求模式**:
```javascript
{
  project_id: Number, // 来自props.projectId
  title: String,
  content: String,
  type: String,
  priority: String,
  developer_ids: Array,
  main_branch: String,
  other_branches: Array,
  start_date: String, // 格式化后的日期
  end_date: String    // 格式化后的日期
}
```

## 注意事项

1. **图片处理**: 组件会自动将Markdown图片语法转换为HTML img标签
2. **分支逻辑**: 主目标分支和其他目标分支互斥，组件会自动处理冲突
3. **表单验证**: 不同模式有不同的必填字段要求
4. **数据重置**: 对话框关闭时会自动重置表单数据

## 迁移指南

如果你之前使用的是分离的组件，可以按以下步骤迁移：

1. 导入新组件：
```javascript
import RequirementFormDialog from '@/components/RequirementFormDialog.vue';
```

2. 替换模板中的组件：
```vue
<!-- 旧的上报漏洞对话框 -->
<el-dialog title="上报漏洞" v-model="showBugDialog">
  <!-- 复杂的表单内容 -->
</el-dialog>

<!-- 新的统一组件 -->
<RequirementFormDialog
  v-model:visible="showBugDialog"
  mode="bug"
  :projects-list="projectsList"
  :project-developers="projectDevelopers"
  :project-branches="projectBranches"
  @submit="handleBugSubmit"
  @project-change="handleProjectChange"
/>
```

3. 更新事件处理方法：
```javascript
// 旧的方法可能需要复杂的数据处理
const handleBugSubmit = async (formData) => {
  // 组件已经处理好了数据格式，直接使用即可
  await createRequirement(formData);
  ElMessage.success('提交成功');
};
```
