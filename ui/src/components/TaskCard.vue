<template>
  <div class="task-card">
    <div class="task-header">
      <div>
        <div class="task-title">{{ task.title || task.requirementName }}</div>
        <div class="task-project">项目：{{ task.project_name || task.projectName }}</div>
        <div class="user-assign">
          <template v-if="!task.developers || task.developers.length === 0">
            负责人：未分配
          </template>
          <template v-else>
            开发者：{{ Array.isArray(task.developers) ? task.developers.map(d => d.name).join(', ') : task.developers }}
          </template>
        </div>
        <div class="tester-assign">
          <template v-if="!task.testers || task.testers.length === 0">
            测试者：未分配
          </template>
          <template v-else>
            测试者：{{ Array.isArray(task.testers) ? task.testers.map(t => t.name).join(', ') : task.testers }}
          </template>
        </div>
      </div>
      <div class="task-meta">
        <span class="badge" :class="getStatusBadgeClass(task.status)">{{ getStatusDisplayName(task.status) }}</span>
        <div class="task-deadline">截止日期：{{ formatDate(task.end_date || task.endDate) }}</div>
      </div>
    </div>
    <div class="task-footer">
      <div class="task-date">
        <el-icon><Clock /></el-icon>
        创建于 {{ formatDate(task.submit_time || task.submitTime) }}
        <template v-if="task.submitter_name || task.submitterName">
          by {{ task.submitter_name || task.submitterName }}
        </template>
      </div>
      <div class="task-actions">
        <!-- 根据权限矩阵显示操作按钮 -->
        <template v-if="canViewTask">
          <el-button class="btn-outline" @click="$emit('view', task)">查看</el-button>
        </template>

        <!-- 草稿状态：只有管理员可以操作 -->
        <template v-if="task.status === 'DRAFT' && userRole === 'admin'">
          <el-button class="btn-outline" @click="$emit('edit', task)">编辑</el-button>
          <el-button class="btn-primary" type="primary" @click="$emit('publish', task)">发布</el-button>
          <el-button class="btn-outline btn-danger" plain type="danger" @click="$emit('delete', task)">删除</el-button>
        </template>

        <!-- 待认领状态：开发人员可以认领 -->
        <template v-if="task.status === 'PENDING' && userRole === 'developer' && isTaskDeveloper">
          <el-button class="btn-primary" type="primary" @click="$emit('claim', task)">认领任务</el-button>
        </template>

        <!-- 开发中状态：开发人员可以提交测试 -->
        <template v-if="task.status === 'DEVELOPING' && userRole === 'developer' && isTaskDeveloper">
          <el-button class="btn-primary" type="primary" @click="$emit('submitToTest', task)">提交测试</el-button>
        </template>

        <!-- 测试中状态：开发人员可以撤回测试，测试人员可以测试操作 -->
        <template v-if="task.status === 'TESTING'">
          <template v-if="userRole === 'developer' && isTaskDeveloper">
            <el-button class="btn-outline" @click="$emit('withdrawFromTest', task)">撤回测试</el-button>
          </template>
          <template v-if="userRole === 'tester' && isTaskTester">
            <el-button class="btn-success" type="success" @click="$emit('approveTest', task)">通过测试</el-button>
            <el-button class="btn-outline btn-danger" plain type="danger" @click="$emit('rejectTest', task)">驳回测试</el-button>
          </template>
        </template>

        <!-- 验证中状态：只有管理员可以验证操作 -->
        <template v-if="task.status === 'VALIDATING' && userRole === 'admin'">
          <el-button class="btn-success" type="success" @click="$emit('approveValidation', task)">通过验证</el-button>
          <el-button class="btn-outline btn-danger" plain type="danger" @click="$emit('rejectValidation', task)">驳回验证</el-button>
        </template>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { Clock } from '@element-plus/icons-vue';

// Props
const props = defineProps({
  task: {
    type: Object,
    required: true
  },
  userRole: {
    type: String,
    required: true,
    validator: (value) => ['admin', 'developer', 'tester', 'project_manager'].includes(value)
  },
  currentUserId: {
    type: Number,
    required: true
  }
});

// Emits
const emit = defineEmits([
  'view', 'edit', 'publish', 'delete', 'claim', 'submitToTest',
  'withdrawFromTest', 'approveTest', 'rejectTest',
  'approveValidation', 'rejectValidation'
]);

// 状态映射
const statusMap = {
  'DRAFT': '草稿',
  'PENDING': '待认领',
  'DEVELOPING': '开发中',
  'TESTING': '测试中',
  'VALIDATING': '验证中',
  'COMPLETED': '已完成'
};

// 检查当前用户是否是任务的开发人员
const isTaskDeveloper = computed(() => {
  if (!props.task.developers) return false;
  if (Array.isArray(props.task.developers)) {
    return props.task.developers.some(dev =>
      (typeof dev === 'object' ? dev.id : dev) == props.currentUserId
    );
  }
  return props.task.developers == props.currentUserId;
});

// 检查当前用户是否是任务的测试人员
const isTaskTester = computed(() => {
  if (!props.task.testers) return false;
  if (Array.isArray(props.task.testers)) {
    return props.task.testers.some(tester =>
      (typeof tester === 'object' ? tester.id : tester) == props.currentUserId
    );
  }
  return props.task.testers == props.currentUserId;
});

// 检查是否可以查看任务
const canViewTask = computed(() => {
  const status = props.task.status;
  const role = props.userRole;

  // 草稿状态只有管理员可以看到
  if (status === 'DRAFT') {
    return role === 'admin';
  }

  // 其他状态：管理员、项目管理部可以查看所有，开发人员和测试人员只能查看相关的任务
  if (role === 'admin' || role === 'project_manager') {
    return true;
  }

  if (role === 'developer') {
    return isTaskDeveloper.value;
  }

  if (role === 'tester') {
    return isTaskTester.value;
  }

  return false;
});

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '未设置';

  const date = new Date(dateString);
  if (isNaN(date.getTime())) return dateString; // 如果无法解析，返回原始字符串

  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  }).replace(/\//g, '-');
};

// 获取状态显示名称
const getStatusDisplayName = (status) => {
  return statusMap[status] || status;
};

// 获取状态对应的徽章样式
const getStatusBadgeClass = (status) => {
  switch (status) {
    case 'DRAFT':
      return 'badge-gray';
    case 'PENDING':
      return 'badge-purple';
    case 'DEVELOPING':
      return 'badge-blue';
    case 'TESTING':
      return 'badge-orange';
    case 'VALIDATING':
      return 'badge-green';
    case 'COMPLETED':
      return 'badge-success';
    default:
      return 'badge-default';
  }
};
</script>

<style scoped>
.task-card {
  background-color: #fff;
  border-radius: var(--radius-card);
  box-shadow: var(--shadow-md);
  margin-bottom: var(--spacing-medium);
  padding: 20px;
  transition: all 0.3s;
}

.task-card:hover {
  box-shadow: var(--shadow-lg);
}

.task-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: var(--spacing-medium);
}

.task-title {
  font-size: 16px;
  font-weight: 500;
  color: var(--text-primary);
}

.task-project {
  font-size: 13px;
  color: var(--text-secondary);
  margin-top: 4px;
}

.task-meta {
  display: flex;
  align-items: flex-end;
  flex-direction: column;
}

.task-deadline {
  margin-top: 8px;
  font-size: 13px;
  color: var(--text-hint);
}

.badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: 24px;
  padding: 0 10px;
  border-radius: 12px;
  font-size: 13px;
  font-weight: 500;
}

.badge-blue {
  background-color: var(--primary-light);
  color: var(--primary-color);
}

.badge-orange {
  background-color: var(--warning-light);
  color: var(--warning);
}

.badge-purple {
  background-color: var(--purple-light);
  color: var(--purple);
}

.badge-green {
  background-color: var(--success-light);
  color: var(--success);
}

.badge-success {
  background-color: #e6f7ff;
  color: #1890ff;
}

.badge-gray {
  background-color: rgba(144, 147, 153, 0.1);
  color: #909399;
}

.badge-default {
  background-color: #f5f5f5;
  color: #666;
}

.task-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: var(--spacing-medium);
  border-top: 1px solid var(--border-color);
}

.task-date {
  font-size: 13px;
  color: var(--text-hint);
  display: flex;
  align-items: center;
}

.task-date .el-icon {
  margin-right: 4px;
  opacity: 0.5;
  font-size: 16px;
}

.task-actions {
  display: flex;
  gap: var(--spacing-small);
}

.user-assign {
  font-size: 13px;
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  margin-top: 4px;
}

.tester-assign {
  font-size: 13px;
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  margin-top: 4px;
}

/* 按钮样式支持 */
:deep(.btn-primary) {
  height: 40px;
  padding: 0 24px;
  border-radius: 6px;
  font-size: 15px;
  font-weight: 500;
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

:deep(.btn-primary:hover) {
  background-color: var(--primary-hover);
  border-color: var(--primary-hover);
}

:deep(.btn-outline) {
  height: 40px;
  padding: 0 24px;
  border-radius: 6px;
  font-size: 15px;
  font-weight: 500;
  background-color: transparent;
  color: var(--text-secondary);
  border: 1px solid var(--border-color);
}

:deep(.btn-outline:hover) {
  border-color: var(--primary-color);
  color: var(--primary-color);
}

:deep(.btn-success) {
  height: 40px;
  padding: 0 24px;
  border-radius: 6px;
  font-size: 15px;
  font-weight: 500;
  background-color: var(--success);
  border-color: var(--success);
}

:deep(.btn-success:hover) {
  background-color: #49ad18;
  border-color: #49ad18;
}

:deep(.btn-danger) {
  color: var(--error);
  border-color: var(--error);
}

:deep(.btn-danger:hover) {
  color: #fff;
  background-color: var(--error);
  border-color: var(--error);
}
</style>
