<template>
  <el-form ref="formRef" :model="form" :rules="rules" label-width="120px" class="test-case-form">
    <el-form-item label="测试功能模块" prop="module_id">
      <el-select v-model="form.module_id" placeholder="请选择测试功能模块" class="form-select">
        <el-option
          v-for="module in modules"
          :key="module.id"
          :label="module.name"
          :value="module.id"
        />
      </el-select>
    </el-form-item>
    <el-form-item label="测试编号" prop="test_number">
      <el-input v-model="form.test_number" placeholder="请输入测试编号" class="form-input" />
    </el-form-item>
    <el-form-item label="测试名称" prop="test_name">
      <el-input v-model="form.test_name" placeholder="请输入测试名称" class="form-input" />
    </el-form-item>
    <el-form-item label="测试类型" prop="test_type">
      <el-select v-model="form.test_type" placeholder="请选择测试类型" class="form-select">
        <el-option label="功能测试" value="功能测试" />
        <el-option label="异常测试" value="异常测试" />
      </el-select>
    </el-form-item>
    <el-form-item label="测试端" prop="test_side">
      <el-select v-model="form.test_side" placeholder="请选择测试端" class="form-select">
        <el-option label="前端测试" value="前端测试" />
        <el-option label="后端测试" value="后端测试" />
      </el-select>
    </el-form-item>
    <el-form-item label="前提条件" prop="precondition">
      <el-input
        v-model="form.precondition"
        type="textarea"
        :rows="3"
        placeholder="请输入前提条件（选填）"
        class="form-textarea"
      />
    </el-form-item>
    <el-form-item label="测试步骤" prop="test_steps">
      <el-input
        v-model="form.test_steps"
        type="textarea"
        :rows="5"
        placeholder="请输入测试步骤"
        class="form-textarea"
      />
    </el-form-item>
    <el-form-item label="预期结果" prop="expected_result">
      <el-input
        v-model="form.expected_result"
        type="textarea"
        :rows="5"
        placeholder="请输入预期结果"
        class="form-textarea"
      />
    </el-form-item>
    <el-form-item label="关联代码" prop="related_code">
      <el-input
        v-model="form.related_code"
        type="textarea"
        :rows="3"
        placeholder="请输入关联代码"
        class="form-textarea"
      />
    </el-form-item>
    <el-form-item label="测试状态" prop="test_status">
      <el-select v-model="form.test_status" placeholder="请选择测试状态" class="form-select">
        <el-option label="PASS" value="PASS" />
        <el-option label="FAIL" value="FAIL" />
      </el-select>
    </el-form-item>
    <el-form-item class="form-actions">
      <el-button type="primary" @click="submitForm" class="btn-primary">提交</el-button>
      <el-button @click="resetForm" class="btn-outline">重置</el-button>
    </el-form-item>
  </el-form>
</template>

<script>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'

export default {
  name: 'TestCaseForm',
  props: {
    projectId: {
      type: [Number, String],
      required: true
    },
    modules: {
      type: Array,
      default: () => []
    },
    editMode: {
      type: Boolean,
      default: false
    },
    testCaseData: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['submit-success'],
  setup(props, { emit }) {
    // 表单数据
    const form = reactive({
      project_id: props.projectId,
      module_id: props.editMode ? props.testCaseData.module_id : '',
      test_number: props.editMode ? props.testCaseData.test_number : '',
      test_name: props.editMode ? props.testCaseData.test_name : '',
      test_type: props.editMode ? props.testCaseData.test_type : '功能测试',
      test_side: props.editMode ? props.testCaseData.test_side : '后端测试',
      precondition: props.editMode ? props.testCaseData.precondition : '',
      test_steps: props.editMode ? props.testCaseData.test_steps : '',
      expected_result: props.editMode ? props.testCaseData.expected_result : '',
      related_code: props.editMode ? props.testCaseData.related_code : '',
      test_status: props.editMode ? props.testCaseData.test_status : 'PASS'
    })

    // 表单验证规则
    const rules = {
      module_id: [{ required: true, message: '请选择测试功能模块', trigger: 'change' }],
      test_number: [{ required: true, message: '请输入测试编号', trigger: 'blur' }],
      test_name: [{ required: true, message: '请输入测试名称', trigger: 'blur' }],
      test_type: [{ required: true, message: '请选择测试类型', trigger: 'change' }],
      test_side: [{ required: true, message: '请选择测试端', trigger: 'change' }],
      test_steps: [{ required: true, message: '请输入测试步骤', trigger: 'blur' }],
      expected_result: [{ required: true, message: '请输入预期结果', trigger: 'blur' }],
      related_code: [{ required: true, message: '请输入关联代码', trigger: 'blur' }],
      test_status: [{ required: true, message: '请选择测试状态', trigger: 'change' }]
    }

    const formRef = ref(null)

    // 提交表单
    const submitForm = async () => {
      if (!formRef.value) return

      await formRef.value.validate((valid, fields) => {
        if (valid) {
          // 表单验证通过，触发提交事件
          emit('submit-success', { ...form })
        } else {
          console.log('表单验证失败:', fields)
          ElMessage.error('请完善表单信息')
        }
      })
    }

    // 重置表单
    const resetForm = () => {
      if (!formRef.value) return
      formRef.value.resetFields()
    }

    return {
      form,
      rules,
      formRef,
      submitForm,
      resetForm
    }
  }
}
</script>

<style scoped>
.test-case-form {
  max-width: 800px;
  margin: 0 auto;
}

/* 元素样式覆盖 */
:deep(.el-form-item__label) {
  font-size: 14px;
  color: var(--text-secondary);
  font-weight: normal;
}

:deep(.el-form-item) {
  margin-bottom: var(--spacing-large);
}

.form-input, .form-select {
  width: 100%;
}

:deep(.el-input__wrapper),
:deep(.el-textarea__inner),
:deep(.el-select .el-input__wrapper) {
  border-radius: var(--radius-base);
  border: 1px solid var(--border-color);
  box-shadow: none !important;
  padding: 0 16px;
  transition: all 0.3s;
}

:deep(.el-input__wrapper:hover),
:deep(.el-textarea__inner:hover),
:deep(.el-select .el-input__wrapper:hover) {
  border-color: var(--primary-color);
}

:deep(.el-input__wrapper.is-focus),
:deep(.el-textarea__inner:focus),
:deep(.el-select .el-input__wrapper.is-focus) {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px var(--primary-light) !important;
}

:deep(.el-input__inner) {
  color: var(--text-primary);
  font-size: 14px;
}

.form-textarea {
  width: 100%;
}

.form-actions {
  display: flex;
  justify-content: flex-start;
  margin-top: var(--spacing-large);
}

:deep(.btn-primary) {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  border-radius: var(--radius-base);
  height: 40px;
  font-size: 15px;
}

:deep(.btn-primary:hover) {
  background-color: var(--primary-hover);
  border-color: var(--primary-hover);
}

:deep(.btn-outline) {
  border-color: var(--border-color);
  color: var(--text-secondary);
  border-radius: var(--radius-base);
  height: 40px;
  font-size: 15px;
}

:deep(.btn-outline:hover) {
  border-color: var(--primary-color);
  color: var(--primary-color);
}
</style>
