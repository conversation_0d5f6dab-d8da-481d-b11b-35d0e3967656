<template>
  <el-dialog
    v-model="visible"
    :title="dialogTitle"
    width="60%"
    :close-on-click-modal="false"
    :close-on-press-escape="true"
    @close="handleClose"
  >
    <el-form :model="formData" :rules="formRules" ref="formRef" label-width="120px">
      <!-- 项目选择 - 仅在上报漏洞模式下显示 -->
      <el-form-item v-if="mode === 'bug'" label="漏洞项目" prop="project_id">
        <el-select v-model="formData.project_id" placeholder="请选择项目" @change="handleProjectChange">
          <el-option
            v-for="item in projectsList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>

      <!-- 标题 -->
      <el-form-item :label="titleLabel" prop="title">
        <el-input v-model="formData.title" :placeholder="titlePlaceholder"></el-input>
      </el-form-item>

      <!-- 内容 -->
      <el-form-item :label="contentLabel" prop="content">
        <mavon-editor
          v-model="formData.content"
          :toolbars="markdownOption"
          :placeholder="contentPlaceholder"
          style="min-height: 300px;max-height: 300px;width:100%"
          class="markdown-theme"
          :toolbarsFlag="false"
          @imgAdd="handleImgAdd"
        ></mavon-editor>
      </el-form-item>

      <!-- 类型 -->
      <el-form-item :label="typeLabel" prop="type">
        <el-radio-group v-if="mode === 'bug'" v-model="formData.type">
          <el-radio label="Bug Fixed">修复漏洞</el-radio>
          <el-radio label="Hot Bug Fixed">紧急修复漏洞</el-radio>
        </el-radio-group>
        <el-select v-else v-model="formData.type" placeholder="请选择需求类型">
          <el-option
            v-for="item in requirementTypes"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>

      <!-- 优先级 - 仅在添加需求模式下显示 -->
      <el-form-item v-if="mode === 'requirement'" label="优先级" prop="priority">
        <el-select v-model="formData.priority" placeholder="请选择优先级">
          <el-option
            v-for="item in priorityOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>

      <!-- 开发人员 -->
      <el-form-item label="开发人员" prop="developer_ids">
        <el-select
          v-model="formData.developer_ids"
          multiple
          placeholder="请选择开发人员"
          :disabled="mode === 'bug' && !formData.project_id"
          style="width: 100%"
        >
          <el-option
            v-for="dev in projectDevelopers"
            :key="dev.id"
            :label="mode === 'requirement' ? `${dev.name} (${dev.username})` : dev.name"
            :value="dev.id"
          ></el-option>
        </el-select>
      </el-form-item>

      <!-- 主目标分支 -->
      <el-form-item label="主目标分支" prop="main_branch">
        <el-select
          v-model="formData.main_branch"
          placeholder="请选择主目标分支"
          :disabled="mode === 'bug' && !formData.project_id"
          style="width: 100%"
          @change="handleMainBranchChange"
        >
          <el-option
            v-for="branch in projectBranches"
            :key="branch.name"
            :label="branch.name"
            :value="branch.name"
          ></el-option>
        </el-select>
      </el-form-item>

      <!-- 其他目标分支 -->
      <el-form-item label="其他目标分支" prop="other_branches">
        <el-select
          v-model="formData.other_branches"
          multiple
          placeholder="请选择其他目标分支（可选）"
          :disabled="mode === 'bug' && (!formData.project_id || !formData.main_branch)"
          style="width: 100%"
        >
          <el-option
            v-for="branch in filteredOtherBranches"
            :key="branch.name"
            :label="branch.name"
            :value="branch.name"
          ></el-option>
        </el-select>
      </el-form-item>

      <!-- 计划时间 - 仅在添加需求模式下显示 -->
      <el-form-item v-if="mode === 'requirement'" label="计划时间" required>
        <el-col :span="11">
          <el-form-item prop="start_date">
            <el-date-picker
              v-model="formData.start_date"
              type="date"
              placeholder="计划开始日期"
              style="width: 100%"
            ></el-date-picker>
          </el-form-item>
        </el-col>
        <el-col class="line" :span="2">-</el-col>
        <el-col :span="11">
          <el-form-item prop="end_date">
            <el-date-picker
              v-model="formData.end_date"
              type="date"
              placeholder="计划结束日期"
              style="width: 100%"
            ></el-date-picker>
          </el-form-item>
        </el-col>
      </el-form-item>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          {{ submitButtonText }}
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { ref, computed, watch, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import 'mavon-editor/dist/css/index.css';

export default {
  name: 'RequirementFormDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    mode: {
      type: String,
      default: 'requirement', // 'requirement' 或 'bug'
      validator: (value) => ['requirement', 'bug'].includes(value)
    },
    projectId: {
      type: [String, Number],
      default: null
    },
    projectsList: {
      type: Array,
      default: () => []
    },
    projectDevelopers: {
      type: Array,
      default: () => []
    },
    projectBranches: {
      type: Array,
      default: () => []
    }
  },
  emits: ['update:visible', 'submit', 'project-change'],
  setup(props, { emit }) {
    const formRef = ref(null);
    const submitting = ref(false);

    // 表单数据
    const formData = ref({
      project_id: props.projectId || '',
      title: '',
      content: '',
      type: props.mode === 'bug' ? 'Bug Fixed' : '',
      priority: 'P2',
      developer_ids: [],
      main_branch: '',
      other_branches: [],
      start_date: new Date(),
      end_date: ''
    });

    // 计算属性 - 根据模式显示不同的文本
    const dialogTitle = computed(() => {
      return props.mode === 'bug' ? '上报漏洞' : '添加需求';
    });

    const titleLabel = computed(() => {
      return props.mode === 'bug' ? '漏洞标题' : '需求标题';
    });

    const titlePlaceholder = computed(() => {
      return props.mode === 'bug' ? '请输入漏洞标题' : '请输入需求标题';
    });

    const contentLabel = computed(() => {
      return props.mode === 'bug' ? '漏洞内容' : '需求内容';
    });

    const contentPlaceholder = computed(() => {
      return props.mode === 'bug' ? '请输入漏洞详细内容，支持Markdown格式' : '请输入需求详细内容，支持Markdown格式';
    });

    const typeLabel = computed(() => {
      return props.mode === 'bug' ? '漏洞类型' : '需求类型';
    });

    const submitButtonText = computed(() => {
      return props.mode === 'bug' ? '提交' : '确定';
    });

    // 表单验证规则
    const formRules = computed(() => {
      const baseRules = {
        title: [{ required: true, message: `请输入${props.mode === 'bug' ? '漏洞' : '需求'}标题`, trigger: 'blur' }],
        content: [{ required: true, message: `请输入${props.mode === 'bug' ? '漏洞' : '需求'}内容`, trigger: 'blur' }],
        type: [{ required: true, message: `请选择${props.mode === 'bug' ? '漏洞' : '需求'}类型`, trigger: 'change' }],
        developer_ids: [{ required: true, message: '请选择开发人员', trigger: 'change' }],
        main_branch: [{ required: true, message: '请选择主目标分支', trigger: 'change' }]
      };

      if (props.mode === 'bug') {
        baseRules.project_id = [{ required: true, message: '请选择项目', trigger: 'change' }];
      } else {
        baseRules.priority = [{ required: true, message: '请选择优先级', trigger: 'change' }];
        baseRules.start_date = [{ required: true, message: '请选择计划开始日期', trigger: 'change' }];
        baseRules.end_date = [{ required: true, message: '请选择计划结束日期', trigger: 'change' }];
      }

      return baseRules;
    });

    // 需求类型选项
    const requirementTypes = [
      { value: 'Bug Fixed', label: '修复漏洞' },
      { value: 'Hot Bug Fixed', label: '紧急修复漏洞' },
      { value: 'New Feature', label: '新功能' }
    ];

    // 优先级选项
    const priorityOptions = [
      { value: 'P0', label: 'P0（最高优先级）' },
      { value: 'P1', label: 'P1（高优先级）' },
      { value: 'P2', label: 'P2（普通优先级）' }
    ];

    // 过滤后的其他目标分支（排除主目标分支）
    const filteredOtherBranches = computed(() => {
      if (!formData.value.main_branch) return props.projectBranches;
      return props.projectBranches.filter(branch => branch.name !== formData.value.main_branch);
    });

    // Markdown编辑器配置
    const markdownOption = {
      bold: true,
      italic: true,
      header: true,
      underline: true,
      strikethrough: true,
      mark: true,
      superscript: true,
      subscript: true,
      quote: true,
      ol: true,
      ul: true,
      link: true,
      imagelink: true,
      code: true,
      table: true,
      fullscreen: true,
      readmodel: true,
      htmlcode: true,
      help: true,
      undo: true,
      redo: true,
      trash: true,
      save: true,
      navigation: true,
      alignleft: true,
      aligncenter: true,
      alignright: true,
      subfield: true,
      preview: true,
      defaultOpen: 'edit',
      theme: 'dark',
      codeStyle: 'atom-one-dark'
    };

    // 监听主目标分支变更
    watch(() => formData.value.main_branch, (newVal) => {
      if (newVal && formData.value.other_branches.includes(newVal)) {
        formData.value.other_branches = formData.value.other_branches.filter(branch => branch !== newVal);
        ElMessage.info('已从其他目标分支中移除主目标分支');
      }
    });

    // 监听其他目标分支变化
    watch(() => formData.value.other_branches, (newVal) => {
      if (newVal.includes(formData.value.main_branch) && formData.value.main_branch) {
        formData.value.main_branch = '';
        ElMessage.warning('主目标分支已被添加到其他目标分支中，请重新选择主目标分支');
      }
    }, { deep: true });

    // 监听项目ID变化
    watch(() => props.projectId, (newVal) => {
      if (newVal && props.mode === 'requirement') {
        formData.value.project_id = newVal;
      }
    }, { immediate: true });

    // 处理项目选择变更
    const handleProjectChange = (projectId) => {
      formData.value.developer_ids = [];
      formData.value.main_branch = '';
      formData.value.other_branches = [];
      emit('project-change', projectId);
    };

    // 处理主目标分支变更
    const handleMainBranchChange = (value) => {
      if (formData.value.other_branches.includes(value)) {
        formData.value.other_branches = formData.value.other_branches.filter(branch => branch !== value);
      }
    };

    // 处理图片添加
    const handleImgAdd = (pos, file) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        const base64 = e.target.result;
        const imgTag = `<img src="${base64}" alt="image" style="max-width: 100%; height: auto;" />`;

        // 替换markdown语法为HTML标签
        const content = formData.value.content;
        const beforeContent = content.substring(0, pos);
        const afterContent = content.substring(pos);
        formData.value.content = beforeContent + imgTag + afterContent;
      };
      reader.readAsDataURL(file);
    };

    // 重置表单
    const resetForm = () => {
      formData.value = {
        project_id: props.projectId || '',
        title: '',
        content: '',
        type: props.mode === 'bug' ? 'Bug Fixed' : '',
        priority: 'P2',
        developer_ids: [],
        main_branch: '',
        other_branches: [],
        start_date: new Date(),
        end_date: ''
      };
    };

    // 处理关闭
    const handleClose = () => {
      resetForm();
      emit('update:visible', false);
    };

    // 处理提交
    const handleSubmit = async () => {
      if (!formRef.value) return;

      try {
        await formRef.value.validate();

        // 检查是否有未处理的Markdown图片语法
        const markdownImgRegex = /!\[.*?\]\(.*?\)/g;
        if (markdownImgRegex.test(formData.value.content)) {
          ElMessage.warning('有图片尚未完成处理，请稍等片刻再提交');
          return;
        }

        submitting.value = true;

        // 构建提交数据
        const submitData = { ...formData.value };

        // 如果是漏洞模式，设置默认的时间和优先级
        if (props.mode === 'bug') {
          const now = new Date();
          const endDate = new Date(now);
          endDate.setDate(endDate.getDate() + 30);

          submitData.priority = 'P0'; // 漏洞默认为最高优先级
          submitData.start_date = now.toISOString().split('T')[0] + ' 00:00:00';
          submitData.end_date = endDate.toISOString().split('T')[0] + ' 23:59:59';
        } else {
          // 需求模式，格式化日期
          const formatDate = (date) => {
            if (!date) return '';
            const d = new Date(date);
            return d.getFullYear() + '-' +
                   String(d.getMonth() + 1).padStart(2, '0') + '-' +
                   String(d.getDate()).padStart(2, '0') + ' 00:00:00';
          };

          submitData.start_date = formatDate(submitData.start_date);
          submitData.end_date = formatDate(submitData.end_date);
          submitData.project_id = props.projectId;
        }

        emit('submit', submitData);

      } catch (error) {
        console.error('表单验证失败:', error);
      } finally {
        submitting.value = false;
      }
    };

    return {
      formRef,
      formData,
      formRules,
      submitting,
      dialogTitle,
      titleLabel,
      titlePlaceholder,
      contentLabel,
      contentPlaceholder,
      typeLabel,
      submitButtonText,
      requirementTypes,
      priorityOptions,
      filteredOtherBranches,
      markdownOption,
      handleProjectChange,
      handleMainBranchChange,
      handleImgAdd,
      handleClose,
      handleSubmit,
      resetForm
    };
  }
};
</script>

<style scoped>
.line {
  text-align: center;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* Markdown编辑器样式 */
.markdown-theme {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
}

.markdown-theme .v-note-wrapper .v-note-panel .v-note-edit.v-note-edit-focus {
  background-color: #fff !important;
}
</style>
