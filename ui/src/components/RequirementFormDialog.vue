<template>
  <el-dialog
    :model-value="visible"
    @update:model-value="$emit('update:visible', $event)"
    :title="dialogTitle"
    width="70%"
    :close-on-click-modal="false"
    :close-on-press-escape="true"
    @close="handleClose"
    class="unified-form-dialog"
  >
    <div class="form-container">
      <el-form :model="formData" :rules="formRules" ref="formRef" label-width="140px" class="unified-form">

        <!-- 基本信息区域 -->
        <div class="form-section">
          <div class="section-title">
            <el-icon><Document /></el-icon>
            <span>基本信息</span>
          </div>

          <!-- 项目选择 - 仅在上报漏洞模式下显示 -->
          <el-form-item v-if="mode === 'bug'" label="所属项目" prop="project_id" class="form-item-unified">
            <el-select
              v-model="formData.project_id"
              placeholder="请选择项目"
              @change="handleProjectChange"
              class="full-width"
              size="large"
            >
              <el-option
                v-for="item in projectsList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>

          <!-- 标题 -->
          <el-form-item :label="titleLabel" prop="title" class="form-item-unified">
            <el-input
              v-model="formData.title"
              :placeholder="titlePlaceholder"
              size="large"
              class="full-width"
            ></el-input>
          </el-form-item>

          <!-- 类型和优先级 - 使用统一的选择器样式 -->
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item :label="typeLabel" prop="type" class="form-item-unified">
                <el-select
                  v-model="formData.type"
                  :placeholder="`请选择${props.mode === 'bug' ? '漏洞' : '需求'}类型`"
                  class="full-width"
                  size="large"
                >
                  <el-option
                    v-for="item in typeOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item v-if="mode === 'requirement'" label="优先级" prop="priority" class="form-item-unified">
                <el-select
                  v-model="formData.priority"
                  placeholder="请选择优先级"
                  class="full-width"
                  size="large"
                >
                  <el-option
                    v-for="item in priorityOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
              <!-- 漏洞模式下显示自动设置的优先级 -->
              <el-form-item v-else label="优先级" class="form-item-unified">
                <el-tag type="danger" size="large" class="priority-tag">P0（最高优先级）</el-tag>
                <span class="auto-set-hint">漏洞自动设置为最高优先级</span>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 详细内容区域 -->
        <div class="form-section">
          <div class="section-title">
            <el-icon><EditPen /></el-icon>
            <span>详细内容</span>
          </div>

          <el-form-item :label="contentLabel" prop="content" class="form-item-unified content-item">
            <mavon-editor
              v-model="formData.content"
              :toolbars="markdownOption"
              :placeholder="contentPlaceholder"
              style="min-height: 320px; max-height: 320px; width: 100%"
              class="markdown-theme unified-editor"
              :toolbarsFlag="false"
              @imgAdd="handleImgAdd"
            ></mavon-editor>
          </el-form-item>
        </div>

        <!-- 分配信息区域 -->
        <div class="form-section">
          <div class="section-title">
            <el-icon><User /></el-icon>
            <span>分配信息</span>
          </div>

          <el-form-item label="开发人员" prop="developer_ids" class="form-item-unified">
            <el-select
              v-model="formData.developer_ids"
              multiple
              placeholder="请选择开发人员"
              :disabled="mode === 'bug' && !formData.project_id"
              class="full-width"
              size="large"
              collapse-tags
              collapse-tags-tooltip
            >
              <el-option
                v-for="dev in projectDevelopers"
                :key="dev.id"
                :label="getDeveloperLabel(dev)"
                :value="dev.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </div>

        <!-- 分支信息区域 -->
        <div class="form-section">
          <div class="section-title">
            <el-icon><Branch /></el-icon>
            <span>分支信息</span>
          </div>

          <el-form-item label="主目标分支" prop="main_branch" class="form-item-unified">
            <el-select
              v-model="formData.main_branch"
              placeholder="请选择主目标分支"
              :disabled="mode === 'bug' && !formData.project_id"
              class="full-width"
              size="large"
              filterable
              @change="handleMainBranchChange"
            >
              <el-option
                v-for="branch in projectBranches"
                :key="branch.name"
                :label="branch.name"
                :value="branch.name"
              ></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="其他目标分支" prop="other_branches" class="form-item-unified">
            <el-select
              v-model="formData.other_branches"
              multiple
              placeholder="请选择其他目标分支（可选）"
              :disabled="mode === 'bug' && (!formData.project_id || !formData.main_branch)"
              class="full-width"
              size="large"
              filterable
              collapse-tags
              collapse-tags-tooltip
            >
              <el-option
                v-for="branch in filteredOtherBranches"
                :key="branch.name"
                :label="branch.name"
                :value="branch.name"
              ></el-option>
            </el-select>
          </el-form-item>
        </div>

        <!-- 时间计划区域 -->
        <div class="form-section">
          <div class="section-title">
            <el-icon><Calendar /></el-icon>
            <span>时间计划</span>
          </div>

          <div v-if="mode === 'requirement'" class="time-range-container">
            <el-form-item label="计划时间" required class="form-item-unified time-range-item">
              <div class="date-range-wrapper">
                <el-form-item prop="start_date" class="date-item">
                  <el-date-picker
                    v-model="formData.start_date"
                    type="date"
                    placeholder="计划开始日期"
                    size="large"
                    class="date-picker"
                  ></el-date-picker>
                </el-form-item>
                <div class="date-separator">至</div>
                <el-form-item prop="end_date" class="date-item">
                  <el-date-picker
                    v-model="formData.end_date"
                    type="date"
                    placeholder="计划结束日期"
                    size="large"
                    class="date-picker"
                  ></el-date-picker>
                </el-form-item>
              </div>
            </el-form-item>
          </div>

          <div v-else class="auto-time-info">
            <el-form-item label="计划时间" class="form-item-unified">
              <div class="auto-time-display">
                <el-tag type="info" size="large">自动设置为当前时间起30天</el-tag>
                <span class="auto-set-hint">漏洞修复时间将自动计算</span>
              </div>
            </el-form-item>
          </div>
        </div>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer-unified">
        <el-button size="large" @click="handleClose">取消</el-button>
        <el-button type="primary" size="large" @click="handleSubmit" :loading="submitting">
          {{ submitButtonText }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script>
import { ref, computed, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { Document, EditPen, User, Calendar, Share } from '@element-plus/icons-vue';
import 'mavon-editor/dist/css/index.css';

export default {
  name: 'RequirementFormDialog',
  components: {
    Document,
    EditPen,
    User,
    Calendar,
    Branch: Share
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    mode: {
      type: String,
      default: 'requirement', // 'requirement' 或 'bug'
      validator: (value) => ['requirement', 'bug'].includes(value)
    },
    projectId: {
      type: [String, Number],
      default: null
    },
    projectsList: {
      type: Array,
      default: () => []
    },
    projectDevelopers: {
      type: Array,
      default: () => []
    },
    projectBranches: {
      type: Array,
      default: () => []
    }
  },
  emits: ['update:visible', 'submit', 'project-change'],
  setup(props, { emit }) {
    const formRef = ref(null);
    const submitting = ref(false);

    // 表单数据
    const formData = ref({
      project_id: props.projectId || '',
      title: '',
      content: '',
      type: props.mode === 'bug' ? 'Bug Fixed' : '',
      priority: 'P2',
      developer_ids: [],
      main_branch: '',
      other_branches: [],
      start_date: new Date(),
      end_date: ''
    });

    // 计算属性 - 根据模式显示不同的文本
    const dialogTitle = computed(() => {
      return props.mode === 'bug' ? '上报漏洞' : '添加需求';
    });

    const titleLabel = computed(() => {
      return props.mode === 'bug' ? '漏洞标题' : '需求标题';
    });

    const titlePlaceholder = computed(() => {
      return props.mode === 'bug' ? '请输入漏洞标题' : '请输入需求标题';
    });

    const contentLabel = computed(() => {
      return props.mode === 'bug' ? '漏洞内容' : '需求内容';
    });

    const contentPlaceholder = computed(() => {
      return props.mode === 'bug' ? '请输入漏洞详细内容，支持Markdown格式' : '请输入需求详细内容，支持Markdown格式';
    });

    const typeLabel = computed(() => {
      return props.mode === 'bug' ? '漏洞类型' : '需求类型';
    });

    const submitButtonText = computed(() => {
      return props.mode === 'bug' ? '提交' : '确定';
    });

    // 表单验证规则
    const formRules = computed(() => {
      const baseRules = {
        title: [{ required: true, message: `请输入${props.mode === 'bug' ? '漏洞' : '需求'}标题`, trigger: 'blur' }],
        content: [{ required: true, message: `请输入${props.mode === 'bug' ? '漏洞' : '需求'}内容`, trigger: 'blur' }],
        type: [{ required: true, message: `请选择${props.mode === 'bug' ? '漏洞' : '需求'}类型`, trigger: 'change' }],
        developer_ids: [{ required: true, message: '请选择开发人员', trigger: 'change' }],
        main_branch: [{ required: true, message: '请选择主目标分支', trigger: 'change' }]
      };

      if (props.mode === 'bug') {
        baseRules.project_id = [{ required: true, message: '请选择项目', trigger: 'change' }];
      } else {
        baseRules.priority = [{ required: true, message: '请选择优先级', trigger: 'change' }];
        baseRules.start_date = [{ required: true, message: '请选择计划开始日期', trigger: 'change' }];
        baseRules.end_date = [{ required: true, message: '请选择计划结束日期', trigger: 'change' }];
      }

      return baseRules;
    });

    // 统一的类型选项 - 根据模式动态返回
    const typeOptions = computed(() => {
      if (props.mode === 'bug') {
        return [
          { value: 'Bug Fixed', label: '修复漏洞' },
          { value: 'Hot Bug Fixed', label: '紧急修复漏洞' }
        ];
      } else {
        return [
          { value: 'Bug Fixed', label: '修复漏洞' },
          { value: 'Hot Bug Fixed', label: '紧急修复漏洞' },
          { value: 'New Feature', label: '新功能' }
        ];
      }
    });

    // 需求类型选项（保留兼容性）
    const requirementTypes = [
      { value: 'Bug Fixed', label: '修复漏洞' },
      { value: 'Hot Bug Fixed', label: '紧急修复漏洞' },
      { value: 'New Feature', label: '新功能' }
    ];

    // 优先级选项
    const priorityOptions = [
      { value: 'P0', label: 'P0（最高优先级）' },
      { value: 'P1', label: 'P1（高优先级）' },
      { value: 'P2', label: 'P2（普通优先级）' }
    ];

    // 过滤后的其他目标分支（排除主目标分支）
    const filteredOtherBranches = computed(() => {
      if (!formData.value.main_branch) return props.projectBranches;
      return props.projectBranches.filter(branch => branch.name !== formData.value.main_branch);
    });

    // Markdown编辑器配置
    const markdownOption = {
      bold: true,
      italic: true,
      header: true,
      underline: true,
      strikethrough: true,
      mark: true,
      superscript: true,
      subscript: true,
      quote: true,
      ol: true,
      ul: true,
      link: true,
      imagelink: true,
      code: true,
      table: true,
      fullscreen: true,
      readmodel: true,
      htmlcode: true,
      help: true,
      undo: true,
      redo: true,
      trash: true,
      save: true,
      navigation: true,
      alignleft: true,
      aligncenter: true,
      alignright: true,
      subfield: true,
      preview: true,
      defaultOpen: 'edit',
      theme: 'dark',
      codeStyle: 'atom-one-dark'
    };

    // 监听主目标分支变更
    watch(() => formData.value.main_branch, (newVal) => {
      if (newVal && formData.value.other_branches.includes(newVal)) {
        formData.value.other_branches = formData.value.other_branches.filter(branch => branch !== newVal);
        ElMessage.info('已从其他目标分支中移除主目标分支');
      }
    });

    // 监听其他目标分支变化
    watch(() => formData.value.other_branches, (newVal) => {
      if (newVal.includes(formData.value.main_branch) && formData.value.main_branch) {
        formData.value.main_branch = '';
        ElMessage.warning('主目标分支已被添加到其他目标分支中，请重新选择主目标分支');
      }
    }, { deep: true });

    // 监听项目ID变化
    watch(() => props.projectId, (newVal) => {
      if (newVal && props.mode === 'requirement') {
        formData.value.project_id = newVal;
      }
    }, { immediate: true });

    // 处理项目选择变更
    const handleProjectChange = (projectId) => {
      formData.value.developer_ids = [];
      formData.value.main_branch = '';
      formData.value.other_branches = [];
      emit('project-change', projectId);
    };

    // 处理主目标分支变更
    const handleMainBranchChange = (value) => {
      if (formData.value.other_branches.includes(value)) {
        formData.value.other_branches = formData.value.other_branches.filter(branch => branch !== value);
      }
    };

    // 获取开发人员标签 - 统一显示格式
    const getDeveloperLabel = (dev) => {
      return `${dev.name} (${dev.username})`;
    };

    // 处理图片添加
    const handleImgAdd = (pos, file) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        const base64 = e.target.result;
        const imgTag = `<img src="${base64}" alt="image" style="max-width: 100%; height: auto;" />`;

        // 替换markdown语法为HTML标签
        const content = formData.value.content;
        const beforeContent = content.substring(0, pos);
        const afterContent = content.substring(pos);
        formData.value.content = beforeContent + imgTag + afterContent;
      };
      reader.readAsDataURL(file);
    };

    // 重置表单
    const resetForm = () => {
      formData.value = {
        project_id: props.projectId || '',
        title: '',
        content: '',
        type: props.mode === 'bug' ? 'Bug Fixed' : '',
        priority: 'P2',
        developer_ids: [],
        main_branch: '',
        other_branches: [],
        start_date: new Date(),
        end_date: ''
      };
    };

    // 处理关闭
    const handleClose = () => {
      resetForm();
      emit('update:visible', false);
    };

    // 处理提交
    const handleSubmit = async () => {
      if (!formRef.value) return;

      try {
        await formRef.value.validate();

        // 检查是否有未处理的Markdown图片语法
        const markdownImgRegex = /!\[.*?\]\(.*?\)/g;
        if (markdownImgRegex.test(formData.value.content)) {
          ElMessage.warning('有图片尚未完成处理，请稍等片刻再提交');
          return;
        }

        submitting.value = true;

        // 构建提交数据
        const submitData = { ...formData.value };

        // 如果是漏洞模式，设置默认的时间和优先级
        if (props.mode === 'bug') {
          const now = new Date();
          const endDate = new Date(now);
          endDate.setDate(endDate.getDate() + 30);

          submitData.priority = 'P0'; // 漏洞默认为最高优先级
          submitData.start_date = now.toISOString().split('T')[0] + ' 00:00:00';
          submitData.end_date = endDate.toISOString().split('T')[0] + ' 23:59:59';
        } else {
          // 需求模式，格式化日期
          const formatDate = (date) => {
            if (!date) return '';
            const d = new Date(date);
            return d.getFullYear() + '-' +
                   String(d.getMonth() + 1).padStart(2, '0') + '-' +
                   String(d.getDate()).padStart(2, '0') + ' 00:00:00';
          };

          submitData.start_date = formatDate(submitData.start_date);
          submitData.end_date = formatDate(submitData.end_date);
          submitData.project_id = props.projectId;
        }

        emit('submit', submitData);

      } catch (error) {
        console.error('表单验证失败:', error);
      } finally {
        submitting.value = false;
      }
    };

    return {
      formRef,
      formData,
      formRules,
      submitting,
      dialogTitle,
      titleLabel,
      titlePlaceholder,
      contentLabel,
      contentPlaceholder,
      typeLabel,
      submitButtonText,
      typeOptions,
      requirementTypes,
      priorityOptions,
      filteredOtherBranches,
      markdownOption,
      getDeveloperLabel,
      handleProjectChange,
      handleMainBranchChange,
      handleImgAdd,
      handleClose,
      handleSubmit,
      resetForm
    };
  }
};
</script>

<style scoped>
/* 统一表单对话框样式 */
.unified-form-dialog {
  --el-dialog-border-radius: 12px;
}

.form-container {
  max-height: 70vh;
  overflow-y: auto;
  padding: 0 4px;
}

.unified-form {
  padding: 0;
}

/* 表单区域样式 */
.form-section {
  margin-bottom: 32px;
  background: #fafbfc;
  border-radius: 8px;
  padding: 20px;
  border: 1px solid #e4e7ed;
}

.form-section:last-child {
  margin-bottom: 0;
}

.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  border-bottom: 2px solid #409eff;
  padding-bottom: 8px;
}

.section-title .el-icon {
  margin-right: 8px;
  color: #409eff;
  font-size: 18px;
}

/* 统一表单项样式 */
.form-item-unified {
  margin-bottom: 24px;
}

.form-item-unified:last-child {
  margin-bottom: 0;
}

.full-width {
  width: 100%;
}

/* 内容编辑器特殊样式 */
.content-item {
  margin-bottom: 0;
}

.unified-editor {
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  overflow: hidden;
}

/* 自动设置提示样式 */
.auto-set-hint {
  margin-left: 12px;
  font-size: 12px;
  color: #909399;
  font-style: italic;
}

.priority-tag {
  margin-right: 8px;
}

.auto-time-display {
  display: flex;
  align-items: center;
}

/* 时间范围选择器样式 */
.time-range-container {
  width: 100%;
}

.time-range-item {
  margin-bottom: 0;
}

.date-range-wrapper {
  display: flex;
  align-items: center;
  width: 100%;
  gap: 16px;
}

.date-item {
  flex: 1;
  margin-bottom: 0;
}

.date-picker {
  width: 100%;
}

.date-separator {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
  white-space: nowrap;
}

/* 底部按钮样式 */
.dialog-footer-unified {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  padding: 20px 24px;
  border-top: 1px solid #e4e7ed;
  background: #fafbfc;
  margin: 0 -24px -24px -24px;
  border-radius: 0 0 12px 12px;
}

/* Markdown编辑器样式优化 */
.markdown-theme {
  border: 1px solid #dcdfe6;
  border-radius: 6px;
}

.markdown-theme .v-note-wrapper .v-note-panel .v-note-edit.v-note-edit-focus {
  background-color: #fff !important;
}

/* 表单标签样式优化 */
:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
  line-height: 1.5;
}

/* 输入框样式优化 */
:deep(.el-input__wrapper) {
  border-radius: 6px;
  transition: all 0.3s ease;
}

:deep(.el-input__wrapper:hover) {
  border-color: #c0c4cc;
}

:deep(.el-input__wrapper.is-focus) {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
}

/* 选择器样式优化 */
:deep(.el-select .el-input__wrapper) {
  border-radius: 6px;
}

:deep(.el-select .el-input__wrapper:hover) {
  border-color: #c0c4cc;
}

:deep(.el-select .el-input__wrapper.is-focus) {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
}

/* 日期选择器样式优化 */
:deep(.el-date-editor.el-input) {
  width: 100%;
}

:deep(.el-date-editor .el-input__wrapper) {
  border-radius: 6px;
}

/* 标签样式优化 */
:deep(.el-tag) {
  border-radius: 4px;
  font-weight: 500;
}

/* 按钮样式优化 */
:deep(.el-button) {
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.3s ease;
}

:deep(.el-button--large) {
  padding: 12px 24px;
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .unified-form-dialog {
    width: 95% !important;
  }

  .form-container {
    max-height: 60vh;
  }

  .date-range-wrapper {
    flex-direction: column;
    gap: 12px;
  }

  .date-separator {
    display: none;
  }

  .dialog-footer-unified {
    flex-direction: column;
    gap: 12px;
  }

  .dialog-footer-unified .el-button {
    width: 100%;
  }
}

/* 滚动条样式 */
.form-container::-webkit-scrollbar {
  width: 6px;
}

.form-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.form-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.form-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
