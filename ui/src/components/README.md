# 卡片组件重构说明

## 概述

为了提高代码的可维护性和复用性，我们将Home.vue和RequirementsList.vue中的卡片组件抽取成了独立的组件。

## 组件列表

### 1. TaskCard.vue - 任务卡片组件

从Home.vue中抽取的任务卡片组件，用于显示任务信息，并根据权限矩阵自动显示相应的操作按钮。

#### Props
- `task` (Object, required): 任务对象
- `userRole` (String, required): 用户角色 ('admin', 'developer', 'tester', 'project_manager')
- `currentUserId` (Number, required): 当前用户ID

#### Events
- `view`: 查看任务详情
- `edit`: 编辑任务 (仅草稿状态的管理员)
- `publish`: 发布任务 (仅草稿状态的管理员)
- `delete`: 删除任务 (仅草稿状态的管理员)
- `claim`: 认领任务 (待认领状态的开发人员)
- `submitToTest`: 提交测试 (开发中状态的开发人员)
- `withdrawFromTest`: 撤回测试 (测试中状态的开发人员)
- `approveTest`: 通过测试 (测试中状态的测试人员)
- `rejectTest`: 驳回测试 (测试中状态的测试人员)
- `approveValidation`: 通过验证 (验证中状态的管理员)
- `rejectValidation`: 驳回验证 (验证中状态的管理员)

#### 使用示例
```vue
<task-card
  :task="task"
  :user-role="userRole"
  :current-user-id="currentUserId"
  @view="handleView"
  @claim="handleClaim"
  @submitToTest="handleSubmitToTest"
  <!-- 其他事件处理 -->
/>
```

#### 支持的按钮样式类
- `.btn-primary`: 主要按钮（蓝色）
- `.btn-outline`: 边框按钮（灰色边框）
- `.btn-success`: 成功按钮（绿色）
- `.btn-danger`: 危险按钮（红色）

### 2. RequirementCard.vue - 需求卡片组件

从RequirementsList.vue中抽取的需求卡片组件，用于显示需求信息，并根据权限矩阵自动显示相应的操作按钮。

#### Props
- `requirement` (Object, required): 需求对象
- `userRole` (String, required): 用户角色 ('admin', 'developer', 'tester', 'project_manager')
- `currentUserId` (Number, required): 当前用户ID

#### Events
- `view`: 查看需求详情
- `edit`: 编辑需求 (仅草稿状态的管理员)
- `publish`: 发布需求 (仅草稿状态的管理员)
- `delete`: 删除需求 (仅草稿状态的管理员)
- `claim`: 认领需求 (待认领状态的开发人员)
- `submitToTest`: 提交测试 (开发中状态的开发人员)
- `withdrawFromTest`: 撤回测试 (测试中状态的开发人员)
- `approveTest`: 通过测试 (测试中状态的测试人员)
- `rejectTest`: 驳回测试 (测试中状态的测试人员)
- `approveValidation`: 通过验证 (验证中状态的管理员)
- `rejectValidation`: 驳回验证 (验证中状态的管理员)

#### 使用示例
```vue
<requirement-card
  :requirement="requirement"
  :user-role="userRole"
  :current-user-id="currentUserId"
  @view="handleView"
  @claim="handleClaim"
  @submitToTest="handleSubmitToTest"
  <!-- 其他事件处理 -->
/>
```

#### 支持的按钮样式类
- `.action-btn`: 基础操作按钮样式
- `.action-view`: 查看按钮（蓝色）
- `.action-edit`: 编辑按钮（蓝色）
- `.action-publish`: 发布按钮（绿色）
- `.action-delete`: 删除按钮（红色）
- `.action-claim`: 认领按钮（绿色）
- `.action-submit`: 提交按钮（橙色）
- `.action-withdraw`: 撤回按钮（灰色）
- `.action-approve`: 通过按钮（绿色）
- `.action-reject`: 驳回按钮（红色）

## 权限矩阵实现

组件内部实现了完整的权限控制逻辑，根据以下规则自动显示操作按钮：

### 状态和角色权限矩阵

| 状态 | 管理员 | 开发人员 | 测试人员 | 项目管理部 |
|------|--------|----------|----------|------------|
| 草稿 (DRAFT) | 编辑、发布、删除 | - | - | 查看 |
| 待认领 (PENDING) | 查看 | 认领 | - | 查看 |
| 开发中 (DEVELOPING) | 查看 | 提交测试 | - | 查看 |
| 测试中 (TESTING) | 查看 | 撤回测试 | 通过/驳回测试 | 查看 |
| 验证中 (VALIDATING) | 通过/驳回验证 | - | - | 查看 |
| 已完成 (COMPLETED) | 查看 | 查看 | 查看 | 查看 |

### 权限检查逻辑

1. **可见性控制**: 草稿状态只有管理员可见，其他状态根据用户角色和任务分配情况决定可见性
2. **角色验证**: 开发人员只能操作分配给自己的任务，测试人员只能操作分配给自己的测试任务
3. **状态限制**: 每个状态只显示符合业务流程的操作按钮

## 重构收益

1. **代码复用**: 卡片组件可以在多个页面中复用
2. **维护性**: 卡片相关的逻辑和样式集中管理
3. **权限控制**: 内置完整的权限矩阵，确保操作安全性
4. **可扩展性**: 通过插槽机制，可以灵活定制操作按钮
5. **一致性**: 确保所有页面的卡片样式和行为保持一致

## 迁移说明

### Home.vue 变更
- 移除了task-card相关的模板代码和样式
- 移除了formatDate和getStatusBadgeClass方法
- 使用TaskCard组件替代原有的卡片实现

### RequirementsList.vue 变更
- 移除了requirement-card相关的模板代码和样式
- 移除了卡片样式相关的方法（getCardHeaderClass、getPriorityClass等）
- 使用RequirementCard组件替代原有的卡片实现

## 注意事项

1. 组件内部已包含所有必要的样式，无需在父组件中重复定义
2. 按钮样式通过:deep()选择器支持，确保样式能够正确应用
3. 日期格式化等工具方法已内置在组件中
4. 组件支持响应式设计，在不同屏幕尺寸下都能正常显示
