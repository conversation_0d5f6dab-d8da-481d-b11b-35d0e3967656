import { http } from '../utils/api';

// 管理员统计数据API
export const adminStatsApi = {
  // 获取系统用户总数
  getTotalUsers() {
    return http.get('/admin/stats/users/total');
  },

  // 获取近7天新增用户个数
  getNewUsers() {
    return http.get('/admin/stats/users/new');
  },

  // 获取项目总数
  getTotalProjects() {
    return http.get('/admin/stats/projects/total');
  },

  // 获取近7天新增项目数
  getNewProjects() {
    return http.get('/admin/stats/projects/new');
  },

  // 获取待处理的git PR记录的个数
  getPendingPullRequests() {
    return http.get('/admin/stats/pull-requests/pending');
  },

  // 获取今日新增的git PR个数
  getTodayPullRequests() {
    return http.get('/admin/stats/pull-requests/today');
  },

  // 获取未完成处理需求的个数
  getPendingRequirements() {
    return http.get('/admin/stats/requirements/pending');
  },

  // 获取逾期需求的个数
  getOverdueRequirements() {
    return http.get('/admin/stats/requirements/overdue');
  },

  // 获取当前用户创建的需求数量
  getMyCreatedRequirements() {
    return http.get('/admin/stats/requirements/my-created');
  },

  // 获取管理员首页所需的所有统计数据
  getDashboardStats() {
    return http.get('/admin/stats/dashboard');
  },

  // 获取特定状态的需求数量
  getRequirementsByStatus(status) {
    return http.get(`/admin/stats/requirements/by-status/${status}`);
  }
};
