import { http } from '@/utils/http'

// 用户管理API
export function getUsers() {
  return http.get('/admin/users')
}

export function getUser(id) {
  return http.get(`/admin/users/${id}`)
}

export function createUser(userData) {
  return http.post('/admin/users', userData)
}

export function updateUser(id, userData) {
  return http.put(`/admin/users/${id}`, userData)
}

export function deleteUser(id) {
  return http.delete(`/admin/users/${id}`)
} 