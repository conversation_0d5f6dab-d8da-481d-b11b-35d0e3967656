import { http } from '@/utils/http'

// 项目管理API
export function getProjects() {
  return http.get('/admin/projects')
}

export function getProject(id) {
  return http.get(`/admin/projects/${id}`)
}

export function createProject(projectData) {
  return http.post('/admin/projects', projectData)
}

export function updateProject(id, projectData) {
  return http.put(`/admin/projects/${id}`, projectData)
}

export function deleteProject(id) {
  return http.delete(`/admin/projects/${id}`)
}

export function getProjectMembers(id, role) {
  let url = `/admin/projects/${id}/members`;
  if (role) {
    url += `?role=${role}`;
  }
  return http.get(url)
}

export function getProjectDevelopers(id) {
  return http.get(`/admin/projects/${id}/developers`)
}

export function addProjectMember(id, userData) {
  return http.post(`/admin/projects/${id}/members`, userData)
}

export function removeProjectMember(id, userId, role) {
  return http.delete(`/admin/projects/${id}/members/${userId}?role=${role}`)
}

// GitLab项目API
export function getGitLabProjects() {
  return http.get('/admin/gitlab-projects')
}

export function getProjectDevBranches(id) {
  return http.get(`/admin/projects/${id}/dev_branches`)
}

export function createMergeRequest(id, mergeRequestData) {
  return http.post(`/admin/projects/${id}/merge_requests`, mergeRequestData)
}

// 需求管理API
export function getRequirements(params) {
  return http.get('/admin/requirements', { params })
}

export function getRequirement(id) {
  return http.get(`/admin/requirements/${id}`)
}

export function createRequirement(requirementData) {
  return http.post('/admin/requirements', requirementData)
}

export function updateRequirement(requirementData) {
  return http.put(`/admin/requirements/${requirementData.id}`, requirementData)
}

export function deleteRequirement(id) {
  return http.delete(`/admin/requirements/${id}`)
}

// 待办事项API
export function getTodos() {
  return http.get('/admin/todos')
}

// 需求列表API
export function getRequirementsList(params) {
  return http.get('/admin/requirements-list', { params })
}

// 用户需求列表API（非管理员专用）
export function getUserRequirementsList(params) {
  return http.get('/requirements-list', { params })
}

// 获取需求详情API
export function getRequirementDetail(id) {
  return http.get(`/admin/requirement-detail/${id}`)
}

// 开发人员认领需求
export function claimRequirement(id) {
  return http.post(`/admin/requirements/${id}/claim`)
}

// 开发人员提交测试
export function submitToTest(id) {
  return http.post(`/admin/requirements/${id}/submit-to-test`)
}

// 开发人员撤回测试
export function withdrawFromTest(id) {
  return http.post(`/admin/requirements/${id}/withdraw-from-test`)
}

// 开发人员撤回验证
export function withdrawFromValidation(id) {
  return http.post(`/admin/requirements/${id}/withdraw-from-validation`)
}

// 测试人员通过测试
export function approveTest(id) {
  return http.post(`/admin/requirements/${id}/approve-test`)
}

// 测试人员驳回测试
export function rejectTest(id, reason) {
  return http.post(`/admin/requirements/${id}/reject-test`, { reason })
}

// 管理员通过验证
export function approveValidation(id) {
  return http.post(`/admin/requirements/${id}/approve-validation`)
}

// 管理员驳回验证
export function rejectValidation(id, reason) {
  return http.post(`/admin/requirements/${id}/reject-validation`, { reason })
}

// 测试功能模块API
export function getTestModules(projectId) {
  return http.get(`/admin/projects/${projectId}/test-modules`)
}

export function createTestModule(projectId, testModuleData) {
  return http.post(`/admin/projects/${projectId}/test-modules`, testModuleData)
}

export function updateTestModule(projectId, testModuleId, testModuleData) {
  return http.put(`/admin/projects/${projectId}/test-modules/${testModuleId}`, testModuleData)
}

export function deleteTestModule(projectId, testModuleId) {
  return http.delete(`/admin/projects/${projectId}/test-modules/${testModuleId}`)
}