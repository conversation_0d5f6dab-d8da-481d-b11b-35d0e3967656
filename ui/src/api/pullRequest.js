import { http } from '../utils/api'

// 获取所有PR列表
export function getPullRequests() {
  return http.get('/admin/pull-requests')
}

// 获取按需求分组的PR列表
export function getGroupedPullRequests() {
  return http.get('/admin/pull-requests/grouped')
}

// 合并单个PR
export function mergePullRequest(prId) {
  return http.post(`/admin/pull-requests/${prId}/merge`)
}

// 批量合并需求的所有PR
export function mergeAllPullRequests(requirementId) {
  return http.post(`/admin/requirements/${requirementId}/merge-all`)
}

// 拒绝需求并回退状态
export function rejectRequirement(requirementId, reason) {
  return http.post(`/admin/requirements/${requirementId}/reject`, { reason })
}

// 关闭PR
export function closePullRequest(prId) {
  return http.post(`/admin/pull-requests/${prId}/close`)
}

export default {
  getPullRequests,
  getGroupedPullRequests,
  mergePullRequest,
  mergeAllPullRequests,
  rejectRequirement,
  closePullRequest
}
