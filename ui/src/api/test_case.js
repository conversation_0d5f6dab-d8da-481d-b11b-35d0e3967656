import { http } from '@/utils/http'

// 获取当前用户作为测试者的项目列表
export function getTesterProjects() {
  return http.get('/tester-projects')
}

// 获取项目的测试功能模块列表
export function getTestModules(projectId) {
  return http.get(`/test-modules/${projectId}`)
}

// 创建测试用例
export function createTestCase(testCaseData) {
  return http.post('/test-cases', testCaseData)
}

// 获取测试用例列表
export function getTestCases(params) {
  return http.get('/test-cases', { params })
}

// 获取测试用例详情
export function getTestCase(id) {
  return http.get(`/test-cases/${id}`)
}

// 更新测试用例
export function updateTestCase(id, testCaseData) {
  return http.put(`/test-cases/${id}`, testCaseData)
}
