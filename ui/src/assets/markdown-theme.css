/* 自定义 Markdown 主题 - 现代简约风格 */

/* 基本文本样式 */
.markdown-theme .v-show-content {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  color: #333;
  line-height: 1.6;
  background-color: #fff;
  padding: 16px;
}

/* 标题样式 */
.markdown-theme .v-show-content h1,
.markdown-theme .v-show-content h2,
.markdown-theme .v-show-content h3,
.markdown-theme .v-show-content h4,
.markdown-theme .v-show-content h5,
.markdown-theme .v-show-content h6 {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  color: #2c3e50;
  font-weight: 600;
  margin-top: 1.5em;
  margin-bottom: 0.5em;
  line-height: 1.3;
}

.markdown-theme .v-show-content h1 {
  font-size: 2em;
  border-bottom: 2px solid #eaecef;
  padding-bottom: 0.3em;
  color: #1a73e8;
}

.markdown-theme .v-show-content h2 {
  font-size: 1.5em;
  border-bottom: 1px solid #eaecef;
  padding-bottom: 0.3em;
  color: #1a73e8;
}

.markdown-theme .v-show-content h3 {
  font-size: 1.25em;
  color: #2c3e50;
}

.markdown-theme .v-show-content h4 {
  font-size: 1.1em;
  color: #2c3e50;
}

/* 段落样式 */
.markdown-theme .v-show-content p {
  margin-top: 0;
  margin-bottom: 16px;
}

/* 链接样式 */
.markdown-theme .v-show-content a {
  color: #1a73e8;
  text-decoration: none;
  transition: color 0.2s ease;
}

.markdown-theme .v-show-content a:hover {
  color: #174ea6;
  text-decoration: underline;
}

/* 代码块样式 */
.markdown-theme .v-show-content pre {
  background-color: #f8f9fa;
  border-radius: 4px;
  padding: 16px;
  overflow: auto;
  margin-bottom: 16px;
  border: 1px solid #e9ecef;
}

.markdown-theme .v-show-content code {
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 3px;
  padding: 0.2em 0.4em;
  font-size: 0.9em;
}

.markdown-theme .v-show-content pre code {
  background-color: transparent;
  padding: 0;
  font-size: 0.9em;
  color: #24292e;
}

/* 引用块样式 */
.markdown-theme .v-show-content blockquote {
  border-left: 4px solid #1a73e8;
  color: #6a737d;
  padding: 0 1em;
  margin: 0 0 16px 0;
  background-color: #f8f9fa;
  border-radius: 0 4px 4px 0;
}

.markdown-theme .v-show-content blockquote > :first-child {
  margin-top: 0;
}

.markdown-theme .v-show-content blockquote > :last-child {
  margin-bottom: 0;
}

/* 表格样式 */
.markdown-theme .v-show-content table {
  border-collapse: collapse;
  width: 100%;
  margin-bottom: 16px;
  border-radius: 4px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.markdown-theme .v-show-content table th,
.markdown-theme .v-show-content table td {
  border: 1px solid #e9ecef;
  padding: 8px 12px;
}

.markdown-theme .v-show-content table th {
  background-color: #f8f9fa;
  font-weight: 600;
  color: #2c3e50;
  text-align: left;
}

.markdown-theme .v-show-content table tr:nth-child(2n) {
  background-color: #f8f9fa;
}

.markdown-theme .v-show-content table tr:hover {
  background-color: #f1f3f5;
}

/* 列表样式 */
.markdown-theme .v-show-content ul,
.markdown-theme .v-show-content ol {
  padding-left: 2em;
  margin-top: 0;
  margin-bottom: 16px;
}

.markdown-theme .v-show-content li + li {
  margin-top: 0.25em;
}

.markdown-theme .v-show-content ul ul,
.markdown-theme .v-show-content ul ol,
.markdown-theme .v-show-content ol ul,
.markdown-theme .v-show-content ol ol {
  margin-top: 0;
  margin-bottom: 0;
}

/* 水平线样式 */
.markdown-theme .v-show-content hr {
  height: 1px;
  padding: 0;
  margin: 24px 0;
  background-color: #e9ecef;
  border: 0;
}

/* 图片样式 */
.markdown-theme .v-show-content img {
  max-width: 100%;
  box-sizing: border-box;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* 任务列表样式 */
.markdown-theme .v-show-content input[type="checkbox"] {
  margin-right: 0.5em;
}

/* 编辑器主题样式 */
.markdown-theme .v-note-wrapper {
  border: 1px solid #e9ecef;
  border-radius: 4px;
}

.markdown-theme .v-note-op {
  background-color: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.markdown-theme .v-note-panel {
  border-top: 1px solid #e9ecef;
}

/* 设置textarea背景色为白色 */
.markdown-theme .v-note-edit .auto-textarea-input,
.markdown-theme .v-note-edit textarea {
  background-color: #FFF !important;
}

/* 确保编辑区域背景也是白色 */
.markdown-theme .v-note-edit {
  background-color: #FFF !important;
}
