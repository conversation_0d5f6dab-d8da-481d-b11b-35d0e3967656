import request from './request'

// 通用请求方法
export const http = {
  get(url, params) {
    return request({
      url,
      method: 'get',
      params
    })
  },
  post(url, data) {
    return request({
      url,
      method: 'post',
      data
    })
  },
  put(url, data) {
    return request({
      url,
      method: 'put',
      data
    })
  },
  delete(url, params) {
    return request({
      url,
      method: 'delete',
      params
    })
  }
}

// 用户相关API
export const userApi = {
  login(data) {
    return http.post('/login', data)
  },
  getUserInfo() {
    return http.get('/users/me')
  }
};

// 用户管理API
export function getUsers() {
  return http.get('/admin/users')
}

export function createUser(data) {
  return http.post('/admin/users', data)
}

export function updateUser(id, data) {
  return http.put(`/admin/users/${id}`, data)
}

export function deleteUser(id) {
  return http.delete(`/admin/users/${id}`)
}

// 项目管理API
export function getProjects() {
  return http.get('/admin/projects')
}

export function createProject(data) {
  return http.post('/admin/projects', data)
}

export function updateProject(id, data) {
  return http.put(`/admin/projects/${id}`, data)
}

export function deleteProject(id) {
  return http.delete(`/admin/projects/${id}`)
}

export function getProjectMembers(projectId) {
  return http.get(`/admin/projects/${projectId}/members`)
}

export function addProjectMember(projectId, data) {
  return http.post(`/admin/projects/${projectId}/members`, data)
}

export function removeProjectMember(projectId, userId) {
  return http.delete(`/admin/projects/${projectId}/members/${userId}`)
}

export function updateProjectMember(projectId, userId, data) {
  return http.put(`/admin/projects/${projectId}/members/${userId}`, data)
}

// 需求管理API
export function getRequirements(params) {
  return http.get('/admin/requirements', { params })
}

export function createRequirement(data) {
  return http.post('/admin/requirements', data)
}

export function updateRequirement(id, data) {
  return http.put(`/admin/requirements/${id}`, data)
}

export function getRequirement(id) {
  return http.get(`/admin/requirements/${id}`)
}
// 添加默认导出以解决导入错误
export default http;