/**
 * 日期时间工具类
 * 用于转换日期时间格式
 */

/**
 * 将日期时间字符串转换为日期格式
 * @param {string} dateTimeStr - 日期时间字符串，格式为 'yyyy-MM-ddThh:mm:ss.SSSSSS'
 * @returns {string} 日期字符串，格式为 'yyyy-MM-dd'
 */
export function formatToDate(dateTimeStr) {
  if (!dateTimeStr) return '';
  return dateTimeStr.split('T')[0];
}

/**
 * 将日期时间字符串转换为完整时间格式
 * @param {string} dateTimeStr - 日期时间字符串，格式为 'yyyy-MM-ddThh:mm:ss.SSSSSS'
 * @returns {string} 时间字符串，格式为 'yyyy-MM-dd HH:mm:ss'
 */
export function formatToDateTime(dateTimeStr) {
  if (!dateTimeStr) return '';
  const [date, time] = dateTimeStr.split('T');
  const timePart = time.split('.')[0];
  return `${date} ${timePart}`;
}