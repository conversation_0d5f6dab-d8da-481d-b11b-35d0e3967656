* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Microsoft YaHei", -apple-system, BlinkMacSystem<PERSON>ont, Se<PERSON><PERSON>, Robot<PERSON>, Arial, sans-serif;
  background-color: #f5f7fa;
  color: #222;
  min-height: 100vh;
}

:root {
  /* 主要颜色 */
  --primary-color: #1877f2;
  --primary-hover: #0d6efd;
  --primary-light: rgba(24, 119, 242, 0.1);
  --primary-lighter: rgba(24, 119, 242, 0.04);
  --primary-light-active: rgba(24, 119, 242, 0.08);
  
  /* 文本颜色 */
  --text-primary: #222;
  --text-secondary: #666;
  --text-hint: #999;
  
  /* 辅助颜色 */
  --success: #52c41a;
  --success-light: rgba(82, 196, 26, 0.1);
  --warning: #f5a623;
  --warning-light: rgba(245, 166, 35, 0.1);
  --error: #ff4d4f;
  --error-light: rgba(255, 77, 79, 0.1);
  --purple: #722ed1;
  --purple-light: rgba(114, 46, 209, 0.1);
  
  /* 背景和边框 */
  --bg-color: #f5f7fa;
  --header-bg: #fafafa;
  --border-color: #e5e6eb;
  
  /* 间距系统 */
  --spacing-small: 8px;
  --spacing-medium: 16px;
  --spacing-large: 24px;
  --spacing-xlarge: 40px;
  
  /* 布局尺寸 */
  --sidebar-width: 220px;
  --header-height: 60px;
  
  /* 阴影 */
  --shadow-sm: 0 2px 8px rgba(0,0,0,0.05);
  --shadow-md: 0 2px 12px rgba(0,0,0,0.04);
  --shadow-lg: 0 4px 16px rgba(0,0,0,0.08);
  --shadow-xl: 0 8px 24px rgba(0,0,0,0.1);
  
  /* 圆角 */
  --radius-base: 6px;
  --radius-card: 10px;
  --radius-circle: 50%;
}

/* 标准布局类 */
.layout {
  display: flex;
  min-height: 100vh;
}

/* 卡片样式 */
.card {
  background-color: #fff;
  border-radius: var(--radius-card);
  box-shadow: var(--shadow-md);
  padding: var(--spacing-large);
}

/* 按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: 40px;
  padding: 0 24px;
  border-radius: var(--radius-base);
  font-size: 15px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
  border: none;
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background-color: var(--primary-hover);
}

.btn-outline {
  background-color: transparent;
  color: var(--text-secondary);
  border: 1px solid var(--border-color);
}

.btn-outline:hover {
  border-color: var(--primary-color);
  color: var(--primary-color);
}

.btn-success {
  background-color: var(--success);
  color: white;
}

.btn-danger {
  background-color: var(--error);
  color: white;
}

/* 表单元素 */
.form-control {
  width: 100%;
  height: 40px;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-base);
  padding: 0 16px;
  font-size: 14px;
  color: var(--text-primary);
  transition: all 0.3s;
}

.form-control:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px var(--primary-light);
}

/* 徽章样式 */
.badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: 24px;
  padding: 0 10px;
  border-radius: 12px;
  font-size: 13px;
  font-weight: 500;
}

.badge-blue {
  background-color: var(--primary-light);
  color: var(--primary-color);
}

.badge-green {
  background-color: var(--success-light);
  color: var(--success);
}

.badge-orange {
  background-color: var(--warning-light);
  color: var(--warning);
}

.badge-red {
  background-color: var(--error-light);
  color: var(--error);
}

.badge-purple {
  background-color: var(--purple-light);
  color: var(--purple);
}
