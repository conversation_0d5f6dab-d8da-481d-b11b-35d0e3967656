<template>
  <div class="project-management-container">
    <div class="project-management-content">
      <div class="content-header">
        <h2>项目管理</h2>
        <el-button type="primary" @click="handleAddProject">添加项目</el-button>
      </div>
      <div class="wp-style-table-wrap">
        <el-table :data="projectList" style="width: 100%" border class="wp-style-table">
          <el-table-column type="selection" width="55"></el-table-column>
          <el-table-column prop="id" label="项目ID" width="100"></el-table-column>
          <el-table-column prop="name" label="项目名称"></el-table-column>
          <el-table-column prop="git_repo_url" label="Git仓库地址" :show-overflow-tooltip="true"></el-table-column>
          <el-table-column prop="description" label="项目描述" :show-overflow-tooltip="true"></el-table-column>
          <el-table-column label="开发人员" width="120">
            <template v-slot="scope">
              <el-tooltip
                effect="dark"
                :content="formatMembers(scope.row.developers)"
                placement="top"
                :disabled="!scope.row.developers || scope.row.developers.length === 0"
              >
                <span>{{ scope.row.developers ? scope.row.developers.length : 0 }}人</span>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column label="测试人员" width="120">
            <template v-slot="scope">
              <el-tooltip
                effect="dark"
                :content="formatMembers(scope.row.testers)"
                placement="top"
                :disabled="!scope.row.testers || scope.row.testers.length === 0"
              >
                <span>{{ scope.row.testers ? scope.row.testers.length : 0 }}人</span>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column prop="created_at" label="创建日期"></el-table-column>
          <el-table-column label="操作" width="180">
            <template v-slot="scope">
              <el-button size="mini" class="action-button" @click="handleManageProject(scope.row)">管理</el-button>
              <el-button size="mini" class="action-button" @click="handleManageMembers(scope.row)">人员</el-button>
              <el-button size="mini" class="action-button edit-button" @click="handleEdit(scope.row)">编辑</el-button>
              <el-button size="mini" class="action-button delete-button" type="danger" @click="handleDelete(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="table-footer">
          <div class="pagination">
            <span>显示 1 到 {{ projectList.length }}，共 {{ projectList.length }} 条记录</span>
          </div>
        </div>
      </div>

      <!-- 添加/编辑项目对话框 -->
      <el-dialog :title="dialogTitle" v-model="dialogVisible" width="40%">
        <el-form :model="projectForm" :rules="projectRules" ref="projectForm" label-width="100px">
          <el-form-item label="项目" prop="gitlabProject">
            <el-select 
              v-model="projectForm.gitlabProject" 
              filterable 
              placeholder="请选择GitLab项目" 
              @change="handleGitLabProjectChange"
              value-key="id">
              <el-option
                v-for="project in gitlabProjects"
                :key="project.id"
                :label="project.name_with_namespace"
                :value="project"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="项目描述" prop="description">
            <el-input type="textarea" v-model="projectForm.description" :rows="3" placeholder="请输入项目描述"></el-input>
          </el-form-item>
          <el-form-item label="开发人员">
            <el-select v-model="projectForm.developers" multiple filterable placeholder="请选择开发人员" style="width: 100%">
              <el-option
                v-for="user in allUsers"
                :key="user.id"
                :label="`${user.name} (${user.username})`"
                :value="user.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="测试人员">
            <el-select v-model="projectForm.testers" multiple filterable placeholder="请选择测试人员" style="width: 100%">
              <el-option
                v-for="user in allUsers"
                :key="user.id"
                :label="`${user.name} (${user.username})`"
                :value="user.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <template v-slot:footer>
          <span class="dialog-footer">
            <el-button @click="dialogVisible = false">取 消</el-button>
            <el-button type="primary" @click="handleSubmit">确 定</el-button>
          </span>
        </template>
      </el-dialog>

      <!-- 项目成员管理对话框 -->
      <el-dialog title="项目成员管理" v-model="memberDialogVisible" width="60%">
        <div v-if="currentProject" class="project-info">
          <h3>{{ currentProject.name }}</h3>
          <p><strong>Git仓库：</strong> {{ currentProject.git_repo_url }}</p>
        </div>
        <el-tabs v-model="activeTab">
          <el-tab-pane label="开发人员" name="developers">
            <div class="member-toolbar">
              <el-button type="primary" size="small" @click="showAddMember('developer')">添加开发人员</el-button>
            </div>
            <el-table :data="developers" style="width: 100%" border>
              <el-table-column prop="username" label="用户ID"></el-table-column>
              <el-table-column prop="name" label="姓名"></el-table-column>
              <el-table-column prop="join_date" label="加入日期"></el-table-column>
              <el-table-column label="操作" width="100">
                <template v-slot="scope">
                  <el-button size="mini" type="danger" @click="handleRemoveMember(scope.row, 'developer')">移除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>
          <el-tab-pane label="测试人员" name="testers">
            <div class="member-toolbar">
              <el-button type="primary" size="small" @click="showAddMember('tester')">添加测试人员</el-button>
            </div>
            <el-table :data="testers" style="width: 100%" border>
              <el-table-column prop="username" label="用户ID"></el-table-column>
              <el-table-column prop="name" label="姓名"></el-table-column>
              <el-table-column prop="join_date" label="加入日期"></el-table-column>
              <el-table-column label="操作" width="100">
                <template v-slot="scope">
                  <el-button size="mini" type="danger" @click="handleRemoveMember(scope.row, 'tester')">移除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>
        </el-tabs>
        <template v-slot:footer>
          <span class="dialog-footer">
            <el-button @click="memberDialogVisible = false">关 闭</el-button>
          </span>
        </template>
      </el-dialog>

      <!-- 添加成员对话框 -->
      <el-dialog :title="addMemberTitle" v-model="addMemberDialogVisible" width="40%">
        <el-form :model="memberForm" :rules="memberRules" ref="memberForm" label-width="100px">
          <el-form-item label="选择用户" prop="userId">
            <el-select v-model="memberForm.userId" placeholder="请选择用户" filterable>
              <el-option 
                v-for="user in availableUsers" 
                :key="user.id" 
                :label="`${user.name} (${user.username})`" 
                :value="user.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <template v-slot:footer>
          <span class="dialog-footer">
            <el-button @click="addMemberDialogVisible = false">取 消</el-button>
            <el-button type="primary" @click="handleAddMember">确 定</el-button>
          </span>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { getProjects, createProject, updateProject, deleteProject, getProjectMembers, addProjectMember, removeProjectMember, getGitLabProjects } from '@/api/project';
import { getUsers } from '@/api/user';
import { ElMessage, ElMessageBox } from 'element-plus'

export default {
  name: 'ProjectManagement',
  data() {
    return {
      projectList: [],
      dialogVisible: false,
      dialogTitle: '添加项目',
      isEditing: false,
      gitlabProjects: [], // GitLab项目列表
      projectForm: {
        gitlabProject: null, // 选择的GitLab项目
        description: '',
        developers: [],
        testers: []
      },
      projectRules: {
        gitlabProject: [
          { required: true, message: '请选择GitLab项目', trigger: 'change' }
        ]
      },
      // 成员管理相关
      memberDialogVisible: false,
      currentProject: null,
      activeTab: 'developers',
      developers: [],
      testers: [],
      // 添加成员相关
      addMemberDialogVisible: false,
      addMemberTitle: '添加成员',
      addingMemberType: '', // 'developer' 或 'tester'
      memberForm: {
        userId: ''
      },
      memberRules: {
        userId: [
          { required: true, message: '请选择用户', trigger: 'change' }
        ]
      },
      allUsers: [],
      availableUsers: [],
    };
  },
  mounted() {
    this.fetchProjects();
    this.fetchAllUsers();
    this.fetchGitLabProjects();
  },
  watch: {
    activeTab() {
      if (this.currentProject) {
        this.fetchProjectMembers(this.currentProject.id);
      }
    }
  },
  methods: {
    // 项目列表管理
    async fetchProjects() {
      try {
        const response = await getProjects();
        this.projectList = response;
      } catch (error) {
        ElMessage.error('获取项目列表失败: ' + (error.response?.data?.detail || error.message));
      }
    },
    handleManageProject(project) {
      this.$router.push({
        name: 'Project-detail',
        params: { id: project.id }
      });
    },
    async fetchGitLabProjects() {
      try {
        const response = await getGitLabProjects();
        this.gitlabProjects = response.map(project => ({
          ...project,
          id: Number(project.id)
        }));
      } catch (error) {
        ElMessage.error('获取GitLab项目列表失败: ' + (error.response?.data?.detail || error.message));
      }
    },
    handleGitLabProjectChange(selectedProject) {
      if (selectedProject) {
        // 确保选中的项目数据类型一致
        if (selectedProject.id) {
          selectedProject.id = Number(selectedProject.id);
        }
        // 选择GitLab项目后自动填充描述
        this.projectForm.description = selectedProject.description || '';
      }
    },
    handleAddProject() {
      this.dialogTitle = '添加项目';
      this.isEditing = false;
      this.projectForm = {
        gitlabProject: null,
        description: '',
        developers: [],
        testers: []
      };
      this.dialogVisible = true;
    },
    handleEdit(row) {
      this.dialogTitle = '编辑项目';
      this.isEditing = true;
      
      // 克隆项目数据
      const projectData = { ...row };
      
      // 处理开发人员和测试人员数据
      if (projectData.developers && Array.isArray(projectData.developers)) {
        projectData.developers = projectData.developers.map(dev => 
          typeof dev === 'object' ? dev.id : dev
        );
      } else {
        projectData.developers = [];
      }
      
      if (projectData.testers && Array.isArray(projectData.testers)) {
        projectData.testers = projectData.testers.map(tester => 
          typeof tester === 'object' ? tester.id : tester
        );
      } else {
        projectData.testers = [];
      }
      
      // 编辑时不需要选择GitLab项目
      this.projectForm = {
        id: projectData.id,
        name: projectData.name,
        git_repo_url: projectData.git_repo_url,
        description: projectData.description,
        developers: projectData.developers,
        testers: projectData.testers
      };
      
      this.dialogVisible = true;
    },
    async handleSubmit() {
      this.$refs['projectForm'].validate(async (valid) => {
        if (valid) {
          try {
            // 创建要提交的数据对象
            let projectData;
            
            if (this.isEditing) {
              // 编辑项目
              projectData = {
                name: this.projectForm.name,
                git_repo_url: this.projectForm.git_repo_url,
                description: this.projectForm.description,
                developer_ids: this.projectForm.developers || [],
                tester_ids: this.projectForm.testers || []
              };
              await updateProject(this.projectForm.id, projectData);
              ElMessage.success('项目更新成功');
            } else {
              // 创建新项目
              const gitlabProject = this.projectForm.gitlabProject;
              projectData = {
                name: gitlabProject.name,
                git_repo_url: gitlabProject.http_url_to_repo,
                description: this.projectForm.description,
                developer_ids: this.projectForm.developers || [],
                tester_ids: this.projectForm.testers || [],
                id: gitlabProject.id
              };
              await createProject(projectData);
              ElMessage.success('项目创建成功');
            }
            
            this.dialogVisible = false;
            this.fetchProjects();
          } catch (error) {
            ElMessage.error((this.isEditing ? '更新' : '创建') + '项目失败: ' + error.message);
          }
        } else {
          return false;
        }
      });
    },
    handleDelete(row) {
      ElMessageBox.confirm('此操作将永久删除该项目, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          await deleteProject(row.id);
          ElMessage.success('项目删除成功');
          this.fetchProjects();
        } catch (error) {
          ElMessage.error('删除项目失败: ' + error.message);
        }
      }).catch(() => {
        ElMessage.info('已取消删除');
      });
    },
    
    // 成员管理
    async handleManageMembers(project) {
      this.currentProject = project;
      this.memberDialogVisible = true;
      this.activeTab = 'developers';
      await this.fetchProjectMembers(project.id);
    },
    async fetchProjectMembers(projectId) {
      try {
        if (this.activeTab === 'developers') {
          const members = await getProjectMembers(projectId, 'developer');
          this.developers = members;
        } else if (this.activeTab === 'testers') {
          const members = await getProjectMembers(projectId, 'tester');
          this.testers = members;
        } else {
          const members = await getProjectMembers(projectId);
          this.developers = members.filter(m => m.role === 'developer');
          this.testers = members.filter(m => m.role === 'tester');
        }
      } catch (error) {
        ElMessage.error('获取项目成员失败: ' + error.message);
      }
    },
    async fetchAllUsers() {
      try {
        this.allUsers = await getUsers();
      } catch (error) {
        ElMessage.error('获取用户列表失败: ' + error.message);
      }
    },
    
    // 添加成员
    showAddMember(type) {
      this.addingMemberType = type;
      this.addMemberTitle = type === 'developer' ? '添加开发人员' : '添加测试人员';
      this.memberForm.userId = '';
      
      // 过滤出可添加的用户（排除已经是该角色的用户）
      const currentMembers = type === 'developer' ? this.developers : this.testers;
      const currentMemberIds = currentMembers.map(m => m.id);
      this.availableUsers = this.allUsers.filter(user => !currentMemberIds.includes(user.id));
      
      this.addMemberDialogVisible = true;
    },
    async handleAddMember() {
      this.$refs['memberForm'].validate(async (valid) => {
        if (valid) {
          try {
            await addProjectMember(this.currentProject.id, {
              user_id: this.memberForm.userId,
              role: this.addingMemberType === 'developer' ? 'developer' : 'tester'
            });
            ElMessage.success('添加成员成功');
            this.addMemberDialogVisible = false;
            await this.fetchProjectMembers(this.currentProject.id);
          } catch (error) {
            ElMessage.error('添加成员失败: ' + error.message);
          }
        } else {
          return false;
        }
      });
    },
    async handleRemoveMember(member, type) {
      ElMessageBox.confirm(`确定要移除${type === 'developer' ? '开发人员' : '测试人员'} ${member.name} 吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          await removeProjectMember(this.currentProject.id, member.id, type);
          ElMessage.success('移除成员成功');
          await this.fetchProjectMembers(this.currentProject.id);
        } catch (error) {
          ElMessage.error('移除成员失败: ' + error.message);
        }
      }).catch(() => {
        ElMessage.info('已取消操作');
      });
    },
    
    // 辅助方法
    formatMembers(members) {
      if (!members || members.length === 0) return '暂无';
      return members.map(m => m.name).join(', ');
    }
  }
};
</script>

<style scoped>
.project-management-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  font-family: Helvetica, Arial, sans-serif;
}

.project-management-content {
  flex: 1;
  background-color: #f0f2f5;
  padding: 16px;
}

.content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.content-header h2 {
  font-size: 28px;
  font-weight: 500;
  color: #1d2129;
  line-height: 32px;
}

.wp-style-table-wrap {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  overflow: hidden;
}

.wp-style-table {
  width: 100%;
  border: none;
}

.wp-style-table :deep(.el-table__header-wrapper) {
  background-color: #f8f9fa;
}

.wp-style-table :deep(.el-table__header) {
  border-bottom: 1px solid #e9ecef;
}

.wp-style-table :deep(.el-table__row) {
  border-bottom: 1px solid #e9ecef;
  transition: background-color 0.2s;
}

.wp-style-table :deep(.el-table__row:hover) {
  background-color: #f8f9fa;
}

.table-footer {
  display: flex;
  justify-content: flex-end;
  padding: 12px 16px;
  background-color: #f8f9fa;
  border-top: 1px solid #e9ecef;
}

.action-button {
  padding: 4px 8px;
  margin: 0 4px;
}

.edit-button {
  color: #409eff;
  border-color: #c6e2ff;
  background-color: #ecf5ff;
}

.delete-button {
  color: #f56c6c;
  border-color: #fbc4c4;
  background-color: #fef0f0;
}

.project-info {
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e9ecef;
}

.member-toolbar {
  margin-bottom: 12px;
  display: flex;
  justify-content: flex-end;
}
</style> 