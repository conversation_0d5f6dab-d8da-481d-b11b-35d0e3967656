<template>
  <div class="login-container">
    <div class="login-container-inner">
      <div class="login-banner">
        <div class="banner-logo">项目管理系统</div>
        <div class="banner-content">
          <h1 class="banner-title">高效管理项目流程</h1>
          <p class="banner-desc">集成了项目、需求、测试用例、PR、用户与权限等核心管理功能，支持与GitLab代码仓库联动，适用于需求全流程追踪、协作开发与质量保障。</p>
        </div>
        <div class="banner-footer">© 2025 千树Java项目组</div>
      </div>
      <div class="login-form-container">
        <h2 class="login-title">账号登录</h2>
        <el-form :model="loginForm" :rules="loginRules" ref="loginFormRef" class="login-form">
          <div class="form-item">
            <label class="form-label">用户名</label>
            <el-input
              v-model="loginForm.username"
              placeholder="请输入用户名"
              size="large"
              class="form-input">
            </el-input>
          </div>
          <div class="form-item">
            <label class="form-label">密码</label>
            <el-input
              v-model="loginForm.password"
              type="password"
              placeholder="请输入密码"
              size="large"
              class="form-input">
            </el-input>
          </div>
          <el-button type="primary" :loading="loading" @click="handleLogin" class="login-btn">登录</el-button>
          <div class="login-footer">
            <a href="#" @click.prevent="handleForgetPassword">忘记密码</a>
            <span>请联系管理员申请账号</span>
          </div>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { userApi } from '@/utils/api'
import { useUserStore } from '@/stores/user'

const router = useRouter()
const userStore = useUserStore()
const loginFormRef = ref(null)
const loading = ref(false)

// 登录表单数据
const loginForm = reactive({
  username: '',
  password: ''
})

// 表单验证规则
const loginRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' }
  ]
}

// 登录处理
const handleLogin = () => {
  loginFormRef.value.validate(async (valid) => {
    if (!valid) return

    loading.value = true
    try {
      const res = await userApi.login(loginForm)
      const token = res.access_token
      localStorage.setItem('token', token)

      // 获取用户信息
      const userInfoRes = await userApi.getUserInfo()
      localStorage.setItem('userInfo', JSON.stringify(userInfoRes))

      // 更新Pinia存储
      userStore.login(userInfoRes, token)

      ElMessage.success('登录成功')
      router.push('/')
    } catch (error) {
      console.error(error)
    } finally {
      loading.value = false
    }
  })
}

const handleForgetPassword = () => {
  ElMessage.info('忘记密码功能正在开发中...')
}
</script>

<style scoped>
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  width: 100vw;
  background-color: var(--bg-color);
  position: fixed;
  top: 0;
  left: 0;
  margin: 0;
  padding: 0;
  font-family: "Microsoft YaHei", -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Arial, sans-serif;
}

.login-container-inner {
  display: flex;
  width: 900px;
  height: 500px;
  box-shadow: var(--shadow-xl);
  border-radius: 12px;
  overflow: hidden;
}

.login-banner {
  flex: 1;
  background: linear-gradient(135deg, var(--primary-color), #36cfc9);
  padding: var(--spacing-xlarge);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  color: white;
}

.banner-logo {
  font-size: 24px;
  font-weight: bold;
}

.banner-content {
  margin-top: -80px;
}

.banner-title {
  font-size: 32px;
  margin-bottom: var(--spacing-medium);
}

.banner-desc {
  font-size: 16px;
  opacity: 0.9;
  line-height: 1.6;
}

.banner-footer {
  font-size: 14px;
  opacity: 0.7;
}

.login-form-container {
  flex: 1;
  background: #fff;
  padding: var(--spacing-xlarge);
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.login-title {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: var(--spacing-xlarge);
  color: var(--text-primary);
}

.login-form {
  display: flex;
  flex-direction: column;
}

.form-item {
  margin-bottom: var(--spacing-large);
}

.form-label {
  display: block;
  margin-bottom: var(--spacing-small);
  font-size: 14px;
  color: var(--text-secondary);
}

:deep(.form-input) {
  width: 100%;
}

:deep(.form-input .el-input__wrapper) {
  height: 44px;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-base);
  padding: 0 16px;
  box-shadow: none !important;
}

:deep(.form-input .el-input__wrapper:hover) {
  border-color: var(--primary-color);
}

:deep(.form-input .el-input__wrapper.is-focus) {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px var(--primary-light) !important;
}

:deep(.form-input .el-input__inner) {
  height: 42px;
  font-size: 15px;
  color: var(--text-primary);
}

.login-btn {
  height: 44px;
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: var(--radius-base);
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
  margin-top: var(--spacing-small);
}

.login-btn:hover {
  background: var(--primary-hover);
}

.login-footer {
  display: flex;
  justify-content: space-between;
  margin-top: var(--spacing-medium);
  font-size: 14px;
}

.login-footer a {
  color: var(--primary-color);
  text-decoration: none;
}

.login-footer a:hover {
  text-decoration: underline;
}

.login-footer span {
  color: var(--text-secondary);
}

:deep(.el-form-item__error) {
  color: #ff4d4f;
}
</style>