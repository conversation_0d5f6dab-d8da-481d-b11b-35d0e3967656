<template>
  <div class="user-management-container">
    <div class="user-management-content">
      <div class="content-header">
        <h2>用户管理</h2>
        <el-button type="primary" @click="handleAddUser">添加用户</el-button>
      </div>
      <div class="wp-style-table-wrap">
        <el-table :data="userList" style="width: 100%" border class="wp-style-table">
          <el-table-column type="selection" width="55"></el-table-column>
          <el-table-column prop="id" label="ID"></el-table-column>
          <el-table-column prop="username" label="用户名">
            <template v-slot="scope">
              <div class="user-info">
                <span class="username">{{ scope.row.username }}</span>
                <span class="user-role" v-if="scope.row.is_admin">(管理员)</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="name" label="姓名"></el-table-column>
          <el-table-column prop="is_active" label="状态">
            <template v-slot="scope">
              <span class="status" :class="scope.row.is_active ? 'active' : 'inactive'">
                {{ scope.row.is_active ? '启用' : '禁用' }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="created_at" label="加入日期"></el-table-column>
          <el-table-column label="操作">
            <template v-slot="scope">
              <el-button size="mini" class="action-button edit-button" @click="handleEdit(scope.row)">编辑</el-button>
              <el-button size="mini" class="action-button delete-button" type="danger" @click="handleDelete(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="table-footer">
          <div class="pagination">
            <!-- 分页控件可以根据实际需求添加 -->
            <span>显示 1 到 {{ userList.length }}，共 {{ userList.length }} 条记录</span>
          </div>
        </div>
      </div>

      <!-- 添加/编辑用户对话框 -->
      <el-dialog :title="dialogTitle" v-model="dialogVisible" width="30%">
        <el-form :model="userForm" :rules="rules" ref="userForm" label-width="100px">
          <el-form-item label="用户ID" prop="username">
            <el-input v-model="userForm.username" :disabled="isEditing && userForm.id !== undefined"></el-input>
          </el-form-item>
          <el-form-item label="姓名" prop="name">
            <el-input v-model="userForm.name"></el-input>
          </el-form-item>
          <el-form-item label="密码" prop="password" v-if="!isEditing">
            <el-input v-model="userForm.password" type="password"></el-input>
          </el-form-item>
          <el-form-item label="角色">
            <el-select v-model="userForm.is_admin" placeholder="请选择角色">
              <el-option label="管理员" :value="true"></el-option>
              <el-option label="普通用户" :value="false"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="userForm.is_active" placeholder="请选择状态">
              <el-option label="启用" :value="true"></el-option>
              <el-option label="禁用" :value="false"></el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <template v-slot:footer>
          <span class="dialog-footer">
            <el-button @click="dialogVisible = false">取 消</el-button>
            <el-button type="primary" @click="handleSubmit">确 定</el-button>
          </span>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { getUsers, createUser, updateUser, deleteUser } from '@/utils/api';
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { ref } from 'vue'

export default {
  name: 'UserManagement',
  data() {
    return {
      userList: [],
      dialogVisible: false,
      dialogTitle: '添加用户',
      isEditing: false,
      bulkAction: '',
      userForm: {
        id: undefined,
        username: '',
        name: '',
        password: '',
        is_admin: false,
        is_active: true
      },
      rules: {
        username: [
          { required: true, message: '请输入用户ID', trigger: 'blur' }
        ],
        name: [
          { required: true, message: '请输入姓名', trigger: 'blur' }
        ],
        password: [
          { required: true, message: '请输入密码', trigger: 'blur' }
        ]
      }
    };
  },
  setup() {
    return {}
  },
  mounted() {
    this.fetchUsers();
  },
  methods: {
    async fetchUsers() {
      try {
        const response = await getUsers();
        this.userList = response;
        console.log('用户列表数据:', this.userList);
        if (this.userList && this.userList.length > 0) {
          console.log('第一个用户对象:', this.userList[0]);
        }
      } catch (error) {
        console.error('获取用户列表失败:', error);
        this.$message.error('获取用户列表失败: ' + (error.response?.data?.detail || error.message));
      }
    },
    handleAddUser() {
      this.dialogTitle = '添加用户';
      this.isEditing = false;
      this.userForm = {
        id: undefined,
        username: '',
        name: '',
        password: '',
        is_admin: false,
        is_active: true
      };
      this.dialogVisible = true;
    },
    handleEdit(row) {
      this.dialogTitle = '编辑用户';
      this.isEditing = true;
      this.userForm = { ...row, password: '' };
      this.dialogVisible = true;
    },
    async handleSubmit() {
      this.$refs['userForm'].validate(async (valid) => {
        if (valid) {
          try {
            if (this.isEditing) {
              // 创建一个只包含修改字段的对象
              const updateData = {
                username: this.userForm.username,
                name: this.userForm.name,
                is_admin: this.userForm.is_admin,
                is_active: this.userForm.is_active
              };

              // 如果密码字段有值，才包含密码
              if (this.userForm.password && this.userForm.password.trim() !== '') {
                updateData.password = this.userForm.password;
              }

              console.log('更新用户数据:', updateData);
              await updateUser(this.userForm.id, updateData);
              this.$message.success('用户更新成功');
            } else {
              await createUser(this.userForm);
              this.$message.success('用户添加成功');
            }
            this.dialogVisible = false;
            this.fetchUsers();
          } catch (error) {
            this.$message.error((this.isEditing ? '更新' : '添加') + '用户失败: ' + error.message);
          }
        } else {
          return false;
        }
      });
    },
    handleDelete(row) {
      this.$confirm('此操作将永久删除该用户, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          await deleteUser(row.id);
          this.$message.success('用户删除成功');
          this.fetchUsers();
        } catch (error) {
          this.$message.error('删除用户失败: ' + error.message);
        }
      }).catch(() => {
        this.$message.info('已取消删除');
      });
    },
  }
};
</script>

<style scoped>
.user-management-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  font-family: Helvetica, Arial, sans-serif;
}

.user-management-content {
  flex: 1;
  background-color: #f0f2f5;
  padding: 16px;
}

.content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.content-header h2 {
  font-size: 28px;
  font-weight: 500;
  color: #1d2129;
  line-height: 32px;
}

.wp-style-table-wrap {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  overflow: hidden;
}

.wp-style-table {
  width: 100%;
  border: none;
}

.wp-style-table :deep(.el-table__header-wrapper) {
  background-color: #f8f9fa;
}

.wp-style-table :deep(.el-table__header) {
  border-bottom: 1px solid #e9ecef;
}

.wp-style-table :deep(.el-table__row) {
  border-bottom: 1px solid #e9ecef;
  transition: background-color 0.2s;
}

.wp-style-table :deep(.el-table__row:hover) {
  background-color: #f1f5f9;
}

.wp-style-table :deep(.el-checkbox__inner) {
  border-color: #ced4da;
}

.user-info {
  display: flex;
  flex-direction: column;
}

.username {
  font-weight: 600;
  color: #2c3e50;
}

.user-role {
  font-size: 12px;
  color: #6c757d;
}

.status {
  font-weight: 500;
}

.status.active {
  color: #28a745;
}

.status.inactive {
  color: #dc3545;
}

.action-button {
  padding: 3px 10px;
  font-size: 12px;
  border-radius: 3px;
  margin-right: 5px;
}

.edit-button {
  color: #007bff;
  border-color: #007bff;
  background-color: transparent;
}

.edit-button:hover {
  background-color: #e9ecef;
}

.delete-button {
  color: white;
  border-color: #dc3545;
  background-color: #dc3545;
}

.delete-button:hover {
  background-color: #c82333;
}

.table-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background-color: #f8f9fa;
  border-top: 1px solid #e9ecef;
}


.pagination {
  font-size: 14px;
  color: #6c757d;
}

</style>