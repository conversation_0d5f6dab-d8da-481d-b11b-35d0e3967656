<template>
  <div class="project-detail-container">
    <div class="project-detail-content">
      <div class="content-header">
        <div class="header-left">
          <h2>{{ project.name || '项目详情' }}</h2>
          <p class="project-id" v-if="project.id">项目ID: {{ project.id }}</p>
        </div>
        <div class="header-right">
          <el-button @click="$router.push('/project-management')">返回项目列表</el-button>
        </div>
      </div>

      <el-tabs v-model="activeTab" class="project-tabs">
        <el-tab-pane label="项目信息" name="info">
          <div class="project-info-panel">
            <div class="info-item">
              <span class="label">项目名称:</span>
              <span class="value">{{ project.name || '暂无' }}</span>
            </div>
            <div class="info-item">
              <span class="label">Git仓库:</span>
              <span class="value">{{ project.git_repo_url || '暂无' }}</span>
            </div>
            <div class="info-item">
              <span class="label">项目描述:</span>
              <span class="value">{{ project.description || '暂无描述' }}</span>
            </div>
            <div class="info-item">
              <span class="label">创建日期:</span>
              <span class="value">{{ project.created_at || '暂无' }}</span>
            </div>
            <div class="info-item">
              <span class="label">开发人员:</span>
              <span class="value">{{ formatMembers(project.developers) }}</span>
            </div>
            <div class="info-item">
              <span class="label">测试人员:</span>
              <span class="value">{{ formatMembers(project.testers) }}</span>
            </div>
          </div>
        </el-tab-pane>

        <el-tab-pane label="需求" name="requirements">
          <div class="requirement-toolbar">
            <el-button type="primary" @click="showAddRequirementDialog = true">添加需求</el-button>
            <div class="requirement-filter">
              <el-select v-model="requirementFilter.type" placeholder="需求类型" clearable style="min-width: 150px;">
                <el-option
                  v-for="item in requirementTypes"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
              <el-select v-model="requirementFilter.priority" placeholder="优先级" clearable style="min-width: 150px;">
                <el-option
                  v-for="item in priorityOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
              <el-select v-model="requirementFilter.status" placeholder="状态" clearable style="min-width: 150px;">
                <el-option
                  v-for="item in statusOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
              <el-button type="primary" @click="fetchRequirements">查询</el-button>
            </div>
          </div>

          <el-table :data="requirements" style="width: 100%" border class="wp-style-table">
            <el-table-column prop="requirement_code" label="需求编号" width="120"></el-table-column>
            <el-table-column prop="title" label="需求标题"></el-table-column>
            <el-table-column prop="type" label="需求类型" width="120">
              <template v-slot="scope">
                <el-tag :type="getTypeTagType(scope.row.type)">{{ getTypeName(scope.row.type) }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="priority" label="优先级" width="100">
              <template v-slot="scope">
                <el-tag :type="getPriorityTagType(scope.row.priority)">{{ scope.row.priority }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="git_branch" label="Git分支" width="220">
              <template v-slot="scope">
                <el-tooltip v-if="scope.row.git_branch" :content="scope.row.git_branch" placement="top" effect="light">
                  <span class="git-branch-text">{{ scope.row.git_branch }}</span>
                </el-tooltip>
                <span v-else>-</span>
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="120">
              <template v-slot="scope">
                <el-tag :type="getStatusTagType(scope.row.status)">{{ getStatusName(scope.row.status) }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column label="开发人员" width="120">
              <template v-slot="scope">
                <el-tooltip
                  effect="dark"
                  :content="formatDevelopers(scope.row)"
                  placement="top"
                  :disabled="!scope.row.developers || scope.row.developers.length === 0">
                  <span>{{ scope.row.developers ? scope.row.developers.length : 0 }}人</span>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column label="主目标分支" width="120">
              <template v-slot="scope">
                <el-tag type="success" v-if="scope.row.main_branch">{{ scope.row.main_branch }}</el-tag>
                <span v-else>暂无</span>
              </template>
            </el-table-column>
            <el-table-column prop="start_date" label="计划开始日期" width="120"></el-table-column>
            <el-table-column prop="end_date" label="计划结束日期" width="120"></el-table-column>
            <el-table-column label="操作" width="220">
              <template v-slot="scope">
                <el-button size="mini" @click="viewRequirement(scope.row)">查看</el-button>
                <el-button size="mini" type="primary" @click="editRequirement(scope.row)">编辑</el-button>
                <el-button size="mini" type="danger" @click="confirmDeleteRequirement(scope.row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>

          <div class="pagination-container">
            <el-pagination
              background
              layout="prev, pager, next"
              :total="totalRequirements"
              :page-size="pageSize"
              :current-page="currentPage"
              @current-change="handlePageChange"
            ></el-pagination>
          </div>
        </el-tab-pane>

        <el-tab-pane label="测试功能模块" name="testModules">
          <div class="test-module-toolbar">
            <el-button type="primary" @click="showAddTestModuleDialog = true">添加功能模块</el-button>
          </div>

          <el-table :data="testModules" style="width: 100%" border class="wp-style-table">
            <el-table-column prop="id" label="ID" width="80"></el-table-column>
            <el-table-column prop="name" label="功能模块名称"></el-table-column>
            <el-table-column prop="created_at" label="创建时间" width="180"></el-table-column>
            <el-table-column label="操作" width="220">
              <template v-slot="scope">
                <el-button size="mini" type="primary" @click="editTestModule(scope.row)">编辑</el-button>
                <el-button size="mini" type="danger" @click="confirmDeleteTestModule(scope.row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
      </el-tabs>

      <!-- 统一的需求表单对话框 -->
      <RequirementFormDialog
        v-model:visible="showAddRequirementDialog"
        mode="requirement"
        :project-id="project.id || route.params.id"
        :project-developers="projectDevelopers"
        :project-branches="projectBranches"
        @submit="handleRequirementSubmit"
      />

      <!-- 需求详情/编辑对话框 -->
      <el-dialog :title="isEditing ? '编辑需求' : '需求详情'" v-model="showRequirementDetailDialog" width="70%">
        <el-form v-if="currentRequirement" :model="currentRequirement" :rules="requirementRules" ref="detailForm" label-width="100px">
          <el-form-item label="需求编号">
            <el-input v-model="currentRequirement.requirement_code" disabled></el-input>
          </el-form-item>
          <el-form-item label="需求标题" prop="title">
            <el-input v-model="currentRequirement.title" :disabled="!isEditing"></el-input>
          </el-form-item>
          <el-form-item label="需求内容" prop="content">
            <mavon-editor
              v-if="isEditing"
              v-model="currentRequirement.content"
              :toolbars="markdownOption"
              style="height: 300px"
              class="markdown-theme"
              :toolbarsFlag="false"
              @imgAdd="handleImgAdd"
            ></mavon-editor>
            <div v-else class="markdown-content" v-html="renderMarkdown(currentRequirement.content)"></div>
          </el-form-item>
          <el-form-item label="需求类型" prop="type">
            <el-select v-model="currentRequirement.type" placeholder="请选择需求类型" :disabled="!isEditing">
              <el-option
                v-for="item in requirementTypes"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="优先级" prop="priority">
            <el-select v-model="currentRequirement.priority" placeholder="请选择优先级" :disabled="!isEditing">
              <el-option
                v-for="item in priorityOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-select v-model="currentRequirement.status" placeholder="请选择状态">
              <el-option
                v-for="item in statusOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="开发人员" prop="developers">
            <div v-if="!isEditing && currentRequirement && currentRequirement.developers">
              <div v-if="currentRequirement.developers.length > 0">
                <el-tag
                  v-for="dev in currentRequirement.developers"
                  :key="typeof dev === 'object' ? dev.id : dev"
                  style="margin-right: 5px; margin-bottom: 5px;"
                >
                  {{ getDeveloperName(dev) }}
                </el-tag>
              </div>
              <span v-else>暂无开发人员</span>
            </div>
            <el-select
              v-if="isEditing"
              v-model="currentRequirement.developerIds"
              multiple
              filterable
              placeholder="请选择开发人员"
              style="width: 100%"
            >
              <el-option
                v-for="dev in projectDevelopers"
                :key="dev.id"
                :label="`${dev.name} (${dev.username})`"
                :value="dev.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="主目标分支" prop="main_branch">
            <el-tag type="success" v-if="currentRequirement.main_branch">{{ currentRequirement.main_branch }}</el-tag>
            <span v-else>暂无主目标分支</span>
          </el-form-item>
          <el-form-item label="其他目标分支" prop="other_branches">
            <el-tag v-for="branch in currentRequirement.other_branches" :key="branch" style="margin-right: 5px;">{{ branch }}</el-tag>
            <span v-if="!currentRequirement.other_branches || currentRequirement.other_branches.length === 0">暂无其他目标分支</span>
          </el-form-item>
          <el-form-item label="计划时间" required>
            <el-col :span="11">
              <el-form-item prop="start_date">
                <el-date-picker
                  v-model="currentRequirement.start_date"
                  type="date"
                  placeholder="计划开始日期"
                  style="width: 100%"
                  :disabled="!isEditing"
                ></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col class="line" :span="2">-</el-col>
            <el-col :span="11">
              <el-form-item prop="end_date">
                <el-date-picker
                  v-model="currentRequirement.end_date"
                  type="date"
                  placeholder="计划结束日期"
                  style="width: 100%"
                  :disabled="!isEditing"
                ></el-date-picker>
              </el-form-item>
            </el-col>
          </el-form-item>
          <el-form-item label="提交人">
            <el-input v-model="currentRequirement.submitter_name" disabled></el-input>
          </el-form-item>
          <el-form-item label="提交时间">
            <el-input v-model="currentRequirement.submit_time" disabled></el-input>
          </el-form-item>
          <el-form-item label="Git分支" v-if="currentRequirement.git_branch">
            <el-input v-model="currentRequirement.git_branch" disabled>
              <template #append>
                <el-button @click="copyToClipboard(currentRequirement.git_branch)">复制</el-button>
              </template>
            </el-input>
          </el-form-item>
        </el-form>
        <template v-slot:footer>
          <span class="dialog-footer">
            <el-button @click="showRequirementDetailDialog = false">关 闭</el-button>
            <el-button v-if="!isEditing" type="primary" @click="isEditing = true">编 辑</el-button>
            <el-button v-if="isEditing" type="primary" @click="updateCurrentRequirement">保 存</el-button>
          </span>
        </template>
      </el-dialog>

      <!-- 删除需求确认对话框 -->
      <el-dialog
        title="确认删除"
        v-model="showDeleteConfirmDialog"
        width="30%">
        <span>确定要删除需求"{{ deleteRequirement ? deleteRequirement.title : '' }}"吗？此操作不可恢复。</span>
        <template v-slot:footer>
          <span class="dialog-footer">
            <el-button @click="showDeleteConfirmDialog = false">取 消</el-button>
            <el-button type="danger" @click="deleteRequirementConfirm">确 定</el-button>
          </span>
        </template>
      </el-dialog>

      <!-- 添加测试功能模块对话框 -->
      <el-dialog title="添加测试功能模块" v-model="showAddTestModuleDialog" width="40%">
        <el-form :model="newTestModule" :rules="testModuleRules" ref="testModuleForm" label-width="100px">
          <el-form-item label="模块名称" prop="name">
            <el-input v-model="newTestModule.name" placeholder="请输入功能模块名称（中文）"></el-input>
          </el-form-item>
        </el-form>
        <template v-slot:footer>
          <span class="dialog-footer">
            <el-button @click="showAddTestModuleDialog = false">取 消</el-button>
            <el-button type="primary" @click="submitTestModule">确 定</el-button>
          </span>
        </template>
      </el-dialog>

      <!-- 编辑测试功能模块对话框 -->
      <el-dialog title="编辑测试功能模块" v-model="showEditTestModuleDialog" width="40%">
        <el-form :model="currentTestModule" :rules="testModuleRules" ref="editTestModuleForm" label-width="100px">
          <el-form-item label="模块名称" prop="name">
            <el-input v-model="currentTestModule.name" placeholder="请输入功能模块名称（中文）"></el-input>
          </el-form-item>
        </el-form>
        <template v-slot:footer>
          <span class="dialog-footer">
            <el-button @click="showEditTestModuleDialog = false">取 消</el-button>
            <el-button type="primary" @click="updateTestModule">保 存</el-button>
          </span>
        </template>
      </el-dialog>

      <!-- 删除测试功能模块确认对话框 -->
      <el-dialog
        title="确认删除"
        v-model="showDeleteTestModuleDialog"
        width="30%">
        <span>确定要删除功能模块"{{ deleteTestModule ? deleteTestModule.name : '' }}"吗？此操作不可恢复。</span>
        <template v-slot:footer>
          <span class="dialog-footer">
            <el-button @click="showDeleteTestModuleDialog = false">取 消</el-button>
            <el-button type="danger" @click="deleteTestModuleConfirm">确 定</el-button>
          </span>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted, watch, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { marked } from 'marked'
import { getProject, getRequirements, createRequirement, updateRequirement, deleteRequirement as deleteRequirementApi, getProjectDevelopers, getProjectDevBranches, getTestModules, createTestModule, updateTestModule, deleteTestModule as deleteTestModuleApi } from '@/api/project'
import { getUsers } from '@/api/user'
import RequirementFormDialog from '@/components/RequirementFormDialog.vue'

export default {
  name: 'ProjectDetail',
  components: {
    RequirementFormDialog
  },
  setup() {
    const route = useRoute()
    const router = useRouter()

    // 项目数据
    const project = ref({
      name: '',
      id: '',
      description: '',
      git_repo_url: '',
      created_at: '',
      developers: [],
      testers: []
    })
    const activeTab = ref('info')

    // 需求列表数据
    const requirements = ref([])
    const totalRequirements = ref(0)
    const pageSize = ref(10)
    const currentPage = ref(1)
    const requirementFilter = reactive({
      type: '',
      priority: '',
      status: ''
    })

    // 测试功能模块数据
    const testModules = ref([])
    const showAddTestModuleDialog = ref(false)
    const showEditTestModuleDialog = ref(false)
    const showDeleteTestModuleDialog = ref(false)
    const deleteTestModule = ref(null)
    const currentTestModule = ref(null)
    const newTestModule = reactive({
      name: '',
      project_id: ''
    })

    // 需求表单数据
    const showAddRequirementDialog = ref(false)
    // 监听添加需求对话框的显示状态，确保在显示时获取最新的项目开发人员列表和分支列表
    watch(showAddRequirementDialog, (newVal) => {
      if (newVal) {
        fetchProjectDevelopers()
        fetchProjectBranches()
      }
    })
    const showRequirementDetailDialog = ref(false)
    // 监听需求详情对话框的显示状态，确保在显示时获取最新的项目开发人员列表
    watch(showRequirementDetailDialog, (newVal) => {
      if (newVal) {
        fetchProjectDevelopers()
      }
    })
    const showDeleteConfirmDialog = ref(false)
    const deleteRequirement = ref(null)
    const isEditing = ref(false)
    // 监听编辑状态，确保在切换到编辑模式时获取最新的项目开发人员列表
    watch(isEditing, (newVal) => {
      if (newVal) {
        fetchProjectDevelopers()
      }
    })
    const isSubmitting = ref(false)
    const currentRequirement = ref(null)
    const newRequirement = reactive({
      title: '',
      content: '',
      type: '',
      priority: 'P2',
      status: '待处理',
      developers: [],
      main_branch: '',
      other_branches: [],
      start_date: new Date(),
      end_date: ''
    })

    // Markdown编辑器配置
    const markdownOption = {
      bold: true, // 粗体
      italic: true, // 斜体
      header: true, // 标题
      underline: true, // 下划线
      strikethrough: true, // 中划线
      mark: true, // 标记
      superscript: true, // 上角标
      subscript: true, // 下角标
      quote: true, // 引用
      ol: true, // 有序列表
      ul: true, // 无序列表
      link: true, // 链接
      imagelink: true, // 图片链接
      code: true, // 代码块
      table: true, // 表格
      fullscreen: true, // 全屏编辑
      readmodel: true, // 沉浸式阅读
      htmlcode: true, // 展示html源码
      help: true, // 帮助
      undo: true, // 上一步
      redo: true, // 下一步
      trash: true, // 清空
      save: true, // 保存
      navigation: true, // 导航目录
      alignleft: true, // 左对齐
      aligncenter: true, // 居中
      alignright: true, // 右对齐
      subfield: true, // 单双栏模式
      preview: true, // 预览
      defaultOpen: 'edit', // 默认展示编辑区域
      theme: 'dark', // 编辑区域主题，默认为 'default'
      codeStyle: 'atom-one-dark' // 代码块主题，可选值: default, github, atom-one-dark, atom-one-light, monokai
    }

    const requirementRules = {
      title: [{ required: true, message: '请输入需求标题', trigger: 'blur' }],
      content: [{ required: true, message: '请输入需求内容', trigger: 'blur' }],
      type: [{ required: true, message: '请选择需求类型', trigger: 'change' }],
      priority: [{ required: true, message: '请选择优先级', trigger: 'change' }],
      developers: [{ required: true, message: '请选择开发人员', trigger: 'change' }],
      main_branch: [{ required: true, message: '请选择主目标分支', trigger: 'change' }],
      other_branches: [], // 其他目标分支是可选的
      start_date: [{ required: true, message: '请选择计划开始日期', trigger: 'change' }],
      end_date: [{ required: true, message: '请选择计划结束日期', trigger: 'change' }]
    }

    // 测试功能模块表单验证规则
    const testModuleRules = {
      name: [{ required: true, message: '请输入功能模块名称', trigger: 'blur' }]
    }

    // 选项数据
    const requirementTypes = [
      { value: 'Bug Fixed', label: '修复漏洞' },
      { value: 'Hot Bug Fixed', label: '紧急修复漏洞' },
      { value: 'New Feature', label: '新功能' }
    ]

    const priorityOptions = [
      { value: 'P0', label: 'P0（最高优先级）' },
      { value: 'P1', label: 'P1（高优先级）' },
      { value: 'P2', label: 'P2（普通优先级）' }
    ]

    const statusOptions = [
      { value: '待处理', label: '待处理' },
      { value: '开发中', label: '开发中' },
      { value: '测试中', label: '测试中' },
      { value: '验证中', label: '验证中' },
      { value: '已完成', label: '已完成' }
    ]

    // 用户列表数据
    const allUsers = ref([])
    // 项目开发人员列表
    const projectDevelopers = ref([])
    // 项目分支列表
    const projectBranches = ref([])

    // 过滤后的其他目标分支列表（排除主目标分支）
    const filteredOtherBranches = computed(() => {
      return projectBranches.value.filter(branch =>
        branch.name !== newRequirement.main_branch
      );
    })

    // 处理主目标分支变更
    const handleMainBranchChange = (value) => {
      // 如果新选择的主目标分支已经在其他目标分支中，则从其他目标分支中移除
      if (newRequirement.other_branches.includes(value)) {
        newRequirement.other_branches = newRequirement.other_branches.filter(branch => branch !== value);
      }
    }

    // 监听其他目标分支变化，确保主目标分支不在其中
    watch(() => newRequirement.other_branches, (newVal) => {
      // 如果主目标分支被添加到其他目标分支中，则清空主目标分支
      if (newVal.includes(newRequirement.main_branch) && newRequirement.main_branch) {
        newRequirement.main_branch = '';
        ElMessage.warning('主目标分支已被添加到其他目标分支中，请重新选择主目标分支');
      }
    }, { deep: true })

    // 获取项目详情
    const fetchProject = async () => {
      try {
        const id = route.params.id
        if (!id) {
          ElMessage.error('项目ID不存在')
          router.push('/project-management')
          return
        }

        const response = await getProject(id)
        project.value = response

        // 获取项目开发人员列表
        fetchProjectDevelopers()
      } catch (error) {
        ElMessage.error('获取项目详情失败: ' + error.message)
        router.push('/project-management')
      }
    }

    // 获取项目开发人员列表
    const fetchProjectDevelopers = async () => {
      try {
        const id = route.params.id
        if (!id) return

        const developers = await getProjectDevelopers(id)
        projectDevelopers.value = developers
      } catch (error) {
        ElMessage.error('获取项目开发人员列表失败: ' + error.message)
      }
    }

    // 获取项目分支列表
    const fetchProjectBranches = async () => {
      try {
        const id = route.params.id
        if (!id) return

        const branches = await getProjectDevBranches(id)
        projectBranches.value = branches
      } catch (error) {
        ElMessage.error('获取项目分支列表失败: ' + error.message)
      }
    }

    // 获取项目需求列表
    const fetchRequirements = async () => {
      try {
        const params = {
          project_id: route.params.id,
          page: currentPage.value,
          pageSize: pageSize.value,
          type: requirementFilter.type || undefined,
          priority: requirementFilter.priority || undefined,
          status: requirementFilter.status || undefined
        }

        console.log('发送需求查询参数:', params)
        const response = await getRequirements(params)

        // 如果返回的是数组而不是分页对象
        if (Array.isArray(response)) {
          requirements.value = response
          totalRequirements.value = response.length
        } else {
          requirements.value = response.items || []
          totalRequirements.value = response.total || 0
        }

        // 添加日志来验证 requirementFilter 的数据
        console.log('requirementFilter:', requirementFilter)
      } catch (error) {
        ElMessage.error('获取需求列表失败: ' + error.message)
      }
    }

    // 分页处理
    const handlePageChange = (page) => {
      currentPage.value = page
      fetchRequirements()
    }

    // 查看需求详情
    const viewRequirement = async (requirement) => {
      console.log('查看需求详情，原始数据:', requirement);

      // 创建一个深拷贝
      const requirementCopy = { ...requirement };

      // 确保已经获取了最新的项目开发人员列表
      await fetchProjectDevelopers();

      // 设置基本数据
      currentRequirement.value = requirementCopy;
      isEditing.value = false;

      // 保存开发人员ID数组，用于编辑模式
      if (Array.isArray(requirementCopy.developers)) {
        if (requirementCopy.developers.length > 0) {
          if (typeof requirementCopy.developers[0] === 'object') {
            // 如果是对象数组，提取ID
            currentRequirement.value.developerIds = requirementCopy.developers.map(dev => dev.id);
          } else {
            // 如果已经是ID数组，直接使用
            currentRequirement.value.developerIds = [...requirementCopy.developers];
          }
        } else {
          currentRequirement.value.developerIds = [];
        }
      } else {
        currentRequirement.value.developerIds = [];
      }

      console.log('设置开发人员ID数组:', currentRequirement.value.developerIds);

      // 显示对话框
      showRequirementDetailDialog.value = true;
    }

    // 编辑需求
    const editRequirement = async (requirement) => {
      console.log('编辑需求，原始数据:', requirement);

      // 创建一个深拷贝
      const requirementCopy = { ...requirement };

      // 确保已经获取了最新的项目开发人员列表
      await fetchProjectDevelopers();

      // 设置基本数据
      currentRequirement.value = requirementCopy;
      isEditing.value = true;

      // 保存开发人员ID数组，用于编辑模式
      if (Array.isArray(requirementCopy.developers)) {
        if (requirementCopy.developers.length > 0) {
          if (typeof requirementCopy.developers[0] === 'object') {
            // 如果是对象数组，提取ID
            currentRequirement.value.developerIds = requirementCopy.developers.map(dev => dev.id);
          } else {
            // 如果已经是ID数组，直接使用
            currentRequirement.value.developerIds = [...requirementCopy.developers];
          }
        } else {
          currentRequirement.value.developerIds = [];
        }
      } else {
        currentRequirement.value.developerIds = [];
      }

      console.log('设置开发人员ID数组:', currentRequirement.value.developerIds);

      // 显示对话框
      showRequirementDetailDialog.value = true;
    }

    // 处理需求提交
    const handleRequirementSubmit = async (formData) => {
      try {
        console.log('提交需求数据:', formData);

        await createRequirement(formData);
        ElMessage.success('需求创建成功');
        showAddRequirementDialog.value = false;
        fetchRequirements();
      } catch (error) {
        console.error('创建需求失败:', error);
        ElMessage.error('创建需求失败: ' + error.message);
      }
    };

    // 提交新需求 (保留原方法以防其他地方使用)
    const submitRequirement = async () => {
      try {
        console.log('newRequirement 对象当前状态:', newRequirement);
        // 表单验证
        if(!newRequirement.title || !newRequirement.content || !newRequirement.type ||
           !newRequirement.priority || !newRequirement.developers.length ||
           !newRequirement.main_branch ||
           !newRequirement.start_date || !newRequirement.end_date) {
          console.log('表单验证失败，存在未填写的必填字段:', newRequirement);
          ElMessage.warning('请填写所有必填字段');
          return;
        }

        // 确保所有图片都已转换为HTML标签
        // 查找并替换所有可能的markdown图片语法
        const markdownImgRegex = /!\[.*?\]\(.*?\)/g;
        if (markdownImgRegex.test(newRequirement.content)) {
          ElMessage.warning('有图片尚未完成处理，请稍等片刻再提交');
          console.log('检测到未处理的Markdown图片语法');
          return;
        }

        console.log('提交需求表单数据:', newRequirement);
        console.log('格式化后的开始日期:', formatDate(newRequirement.start_date));
        console.log('格式化后的结束日期:', formatDate(newRequirement.end_date));

        const requirementData = {
          project_id: Number(route.params.id),
          title: newRequirement.title,
          content: newRequirement.content,
          type: newRequirement.type,
          priority: newRequirement.priority,
          status: '待处理',
          developer_ids: newRequirement.developers,
          main_branch: newRequirement.main_branch,
          other_branches: newRequirement.other_branches,
          start_date: formatDate(newRequirement.start_date),
          end_date: formatDate(newRequirement.end_date)
        }

        console.log('发送到后端的需求数据:', requirementData);

        await createRequirement(requirementData)
        ElMessage.success('需求创建成功')
        showAddRequirementDialog.value = false
        fetchRequirements()

        // 重置表单
        Object.assign(newRequirement, {
          title: '',
          content: '',
          type: '',
          priority: 'P2',
          developers: [],
          main_branch: '',
          other_branches: [],
          start_date: new Date(),
          end_date: ''
        })
      } catch (error) {
        ElMessage.error('创建需求失败: ' + error.message)
      }
    }

    // 更新需求
    const updateCurrentRequirement = async () => {
      if (isSubmitting.value) {
        console.log('已经在提交中，阻止重复提交');
        return;
      }
      try {
        isSubmitting.value = true;
        console.log('开始执行 updateRequirement 函数');
        // 表单验证
        if(!currentRequirement.value.title || !currentRequirement.value.content ||
           !currentRequirement.value.type || !currentRequirement.value.priority ||
           !currentRequirement.value.developers.length ||
           !currentRequirement.value.start_date || !currentRequirement.value.end_date) {
          console.log('表单验证失败，存在未填写的必填字段');
          ElMessage.warning('请填写所有必填字段');
          isSubmitting.value = false;
          return;
        }

        // 分支信息在修改需求时不可编辑，因此不需要验证

        // 确保所有图片都已转换为HTML标签
        // 查找并替换所有可能的markdown图片语法
        const markdownImgRegex = /!\[.*?\]\(.*?\)/g;
        if (markdownImgRegex.test(currentRequirement.value.content)) {
          ElMessage.warning('有图片尚未完成处理，请稍等片刻再提交');
          console.log('检测到未处理的Markdown图片语法');
          isSubmitting.value = false;
          return;
        }

        console.log('表单验证通过，开始处理数据');

        // 使用developerIds作为开发人员ID列表
        let developerIds = currentRequirement.value.developerIds || [];
        console.log('提交需求使用的开发人员ID列表:', developerIds);

        console.log('更新需求表单数据:', currentRequirement.value);

        const requirementData = {
          id: currentRequirement.value.id,
          title: currentRequirement.value.title,
          content: currentRequirement.value.content,
          type: currentRequirement.value.type,
          priority: currentRequirement.value.priority,
          status: currentRequirement.value.status,
          developer_ids: developerIds,
          branches: currentRequirement.value.branches,
          start_date: formatDate(currentRequirement.value.start_date),
          end_date: formatDate(currentRequirement.value.end_date)
        }

        console.log('发送到后端的更新数据:', requirementData);

        console.log('准备调用 API 更新需求');
        await updateRequirement(requirementData)
        console.log('API 调用成功，需求更新完成');
        ElMessage.success('需求更新成功')
        console.log('更新状态：关闭编辑模式和对话框');
        isEditing.value = false
        showRequirementDetailDialog.value = false
        console.log('状态更新后: isEditing=', isEditing.value, 'showRequirementDetailDialog=', showRequirementDetailDialog.value);
        fetchRequirements()
        console.log('完成 fetchRequirements 调用');
      } catch (error) {
        ElMessage.error('更新需求失败: ' + error.message)
      } finally {
        isSubmitting.value = false;
        console.log('提交完成，恢复提交状态');
      }
    }

    // 确认删除需求
    const confirmDeleteRequirement = (requirement) => {
      deleteRequirement.value = requirement
      showDeleteConfirmDialog.value = true
    }

    // 删除需求
    const deleteRequirementConfirm = async () => {
      try {
        if (!deleteRequirement.value || !deleteRequirement.value.id) {
          ElMessage.error('需求ID不存在')
          return
        }

        await deleteRequirementApi(deleteRequirement.value.id)
        ElMessage.success('需求删除成功')
        showDeleteConfirmDialog.value = false
        deleteRequirement.value = null
        fetchRequirements()
      } catch (error) {
        ElMessage.error('删除需求失败: ' + error.message)
      }
    }

    // 处理图片粘贴事件
    const handleImgAdd = (pos, $file) => {
      console.log('图片粘贴事件触发，位置:', pos);

      // 将图片转换为base64
      const reader = new FileReader();
      reader.onload = () => {
        const base64Data = reader.result;
        // 创建HTML img标签
        const imgTag = `<img src="${base64Data}" alt="粘贴的图片" style="max-width: 100%;" />`;

        // 获取当前编辑的内容
        if (isEditing.value && currentRequirement.value) {
          // 查找并替换markdown图片语法为HTML img标签
          // 这里使用正则表达式来匹配各种可能的图片占位符格式
          const regex = new RegExp(`!\\[.*?\\]\\(${pos}\\)`, 'g');
          currentRequirement.value.content = currentRequirement.value.content.replace(regex, imgTag);

          console.log('已替换编辑需求中的图片为HTML标签');
        } else {
          // 查找并替换markdown图片语法为HTML img标签
          const regex = new RegExp(`!\\[.*?\\]\\(${pos}\\)`, 'g');
          newRequirement.content = newRequirement.content.replace(regex, imgTag);

          console.log('已替换新需求中的图片为HTML标签');
        }
      };
      reader.readAsDataURL($file);

      // 返回false可以阻止mavonEditor默认的图片上传行为
      return false;
    };

    // 获取开发人员名称
    const getDeveloperName = (developer) => {
      if (!developer) return '未知';

      // 如果是对象，直接使用name和username
      if (typeof developer === 'object') {
        return `${developer.name} (${developer.username})`;
      }

      // 如果是ID，从projectDevelopers中查找
      const dev = projectDevelopers.value.find(d => d.id === developer);
      return dev ? `${dev.name} (${dev.username})` : `未知(ID:${developer})`;
    };

    // 辅助函数
    const formatMembers = (members) => {
      if (!members || members.length === 0) return '暂无';
      return members.map(m => m.name).join(', ');
    }

    const formatDevelopers = (requirement) => {
      if (!requirement.developers || requirement.developers.length === 0) return '暂无';
      return requirement.developers.map(d => d.name).join(', ');
    }

    const getTypeName = (type) => {
      const typeObj = requirementTypes.find(t => t.value === type);
      return typeObj ? typeObj.label : type;
    }

    const getTypeTagType = (type) => {
      switch (type) {
        case 'Bug Fixed': return 'danger';
        case 'Hot Bug Fixed': return 'warning';
        case 'New Feature': return 'success';
        default: return '';
      }
    }

    const getPriorityTagType = (priority) => {
      switch (priority) {
        case 'P0': return 'danger';
        case 'P1': return 'warning';
        case 'P2': return '';
        default: return '';
      }
    }

    const getStatusName = (status) => {
      const statusObj = statusOptions.find(s => s.value === status);
      return statusObj ? statusObj.label : status;
    }

    const getStatusTagType = (status) => {
      switch (status) {
        case '待处理': return 'pending';
        case '开发中': return 'developing';
        case '测试中': return 'testing';
        case '验证中': return 'validating';
        case '已完成': return 'completed';
        default: return '';
      }
    }

    const formatDate = (date) => {
      if (!date) return '';
      if (typeof date === 'string') return date;

      // 转为ISO标准格式
      return new Date(date).toISOString();
    }

    const renderMarkdown = (content) => {
      if (!content) return '';
      return marked(content);
    }

    // 复制到剪贴板
    const copyToClipboard = (text) => {
      navigator.clipboard.writeText(text).then(() => {
        ElMessage.success('已复制到剪贴板');
      }).catch(() => {
        ElMessage.error('复制失败');
      });
    }

    // 获取测试功能模块列表
    const fetchTestModules = async () => {
      try {
        const projectId = route.params.id;
        if (!projectId) {
          ElMessage.error('项目ID不存在');
          return;
        }

        const response = await getTestModules(projectId);
        testModules.value = response;
      } catch (error) {
        ElMessage.error('获取测试功能模块列表失败: ' + error.message);
      }
    };

    // 编辑测试功能模块
    const editTestModule = (testModule) => {
      currentTestModule.value = { ...testModule };
      showEditTestModuleDialog.value = true;
    };

    // 确认删除测试功能模块
    const confirmDeleteTestModule = (testModule) => {
      deleteTestModule.value = testModule;
      showDeleteTestModuleDialog.value = true;
    };

    // 提交新测试功能模块
    const submitTestModule = async () => {
      try {
        // 表单验证
        if (!newTestModule.name) {
          ElMessage.warning('请填写功能模块名称');
          return;
        }

        const projectId = route.params.id;
        if (!projectId) {
          ElMessage.error('项目ID不存在');
          return;
        }

        // 设置项目ID
        newTestModule.project_id = parseInt(projectId);

        await createTestModule(projectId, newTestModule);
        ElMessage.success('功能模块创建成功');
        showAddTestModuleDialog.value = false;
        fetchTestModules();

        // 重置表单
        newTestModule.name = '';
      } catch (error) {
        ElMessage.error('创建功能模块失败: ' + error.message);
      }
    };

    // 更新测试功能模块
    const updateTestModuleData = async () => {
      try {
        // 表单验证
        if (!currentTestModule.value.name) {
          ElMessage.warning('请填写功能模块名称');
          return;
        }

        const projectId = route.params.id;
        if (!projectId) {
          ElMessage.error('项目ID不存在');
          return;
        }

        const testModuleData = {
          name: currentTestModule.value.name
        };

        await updateTestModule(projectId, currentTestModule.value.id, testModuleData);
        ElMessage.success('功能模块更新成功');
        showEditTestModuleDialog.value = false;
        fetchTestModules();
      } catch (error) {
        ElMessage.error('更新功能模块失败: ' + error.message);
      }
    };

    // 删除测试功能模块
    const deleteTestModuleConfirm = async () => {
      try {
        const projectId = route.params.id;
        if (!projectId || !deleteTestModule.value) {
          ElMessage.error('项目ID或功能模块不存在');
          return;
        }

        await deleteTestModuleApi(projectId, deleteTestModule.value.id);
        ElMessage.success('功能模块删除成功');
        showDeleteTestModuleDialog.value = false;
        fetchTestModules();
        deleteTestModule.value = null;
      } catch (error) {
        ElMessage.error('删除功能模块失败: ' + error.message);
      }
    };

    // 生命周期钩子
    onMounted(async () => {
      fetchProject();
      fetchRequirements();
      fetchProjectBranches();
      fetchTestModules();

      try {
        // 仍然获取所有用户列表，以备不时之需
        const users = await getUsers();
        allUsers.value = users;
      } catch (error) {
        ElMessage.error('获取用户列表失败: ' + error.message);
      }
    });

    watch(() => route.params.id, () => {
      fetchProject(); // This will also call fetchProjectDevelopers
      fetchRequirements();
      fetchProjectBranches();
      fetchTestModules();
    });

    watch(activeTab, (newTab) => {
      if (newTab === 'requirements') {
        fetchRequirements();
        fetchProjectDevelopers(); // 确保在切换到需求标签时获取最新的项目开发人员列表
        fetchProjectBranches(); // 确保在切换到需求标签时获取最新的项目分支列表
      } else if (newTab === 'testModules') {
        fetchTestModules(); // 确保在切换到测试功能模块标签时获取最新的列表
      }
    });

    return {
      // 路由数据
      route,

      // 项目数据
      project,
      activeTab,

      // 需求列表数据
      requirements,
      totalRequirements,
      pageSize,
      currentPage,
      requirementFilter,

      // 需求表单数据
      newRequirement,
      currentRequirement,
      showAddRequirementDialog,
      showRequirementDetailDialog,
      isEditing,
      requirementRules,
      showDeleteConfirmDialog,
      deleteRequirement,

      // 测试功能模块数据
      testModules,
      newTestModule,
      currentTestModule,
      showAddTestModuleDialog,
      showEditTestModuleDialog,
      showDeleteTestModuleDialog,
      deleteTestModule,
      testModuleRules,

      // 选项数据
      requirementTypes,
      priorityOptions,
      statusOptions,
      markdownOption,

      // 全局数据
      allUsers,
      projectDevelopers,
      projectBranches,
      filteredOtherBranches,

      // 需求相关方法
      fetchRequirements,
      fetchProjectDevelopers,
      fetchProjectBranches,
      handlePageChange,
      viewRequirement,
      editRequirement,
      handleRequirementSubmit,
      submitRequirement,
      updateCurrentRequirement,
      confirmDeleteRequirement,
      deleteRequirementConfirm,

      // 测试功能模块相关方法
      fetchTestModules,
      editTestModule,
      confirmDeleteTestModule,
      submitTestModule,
      updateTestModule: updateTestModuleData,
      deleteTestModuleConfirm,

      // 辅助函数
      formatMembers,
      formatDevelopers,
      getTypeName,
      getTypeTagType,
      getPriorityTagType,
      getStatusName,
      getStatusTagType,
      renderMarkdown,
      handleImgAdd,
      copyToClipboard,
      handleMainBranchChange,
      getDeveloperName
    }
  }
}
</script>

<style scoped>
.project-detail-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen-Sans, Ubuntu, Cantarell, 'Helvetica Neue', sans-serif;
}

.project-detail-content {
  flex: 1;
  background-color: #f5f7fa;
  padding: 20px;
}

.content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  background-color: white;
  border-radius: 8px;
  padding: 16px 20px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.header-left {
  display: flex;
  flex-direction: column;
}

.header-left h2 {
  font-size: 24px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0;
}

.project-id {
  font-size: 14px;
  color: #606770;
  margin-top: 4px;
}

.project-tabs {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.project-info-panel {
  padding: 16px 0;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(450px, 1fr));
  gap: 16px;
}

.info-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16px;
  border-bottom: 1px solid #f0f2f5;
  padding-bottom: 16px;
}

.info-item .label {
  width: 140px;
  font-weight: 500;
  color: #606770;
  text-align: right;
  padding-right: 16px;
}

.info-item .value {
  flex: 1;
  color: #1c1e21;
  font-weight: 400;
}

.requirement-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  background-color: white;
  border-radius: 8px;
  padding: 12px 0px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.requirement-filter {
  display: flex;
  align-items: center;
  gap: 12px;
}

.wp-style-table {
  margin-bottom: 20px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

/* 改进表格样式，模仿 WordPress 后台 */
:deep(.el-table) {
  font-size: 14px;
}

:deep(.el-table th) {
  background-color: #f9fafb;
  color: #444;
  font-weight: 600;
  padding: 12px 8px;
}

:deep(.el-table td) {
  padding: 12px 8px;
}

:deep(.el-table--border) {
  border: 1px solid #ebeef5;
  border-radius: 8px;
}

:deep(.el-table--striped .el-table__body tr.el-table__row--striped td) {
  background-color: #f9fafb;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin: 20px 0;
}

.markdown-content {
  padding: 16px;
  background-color: #f9fafb;
  border-radius: 4px;
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #ebeef5;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  color: #333;
  line-height: 1.6;
}

.markdown-content img {
  max-width: 100%;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* 自定义非编辑状态下的 Markdown 样式 */
.markdown-content h1,
.markdown-content h2,
.markdown-content h3,
.markdown-content h4,
.markdown-content h5,
.markdown-content h6 {
  color: #1a73e8;
  font-weight: 600;
  margin-top: 1.5em;
  margin-bottom: 0.5em;
}

.markdown-content h1 {
  font-size: 2em;
  border-bottom: 2px solid #eaecef;
  padding-bottom: 0.3em;
}

.markdown-content h2 {
  font-size: 1.5em;
  border-bottom: 1px solid #eaecef;
  padding-bottom: 0.3em;
}

.markdown-content a {
  color: #1a73e8;
  text-decoration: none;
}

.markdown-content a:hover {
  text-decoration: underline;
}

.markdown-content pre {
  background-color: #f8f9fa;
  border-radius: 4px;
  padding: 16px;
  overflow: auto;
  margin-bottom: 16px;
  border: 1px solid #e9ecef;
}

.markdown-content code {
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 3px;
  padding: 0.2em 0.4em;
  font-size: 0.9em;
}

.markdown-content blockquote {
  border-left: 4px solid #1a73e8;
  color: #6a737d;
  padding: 0 1em;
  margin: 0 0 16px 0;
  background-color: #f8f9fa;
  border-radius: 0 4px 4px 0;
}

.markdown-content table {
  border-collapse: collapse;
  width: 100%;
  margin-bottom: 16px;
  border-radius: 4px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.markdown-content table th,
.markdown-content table td {
  border: 1px solid #e9ecef;
  padding: 8px 12px;
}

.markdown-content table th {
  background-color: #f8f9fa;
  font-weight: 600;
  color: #2c3e50;
  text-align: left;
}

.markdown-content table tr:nth-child(2n) {
  background-color: #f8f9fa;
}

.markdown-content ul,
.markdown-content ol {
  padding-left: 2em;
  margin-top: 0;
  margin-bottom: 16px;
}

.markdown-content li + li {
  margin-top: 0.25em;
}

.line {
  text-align: center;
  line-height: 32px;
  color: #909399;
}

/* 修改对话框样式 */
:deep(.el-dialog) {
  border-radius: 8px;
}

:deep(.el-dialog__header) {
  padding: 20px;
  border-bottom: 1px solid #f0f2f5;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-input__inner) {
  border-radius: 4px;
}

:deep(.el-dialog__body) {
  padding: 24px 20px;
}

:deep(.el-dialog__footer) {
  padding: 16px 20px;
  border-top: 1px solid #f0f2f5;
}

/* Markdown编辑器样式 */
:deep(.v-note-wrapper) {
  z-index: 1;
  min-height: 300px;
  width: 100%;
}

:deep(.v-note-panel) {
  min-height: 300px;
}

:deep(.v-note-edit) {
  min-height: 300px;
}

/* 设置textarea背景色为白色 */
:deep(.v-note-edit .auto-textarea-input),
:deep(.v-note-edit textarea) {
  background-color: #FFF !important;
}

/* 确保编辑区域背景也是白色 */
:deep(.v-note-edit) {
  background-color: #FFF !important;
}

/* 自定义 Markdown 渲染样式 */
:deep(.v-show-content) {
  /* 修改基本文本样式 */
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  color: #333;
  line-height: 1.6;
}

/* 修改标题样式 */
:deep(.v-show-content h1),
:deep(.v-show-content h2),
:deep(.v-show-content h3),
:deep(.v-show-content h4),
:deep(.v-show-content h5),
:deep(.v-show-content h6) {
  color: #2c3e50;
  font-weight: 600;
  margin-top: 1.5em;
  margin-bottom: 0.5em;
}

:deep(.v-show-content h1) {
  font-size: 2em;
  border-bottom: 1px solid #eaecef;
  padding-bottom: 0.3em;
}

:deep(.v-show-content h2) {
  font-size: 1.5em;
  border-bottom: 1px solid #eaecef;
  padding-bottom: 0.3em;
}

/* 修改链接样式 */
:deep(.v-show-content a) {
  color: #0366d6;
  text-decoration: none;
}

:deep(.v-show-content a:hover) {
  text-decoration: underline;
}

/* 修改代码块样式 */
:deep(.v-show-content pre) {
  background-color: #f6f8fa;
  border-radius: 3px;
  padding: 16px;
  overflow: auto;
}

:deep(.v-show-content code) {
  font-family: Consolas, Monaco, 'Andale Mono', monospace;
  background-color: rgba(27, 31, 35, 0.05);
  border-radius: 3px;
  padding: 0.2em 0.4em;
}

/* 修改引用块样式 */
:deep(.v-show-content blockquote) {
  border-left: 4px solid #dfe2e5;
  color: #6a737d;
  padding: 0 1em;
  margin: 0 0 16px 0;
}

/* 修改表格样式 */
:deep(.v-show-content table) {
  border-collapse: collapse;
  width: 100%;
  margin-bottom: 16px;
}

:deep(.v-show-content table th),
:deep(.v-show-content table td) {
  border: 1px solid #dfe2e5;
  padding: 6px 13px;
}

:deep(.v-show-content table th) {
  background-color: #f6f8fa;
  font-weight: 600;
}

:deep(.v-show-content table tr:nth-child(2n)) {
  background-color: #f6f8fa;
}

/* 修改列表样式 */
:deep(.v-show-content ul),
:deep(.v-show-content ol) {
  padding-left: 2em;
}

:deep(.v-show-content li + li) {
  margin-top: 0.25em;
}

/* Git分支样式 */
.git-branch-text {
  display: inline-block;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-family: monospace;
  background-color: #f0f0f0;
  padding: 2px 6px;
  border-radius: 3px;
  border: 1px solid #ddd;
  color: #333;
}
</style>