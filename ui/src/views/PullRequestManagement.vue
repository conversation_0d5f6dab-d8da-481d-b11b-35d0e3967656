<template>
  <div class="pull-request-management-container">
    <div class="pull-request-management-content">
      <div class="content-header">
        <h2>合并分支管理</h2>
      </div>

      <div class="wp-style-table-wrap">
        <el-table
          v-loading="loading"
          :data="groupedPullRequests"
          style="width: 100%"
          border
          class="wp-style-table"
        >
          <el-table-column prop="requirement.code" label="需求编号" width="120" />
          <el-table-column label="需求标题" min-width="200">
            <template #default="scope">
              <div>{{ scope.row.requirement.title }}</div>
            </template>
          </el-table-column>
          <el-table-column prop="project_name" label="项目" min-width="120" />
          <el-table-column label="PR数量" width="100">
            <template #default="scope">
              {{ scope.row.pull_requests.length }}
            </template>
          </el-table-column>
          <el-table-column label="需求状态" width="120">
            <template #default="scope">
              <el-tag :type="getRequirementStatusType(scope.row.requirement.status)">
                {{ scope.row.requirement.status }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120" fixed="right">
            <template #default="scope">
              <el-button
                type="primary"
                size="small"
                @click="handleViewDetail(scope.row)"
              >
                查看
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="table-footer">
          <div class="pagination">
            <span>显示 1 到 {{ groupedPullRequests.length }}，共 {{ groupedPullRequests.length }} 条记录</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 需求详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      :title="'需求详情: ' + (currentRequirement?.requirement?.code || '')"
      width="70%"
      destroy-on-close
    >
      <div v-if="currentRequirement" class="requirement-detail">
        <div class="detail-section">
          <h3>基本信息</h3>
          <div class="detail-row">
            <div class="detail-item">
              <span class="label">需求编号：</span>
              <span class="value">{{ currentRequirement.requirement.code }}</span>
            </div>
            <div class="detail-item">
              <span class="label">项目：</span>
              <span class="value">{{ currentRequirement.project_name }}</span>
            </div>
          </div>
          <div class="detail-row">
            <div class="detail-item">
              <span class="label">标题：</span>
              <span class="value">{{ currentRequirement.requirement.title }}</span>
            </div>
            <div class="detail-item">
              <span class="label">状态：</span>
              <el-tag :type="getRequirementStatusType(currentRequirement.requirement.status)">
                {{ currentRequirement.requirement.status }}
              </el-tag>
            </div>
          </div>
          <div class="detail-row">
            <div class="detail-item">
              <span class="label">类型：</span>
              <span class="value">{{ currentRequirement.requirement.type }}</span>
            </div>
            <div class="detail-item">
              <span class="label">优先级：</span>
              <span class="value">{{ currentRequirement.requirement.priority }}</span>
            </div>
          </div>
          <div class="detail-row">
            <div class="detail-item full-width">
              <span class="label">Git分支：</span>
              <span class="value">{{ currentRequirement.requirement.git_branch }}</span>
            </div>
          </div>
        </div>

        <div class="detail-section">
          <h3>需求内容</h3>
          <div class="content-box" v-html="currentRequirement.requirement.content"></div>
        </div>

        <div class="detail-section">
          <h3>Pull Requests ({{ currentRequirement.pull_requests.length }})</h3>
          <el-table :data="currentRequirement.pull_requests" border style="width: 100%">
            <el-table-column prop="source_branch" label="源分支" min-width="180" />
            <el-table-column prop="target_branch" label="目标分支" min-width="120" />
            <el-table-column label="PR信息" min-width="120">
              <template #default="scope">
                <div>
                  <div><strong>IID：</strong>{{ scope.row.pr_iid }}</div>
                  <div>
                    <a :href="scope.row.pr_url" target="_blank" class="pr-link">
                      在GitLab中查看
                    </a>
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="状态" width="100">
              <template #default="scope">
                <el-tag
                  :type="getStatusType(scope.row.status)"
                  effect="plain"
                >
                  {{ getStatusDisplay(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="创建时间" width="180">
              <template #default="scope">
                {{ formatDateTime(scope.row.create_time) }}
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="detailDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleMergeAll" :loading="mergeAllLoading">合并</el-button>
          <el-button type="danger" @click="handleReject">关闭</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 拒绝理由对话框 -->
    <el-dialog
      v-model="rejectDialogVisible"
      title="拒绝合并"
      width="40%"
      append-to-body
    >
      <el-form :model="rejectForm" ref="rejectFormRef">
        <el-form-item
          label="拒绝理由"
          prop="reason"
          :rules="[{ required: true, message: '请输入拒绝理由', trigger: 'blur' }]"
        >
          <el-input
            v-model="rejectForm.reason"
            type="textarea"
            :rows="4"
            placeholder="请输入拒绝理由"
          ></el-input>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="rejectDialogVisible = false">取消</el-button>
          <el-button type="danger" @click="confirmReject" :loading="rejectLoading">确认拒绝</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 操作结果对话框 -->
    <el-dialog
      v-model="resultDialogVisible"
      :title="resultDialogTitle"
      width="50%"
    >
      <div class="operation-result">
        <div class="result-message">{{ resultDialogMessage }}</div>

        <div v-if="operationResults && operationResults.length > 0" class="operation-log">
          <h3>操作结果</h3>
          <div v-for="(result, index) in operationResults" :key="index" class="result-item">
            <div><strong>PR ID：</strong>{{ result.pr_id }}</div>
            <div><strong>状态：</strong>{{ result.status === 'success' ? '成功' : '失败' }}</div>
            <div v-if="result.error" class="error-message">
              <strong>错误信息：</strong>{{ result.error }}
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="resultDialogVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  getGroupedPullRequests,
  mergeAllPullRequests,
  rejectRequirement
} from '../api/pullRequest'

export default {
  name: 'PullRequestManagement',
  setup() {
    // 列表数据
    const groupedPullRequests = ref([])
    const loading = ref(false)

    // 详情对话框
    const detailDialogVisible = ref(false)
    const currentRequirement = ref(null)
    const mergeAllLoading = ref(false)

    // 拒绝对话框
    const rejectDialogVisible = ref(false)
    const rejectForm = ref({ reason: '' })
    const rejectFormRef = ref(null)
    const rejectLoading = ref(false)

    // 结果对话框
    const resultDialogVisible = ref(false)
    const resultDialogTitle = ref('')
    const resultDialogMessage = ref('')
    const operationResults = ref([])

    // 获取按需求分组的PR列表
    const fetchGroupedPullRequests = async () => {
      loading.value = true
      try {
        const res = await getGroupedPullRequests()
        groupedPullRequests.value = res
      } catch (error) {
        console.error('获取PR列表失败:', error)
        ElMessage.error('获取PR列表失败')
      } finally {
        loading.value = false
      }
    }

    // 查看需求详情
    const handleViewDetail = (row) => {
      currentRequirement.value = row
      detailDialogVisible.value = true
    }

    // 批量合并PR
    const handleMergeAll = async () => {
      if (!currentRequirement.value) return

      // 检查是否有开放中的PR
      const openPRs = currentRequirement.value.pull_requests.filter(pr =>
        isOpenStatus(pr.status)
      )

      if (openPRs.length === 0) {
        ElMessage.warning('没有可合并的PR')
        return
      }

      try {
        await ElMessageBox.confirm(
          `确定要合并需求 ${currentRequirement.value.requirement.code} 的所有PR吗？`,
          '合并确认',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )

        mergeAllLoading.value = true
        const res = await mergeAllPullRequests(currentRequirement.value.requirement_id)

        // 显示结果对话框
        resultDialogTitle.value = '批量合并结果'
        resultDialogMessage.value = res.message
        operationResults.value = res.merge_results || []

        // 关闭详情对话框，显示结果对话框
        detailDialogVisible.value = false
        resultDialogVisible.value = true

        // 刷新列表
        await fetchGroupedPullRequests()

      } catch (error) {
        if (error !== 'cancel') {
          console.error('批量合并PR失败:', error)
          ElMessage.error(`批量合并PR失败: ${error.message || '未知错误'}`)
        }
      } finally {
        mergeAllLoading.value = false
      }
    }

    // 打开拒绝对话框
    const handleReject = () => {
      rejectForm.value.reason = ''
      rejectDialogVisible.value = true
    }

    // 确认拒绝
    const confirmReject = async () => {
      if (!rejectFormRef.value) return

      await rejectFormRef.value.validate(async (valid) => {
        if (!valid) return

        try {
          rejectLoading.value = true
          const res = await rejectRequirement(
            currentRequirement.value.requirement_id,
            rejectForm.value.reason
          )

          // 显示结果对话框
          resultDialogTitle.value = '拒绝需求结果'
          resultDialogMessage.value = res.message
          operationResults.value = res.close_results || []

          // 关闭所有对话框，显示结果对话框
          rejectDialogVisible.value = false
          detailDialogVisible.value = false
          resultDialogVisible.value = true

          // 刷新列表
          await fetchGroupedPullRequests()

        } catch (error) {
          console.error('拒绝需求失败:', error)
          ElMessage.error(`拒绝需求失败: ${error.message || '未知错误'}`)
        } finally {
          rejectLoading.value = false
        }
      })
    }

    // 根据状态获取标签类型
    const getStatusType = (status) => {
      if (!status) return 'info'

      // 直接处理字符串状态
      if (typeof status === 'string') {
        if (status === 'OPEN' || status === '开放中') return 'primary'
        if (status === 'MERGED' || status === '已合并') return 'success'
        if (status === 'CLOSED' || status === '已关闭') return 'danger'
        return 'info'
      }

      // 处理对象状态
      if (status.name) {
        if (status.name === 'OPEN') return 'primary'
        if (status.name === 'MERGED') return 'success'
        if (status.name === 'CLOSED') return 'danger'
      }

      if (status.value) {
        if (status.value === '开放中') return 'primary'
        if (status.value === '已合并') return 'success'
        if (status.value === '已关闭') return 'danger'
      }

      return 'info'
    }

    // 获取需求状态标签类型
    const getRequirementStatusType = (status) => {
      if (!status) return 'info'

      if (status === '待处理') return 'info'
      if (status === '开发中') return 'warning'
      if (status === '测试中') return 'primary'
      if (status === '验证中') return 'success'
      if (status === '已完成') return 'success'

      return 'info'
    }

    // 获取状态显示文本
    const getStatusDisplay = (status) => {
      if (!status) return '未知'

      // 直接返回中文状态
      if (typeof status === 'string') {
        // 如果已经是中文状态，直接返回
        if (status === '开放中' || status === '已合并' || status === '已关闭') {
          return status
        }

        // 转换英文状态为中文
        if (status === 'OPEN') return '开放中'
        if (status === 'MERGED') return '已合并'
        if (status === 'CLOSED') return '已关闭'
        return status
      }

      // 处理对象状态
      if (status.value) return status.value

      if (status.name) {
        if (status.name === 'OPEN') return '开放中'
        if (status.name === 'MERGED') return '已合并'
        if (status.name === 'CLOSED') return '已关闭'
      }

      return '未知'
    }

    // 判断是否为开放状态
    const isOpenStatus = (status) => {
      if (!status) return false

      // 直接处理字符串状态
      if (typeof status === 'string') {
        return status === 'OPEN' || status === '开放中'
      }

      // 处理对象状态 - 后端返回 {name: 'OPEN', value: '开放中'} 格式
      if (status.name) return status.name === 'OPEN'
      if (status.value) return status.value === '开放中'

      return false
    }

    // 格式化日期时间
    const formatDateTime = (dateTime) => {
      if (!dateTime) return '-'
      const date = new Date(dateTime)
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    }

    onMounted(() => {
      fetchGroupedPullRequests()
    })

    return {
      groupedPullRequests,
      loading,
      detailDialogVisible,
      currentRequirement,
      mergeAllLoading,
      rejectDialogVisible,
      rejectForm,
      rejectFormRef,
      rejectLoading,
      resultDialogVisible,
      resultDialogTitle,
      resultDialogMessage,
      operationResults,
      handleViewDetail,
      handleMergeAll,
      handleReject,
      confirmReject,
      getStatusType,
      getRequirementStatusType,
      getStatusDisplay,
      isOpenStatus,
      formatDateTime
    }
  }
}
</script>

<style scoped>
.pull-request-management-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  font-family: Helvetica, Arial, sans-serif;
}

.pull-request-management-content {
  flex: 1;
  background-color: #f0f2f5;
  padding: 16px;
}

.content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.content-header h2 {
  font-size: 28px;
  font-weight: 500;
  color: #1d2129;
  line-height: 32px;
}

.wp-style-table-wrap {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  overflow: hidden;
}

.wp-style-table {
  width: 100%;
  border: none;
}

.wp-style-table :deep(.el-table__header-wrapper) {
  background-color: #f8f9fa;
}

.wp-style-table :deep(.el-table__header) {
  border-bottom: 1px solid #e9ecef;
}

.wp-style-table :deep(.el-table__row) {
  border-bottom: 1px solid #e9ecef;
  transition: background-color 0.2s;
}

.wp-style-table :deep(.el-table__row:hover) {
  background-color: #f1f5f9;
}

.table-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background-color: #f8f9fa;
  border-top: 1px solid #e9ecef;
}

.pagination {
  font-size: 14px;
  color: #6c757d;
}

.pr-link {
  color: #1877f2;
  text-decoration: none;
}

.pr-link:hover {
  text-decoration: underline;
}

/* 需求详情样式 */
.requirement-detail {
  padding: 0 10px;
}

.detail-section {
  margin-bottom: 24px;
}

.detail-section h3 {
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e9ecef;
}

.detail-row {
  display: flex;
  margin-bottom: 12px;
}

.detail-item {
  flex: 1;
  display: flex;
  align-items: flex-start;
}

.detail-item.full-width {
  flex: 2;
}

.detail-item .label {
  font-weight: 500;
  color: #606266;
  width: 80px;
  flex-shrink: 0;
}

.detail-item .value {
  flex: 1;
  word-break: break-word;
}

.content-box {
  background-color: #f8f9fa;
  border-radius: 4px;
  padding: 16px;
  max-height: 300px;
  overflow-y: auto;
}

/* 操作结果样式 */
.operation-result {
  padding: 10px;
}

.result-message {
  font-size: 16px;
  margin-bottom: 20px;
}

.operation-log {
  background-color: #f8f9fa;
  border-radius: 4px;
  padding: 15px;
  margin-top: 10px;
}

.operation-log h3 {
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 16px;
}

.result-item {
  margin-bottom: 15px;
  padding-bottom: 15px;
  border-bottom: 1px dashed #e9ecef;
}

.result-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.error-message {
  margin-top: 8px;
  color: #f56c6c;
}

.error-message pre {
  background-color: #fff;
  border: 1px solid #f56c6c;
  border-radius: 4px;
  padding: 10px;
  overflow-x: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
}
</style>
