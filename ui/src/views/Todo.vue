<template>
  <div class="todo-container">
    <div class="content-header">
      <h2>待办事项</h2>
    </div>
    <div class="todo-content">
      <table class="todo-table">
        <thead>
          <tr>
            <th>项目名称</th>
            <th>需求编号</th>
            <th>需求名称</th>
            <th>优先级</th>
            <th>需求类型</th>
            <th>状态</th>
            <th>我的角色</th>
            <th>计划开始时间</th>
            <th>计划完成时间</th>
            <th>操作</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="item in todos" :key="item.requirementId">
            <td>{{ item.projectName }}</td>
            <td>{{ item.requirementCode }}</td>
            <td>{{ item.requirementName }}</td>
            <td>{{ item.priority }}</td>
            <td>{{ item.typeDisplay }}</td>
            <td>
              <span class="status-tag" :class="getStatusClass(item.status)">{{ item.status }}</span>
            </td>
            <td>
              <span v-if="item.isDeveloper" class="role-tag developer">开发人员</span>
              <span v-if="item.isTester" class="role-tag tester">测试人员</span>
            </td>
            <td>{{ formatToDate(item.startDate) }}</td>
            <td>{{ formatToDate(item.endDate) }}</td>
            <td>
              <!-- 所有角色都可以查看 -->
              <button class="view-btn" @click="viewRequirement(item)">查看</button>

              <!-- 开发人员按钮 -->
              <template v-if="item.isDeveloper">
                <!-- 待处理状态：认领 -->
                <button class="claim-btn" @click="startRequirement(item)" v-if="item.status === '待处理'">认领</button>

                <!-- 开发中状态：提交测试 -->
                <button class="submit-test-btn" @click="submitToTest(item)" v-if="item.status === '开发中'">提交测试</button>

                <!-- 测试中状态：撤回测试 -->
                <button class="withdraw-btn" @click="withdrawFromTest(item)" v-if="item.status === '测试中'">撤回测试</button>

                <!-- 验证中状态：撤回验证 -->
                <button class="withdraw-btn" @click="withdrawFromValidation(item)" v-if="item.status === '验证中'">撤回验证</button>
              </template>

              <!-- 测试人员按钮 -->
              <template v-if="item.isTester && item.status === '测试中'">
                <!-- 测试中状态：通过、驳回 -->
                <button class="approve-btn" @click="approveTest(item)">通过</button>
                <button class="reject-btn" @click="showRejectDialog(item, 'test')">驳回</button>
              </template>

              <!-- 管理员按钮 -->
              <template v-if="isAdmin && item.status === '验证中'">
                <!-- 验证中状态：通过验证、驳回验证 -->
                <button class="approve-btn" @click="approveValidation(item)">通过验证</button>
                <button class="reject-btn" @click="showRejectDialog(item, 'validation')">驳回验证</button>
              </template>
            </td>
          </tr>
          <tr v-if="todos.length === 0">
            <td colspan="10" class="no-data">暂无待办事项</td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- 使用公共需求详情组件 -->
    <requirement-detail
      v-model:visible="showRequirementDetailDialog"
      :requirement-id="currentRequirementId"
      :restrict-content="true"
      :can-view-full-content="canViewFullRequirement"
      @closed="handleDetailDialogClosed"
    />

    <!-- 驳回理由对话框 -->
    <el-dialog
      v-model="showRejectReasonDialog"
      :title="rejectType === 'test' ? '驳回测试' : '驳回验证'"
      width="30%"
      :close-on-click-modal="false"
      :close-on-press-escape="true"
    >
      <el-form :model="rejectForm" label-width="80px">
        <el-form-item label="驳回理由" required>
          <el-input
            v-model="rejectForm.reason"
            type="textarea"
            :rows="4"
            placeholder="请输入驳回理由"
          ></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showRejectReasonDialog = false">取消</el-button>
          <el-button type="primary" @click="submitReject" :disabled="!rejectForm.reason">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, onMounted, computed } from 'vue';
import {
  getTodos,
  claimRequirement as apiClaimRequirement,
  submitToTest as apiSubmitToTest,
  withdrawFromTest as apiWithdrawFromTest,
  withdrawFromValidation as apiWithdrawFromValidation,
  approveTest as apiApproveTest,
  rejectTest as apiRejectTest,
  approveValidation as apiApproveValidation,
  rejectValidation as apiRejectValidation
} from '../api/project';
import { formatToDate, formatToDateTime } from '../utils/dateUtils';
import { ElMessage, ElMessageBox } from 'element-plus';
import RequirementDetail from '../components/RequirementDetail.vue';

export default {
  name: 'Todo',
  components: {
    RequirementDetail
  },
  setup() {
    const todos = ref([]);
    const showRequirementDetailDialog = ref(false);
    const showRejectReasonDialog = ref(false);
    const currentRequirementId = ref(null);
    const loading = ref(false);
    const currentViewingItem = ref(null); // 当前正在查看的待办项
    const rejectType = ref(''); // 'test' 或 'validation'
    const currentRejectItem = ref(null); // 当前要驳回的项目
    const rejectForm = ref({ reason: '' }); // 驳回理由表单
    const isAdmin = computed(() => {
      const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
      return userInfo.is_admin === true;
    });

    // 需求类型映射
    const requirementTypes = {
      'Bug Fixed': '修复漏洞',
      'Hot Bug Fixed': '紧急修复漏洞',
      'New Feature': '新功能'
    };

    // 获取状态对应的CSS类
    const getStatusClass = (status) => {
      switch (status) {
        case '待处理': return 'pending';
        case '开发中': return 'developing';
        case '测试中': return 'testing';
        case '验证中': return 'validating';
        case '已完成': return 'completed';
        case '已拒绝': return 'rejected';
        default: return '';
      }
    };

    // 获取历史记录项的类型
    const getHistoryItemType = (action) => {
      switch (action) {
        case '提交需求': return 'primary';
        case '认领需求': return 'success';
        case '提交测试': return 'warning';
        case '撤回测试': return 'info';
        case '通过测试': return 'success';
        case '驳回测试': return 'danger';
        case '撤回验证': return 'info';
        default: return 'info';
      }
    };

    // 复制到剪贴板
    const copyToClipboard = (text) => {
      navigator.clipboard.writeText(text).then(() => {
        ElMessage.success('已复制到剪贴板');
      }).catch(() => {
        ElMessage.error('复制失败');
      });
    };

    const fetchTodos = async () => {
      try {
        const response = await getTodos();
        console.log('完整响应:', response);

        // 获取当前用户ID
        const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
        const currentUserId = userInfo.id;

        // 处理响应数据
        const processedTodos = (response || []).map(item => {
          // 处理assignments字段：分隔逗号，取得数组，并去重
          let assignments = [];
          if (item.assignments && typeof item.assignments === 'string') {
            assignments = [...new Set(item.assignments.split(','))];
          } else if (Array.isArray(item.assignments)) {
            assignments = [...new Set(item.assignments)];
          }

          // 处理testers字段：分隔逗号，取得数组，并去重
          let testers = [];
          if (item.testers && typeof item.testers === 'string') {
            testers = [...new Set(item.testers.split(','))];
          } else if (Array.isArray(item.testers)) {
            testers = [...new Set(item.testers)];
          }

          // 检查当前用户是否为开发人员或测试人员
          const isDeveloper = assignments.some(id => Number(id) === currentUserId);
          const isTester = testers.some(id => Number(id) === currentUserId);

          // 转换需求类型为中文
          const typeInChinese = requirementTypes[item.type] || item.type;

          return {
            ...item,
            assignments,
            testers,
            isDeveloper,
            isTester,
            typeDisplay: typeInChinese
          };
        });

        todos.value = processedTodos;
      } catch (error) {
        console.error('获取待办事项失败:', error);
        ElMessage.error('获取待办事项失败: ' + (error.message || '未知错误'));
      }
    };

    // 判断当前用户是否可以查看完整需求信息
    const canViewFullRequirement = computed(() => {
      // 如果没有当前查看的待办项，返回false
      if (!currentViewingItem.value) return false;

      // 如果用户包含测试角色，可以查看完整信息
      if (currentViewingItem.value.isTester) return true;

      // 如果用户是开发角色，但需求已经被认领（状态不是"待处理"），可以查看完整信息
      if (currentViewingItem.value.isDeveloper && currentViewingItem.value.status !== '待处理') return true;

      // 其他情况不能查看完整信息
      return false;
    });

    // 查看需求详情
    const viewRequirement = (item) => {
      currentViewingItem.value = item; // 保存当前查看的待办项
      currentRequirementId.value = item.requirementId;
      showRequirementDetailDialog.value = true;
    };

    // 处理详情对话框关闭事件
    const handleDetailDialogClosed = () => {
      currentViewingItem.value = null;
    };

    // 开始处理需求（认领）
    const startRequirement = async (item) => {
      try {
        // 确认对话框
        await ElMessageBox.confirm('确定要认领该需求吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'info'
        });

        // 调用认领API
        await apiClaimRequirement(item.requirementId);
        ElMessage.success('需求已认领，状态已更新为开发中');

        // 刷新列表
        fetchTodos();
      } catch (error) {
        if (error !== 'cancel') {
          console.error('认领需求失败:', error);
          ElMessage.error('认领需求失败: ' + (error.message || '未知错误'));
        }
      }
    };

    // 提交测试
    const submitToTest = async (item) => {
      try {
        // 确认对话框
        await ElMessageBox.confirm('确定要将该需求提交测试吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'info'
        });

        // 调用提交测试API
        await apiSubmitToTest(item.requirementId);
        ElMessage.success('需求状态已更新为测试中');

        // 刷新列表
        fetchTodos();
      } catch (error) {
        if (error !== 'cancel') {
          console.error('提交测试失败:', error);
          ElMessage.error('提交测试失败: ' + (error.message || '未知错误'));
        }
      }
    };

    // 撤回测试
    const withdrawFromTest = async (item) => {
      try {
        // 确认对话框
        await ElMessageBox.confirm('确定要撤回测试吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'info'
        });

        // 调用撤回测试API
        await apiWithdrawFromTest(item.requirementId);
        ElMessage.success('需求已撤回，状态已更新为开发中');

        // 刷新列表
        fetchTodos();
      } catch (error) {
        if (error !== 'cancel') {
          console.error('撤回测试失败:', error);
          ElMessage.error('撤回测试失败: ' + (error.message || '未知错误'));
        }
      }
    };

    // 撤回验证
    const withdrawFromValidation = async (item) => {
      try {
        // 确认对话框
        await ElMessageBox.confirm('确定要撤回验证吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'info'
        });

        // 调用撤回验证API
        await apiWithdrawFromValidation(item.requirementId);
        ElMessage.success('需求已撤回，状态已更新为测试中');

        // 刷新列表
        fetchTodos();
      } catch (error) {
        if (error !== 'cancel') {
          console.error('撤回验证失败:', error);
          ElMessage.error('撤回验证失败: ' + (error.message || '未知错误'));
        }
      }
    };

    // 测试通过
    const approveTest = async (item) => {
      try {
        // 确认对话框
        await ElMessageBox.confirm('确定要通过测试吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'info'
        });

        // 调用通过测试API
        await apiApproveTest(item.requirementId);
        ElMessage.success('需求状态已更新为验证中');

        // 刷新列表
        fetchTodos();
      } catch (error) {
        if (error !== 'cancel') {
          console.error('通过测试失败:', error);
          ElMessage.error('通过测试失败: ' + (error.message || '未知错误'));
        }
      }
    };

    // 显示驳回对话框
    const showRejectDialog = (item, type) => {
      rejectType.value = type;
      currentRejectItem.value = item;
      rejectForm.value.reason = ''; // 清空理由
      showRejectReasonDialog.value = true;
    };

    // 提交驳回
    const submitReject = async () => {
      try {
        if (!rejectForm.value.reason) {
          ElMessage.warning('请输入驳回理由');
          return;
        }

        if (rejectType.value === 'test') {
          // 驳回测试
          await apiRejectTest(currentRejectItem.value.requirementId, rejectForm.value.reason);
          ElMessage.success('需求已驳回，状态已更新为开发中');
        } else if (rejectType.value === 'validation') {
          // 驳回验证
          await apiRejectValidation(currentRejectItem.value.requirementId, rejectForm.value.reason);
          ElMessage.success('需求验证已驳回，状态已更新为测试中');
        }

        // 关闭对话框
        showRejectReasonDialog.value = false;

        // 刷新列表
        fetchTodos();
      } catch (error) {
        console.error('驳回操作失败:', error);
        ElMessage.error('驳回操作失败: ' + (error.message || '未知错误'));
      }
    };

    // 管理员通过验证
    const approveValidation = async (item) => {
      try {
        // 确认对话框
        await ElMessageBox.confirm('确定要通过验证吗? 通过后需求将标记为已完成。', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'info'
        });

        // 调用通过验证API
        await apiApproveValidation(item.requirementId);
        ElMessage.success('需求验证已通过，状态已更新为已完成');

        // 刷新列表
        fetchTodos();
      } catch (error) {
        if (error !== 'cancel') {
          console.error('通过验证失败:', error);
          ElMessage.error('通过验证失败: ' + (error.message || '未知错误'));
        }
      }
    };

    onMounted(() => {
      fetchTodos();
    });

    return {
      todos,
      formatToDate,
      formatToDateTime,
      viewRequirement,
      startRequirement,
      submitToTest,
      withdrawFromTest,
      withdrawFromValidation,
      approveTest,
      showRejectDialog,
      submitReject,
      approveValidation,
      requirementTypes,
      getStatusClass,
      getHistoryItemType,
      showRequirementDetailDialog,
      showRejectReasonDialog,
      currentRequirementId,
      loading,
      copyToClipboard,
      canViewFullRequirement,
      currentViewingItem,
      rejectType,
      currentRejectItem,
      rejectForm,
      isAdmin,
      handleDetailDialogClosed
    };
  }
};
</script>

<style scoped>
.todo-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  font-family: Helvetica, Arial, sans-serif;
  background-color: #f0f2f5;
  padding: 16px;
}

.content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.content-header h2 {
  font-size: 28px;
  font-weight: 500;
  color: #1d2129;
  line-height: 32px;
}

.todo-content {
  flex: 1;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  overflow: hidden;
  padding: 16px;
}

.todo-table {
  width: 100%;
  border-collapse: collapse;
}

.todo-table th, .todo-table td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #e8e8e8;
}

.todo-table th {
  font-weight: 600;
  color: #606266;
  background-color: #f8f9fa;
}

.todo-table td {
  color: #303133;
}

.todo-table tr:hover {
  background-color: #f5f7fa;
}

.no-data {
  text-align: center;
  color: #909399;
  font-style: italic;
}

button {
  padding: 3px 10px;
  font-size: 12px;
  border-radius: 3px;
  margin-right: 5px;
  border: 1px solid;
  cursor: pointer;
  transition: all 0.25s;
}

button:hover {
  opacity: 0.8;
}

.view-btn {
  color: #409eff;
  border-color: #409eff;
  background-color: transparent;
}

.claim-btn, .start-btn {
  color: #67c23a;
  border-color: #67c23a;
  background-color: transparent;
}

.submit-test-btn {
  color: #e6a23c;
  border-color: #e6a23c;
  background-color: transparent;
}

.withdraw-btn {
  color: #909399;
  border-color: #909399;
  background-color: transparent;
}

.approve-btn {
  color: #67c23a;
  border-color: #67c23a;
  background-color: transparent;
}

.reject-btn {
  color: #f56c6c;
  border-color: #f56c6c;
  background-color: transparent;
}

.role-tag {
  display: inline-block;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  margin-right: 5px;
  color: white;
}

.role-tag.developer {
  background-color: #409eff;
}

.role-tag.tester {
  background-color: #67c23a;
}

.status-tag {
  display: inline-block;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  color: white;
}

.status-tag.pending {
  background-color: #909399;
}

.status-tag.developing {
  background-color: #409eff;
}

.status-tag.testing {
  background-color: #e6a23c;
}

.status-tag.validating {
  background-color: #9254de;
}

.status-tag.completed {
  background-color: #67c23a;
}

.status-tag.rejected {
  background-color: #f56c6c;
}



.status-tag.small {
  font-size: 10px;
  padding: 1px 4px;
}

.git-branch-tag {
  font-family: monospace;
  max-width: 300px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>