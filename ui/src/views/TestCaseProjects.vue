<template>
  <div class="test-case-projects-container">
    <div class="test-case-projects-content">
      <div class="content-header">
        <h2>测试用例项目列表</h2>
      </div>
      <div class="wp-style-table-wrap">
        <el-table :data="projects" v-loading="loading" style="width: 100%" border class="wp-style-table">
          <el-table-column prop="name" label="项目名称" min-width="200" />
          <el-table-column prop="description" label="项目描述" min-width="300" />
          <el-table-column prop="git_repo_url" label="Git仓库地址" min-width="300" />
          <el-table-column prop="created_at" label="创建时间" min-width="180">
            <template #default="scope">
              {{ formatDate(scope.row.created_at) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120" fixed="right">
            <template #default="scope">
              <el-button type="primary" size="small" @click="viewTestCases(scope.row)">查看</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="table-footer" v-if="projects && projects.length > 0">
          <div class="pagination">
            <span>显示 1 到 {{ projects.length }}，共 {{ projects.length }} 条记录</span>
          </div>
        </div>
        <div class="table-footer" v-else>
          <div class="pagination">
            <span>暂无数据</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'

export default {
  name: 'TestCaseProjects',
  setup() {
    const router = useRouter()
    const projects = ref([])
    const loading = ref(false)

    // 获取项目列表
    const fetchProjects = async () => {
      loading.value = true
      try {
        const response = await fetch('/api/tester-projects', {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          }
        })

        const data = await response.json()

        if (Array.isArray(data)) {
          projects.value = data
        } else if (data && data.data && Array.isArray(data.data)) {
          projects.value = data.data
        } else {
          projects.value = []
        }
      } catch (error) {
        console.error('获取项目列表失败:', error)
        ElMessage.error('获取项目列表失败')
        projects.value = []
      } finally {
        loading.value = false
      }
    }

    // 查看测试用例
    const viewTestCases = (project) => {
      router.push({
        name: 'Test-case-list',
        params: { projectId: project.id }
      })
    }

    // 格式化日期
    const formatDate = (dateString) => {
      if (!dateString) return ''
      const date = new Date(dateString)
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      })
    }

    onMounted(() => {
      fetchProjects()
    })

    return {
      projects,
      loading,
      viewTestCases,
      formatDate
    }
  }
}
</script>

<style scoped>
.test-case-projects-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  font-family: Helvetica, Arial, sans-serif;
}

.test-case-projects-content {
  flex: 1;
  background-color: #f0f2f5;
  padding: 16px;
}

.content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.content-header h2 {
  font-size: 28px;
  font-weight: 500;
  color: #1d2129;
  line-height: 32px;
}



.wp-style-table-wrap {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  overflow: hidden;
}

.wp-style-table {
  width: 100%;
  border: none;
}

.wp-style-table :deep(.el-table__header-wrapper) {
  background-color: #f8f9fa;
}

.wp-style-table :deep(.el-table__header) {
  border-bottom: 1px solid #e9ecef;
}

.wp-style-table :deep(.el-table__row) {
  border-bottom: 1px solid #e9ecef;
  transition: background-color 0.2s;
}

.wp-style-table :deep(.el-table__row:hover) {
  background-color: #f8f9fa;
}

.table-footer {
  display: flex;
  justify-content: flex-end;
  padding: 12px 16px;
  background-color: #f8f9fa;
  border-top: 1px solid #e9ecef;
}
</style>
