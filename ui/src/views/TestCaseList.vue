<template>
  <div class="test-case-list-container">
    <div class="test-case-list-content">
      <div class="content-header">
        <div class="header-left">
          <el-button type="default" icon="el-icon-back" @click="goBack">返回</el-button>
          <h2>测试用例列表</h2>
        </div>
        <el-button type="primary" @click="showAddDialog">新增测试用例</el-button>
      </div>
      <div class="wp-style-table-wrap">
        <div v-if="loading" class="loading-container">
          <el-loading :fullscreen="false" text="加载中..." />
        </div>
        <el-table v-else :data="testCases" style="width: 100%" border class="wp-style-table">
          <el-table-column prop="test_number" label="测试编号" min-width="120" />
          <el-table-column prop="test_name" label="测试名称" min-width="200" />
          <el-table-column prop="module_name" label="测试功能模块" min-width="150" />
          <el-table-column prop="test_type" label="测试类型" min-width="120" />
          <el-table-column prop="test_side" label="测试端" min-width="120" />
          <el-table-column prop="test_status" label="测试状态" min-width="100">
            <template #default="scope">
              <el-tag :type="scope.row.test_status === 'PASS' ? 'success' : 'danger'">
                {{ scope.row.test_status }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="creator_name" label="创建人" min-width="120" />
          <el-table-column prop="created_at" label="创建时间" min-width="180">
            <template #default="scope">
              {{ formatDate(scope.row.created_at) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="150" fixed="right">
            <template #default="scope">
              <el-button type="primary" size="small" @click="viewTestCase(scope.row)">查看</el-button>
              <el-button type="warning" size="small" @click="editTestCase(scope.row)">修改</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="table-footer" v-if="totalItems > 0">
          <div class="pagination">
            <el-pagination
              v-model:current-page="currentPage"
              v-model:page-size="pageSize"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next, jumper"
              :total="totalItems"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 新增测试用例对话框 -->
    <el-dialog
      v-model="addDialogVisible"
      title="新增测试用例"
      width="60%"
      :close-on-click-modal="false"
    >
      <test-case-form
        :project-id="projectId"
        :modules="modules"
        @submit-success="handleAddTestCase"
      />
    </el-dialog>

    <!-- 查看测试用例对话框 -->
    <el-dialog
      v-model="viewDialogVisible"
      title="查看测试用例"
      width="60%"
    >
      <div class="test-case-detail" v-if="currentTestCase">
        <div class="detail-item">
          <div class="detail-label">测试功能模块：</div>
          <div class="detail-value">{{ currentTestCase.module_name }}</div>
        </div>
        <div class="detail-item">
          <div class="detail-label">测试编号：</div>
          <div class="detail-value">{{ currentTestCase.test_number }}</div>
        </div>
        <div class="detail-item">
          <div class="detail-label">测试名称：</div>
          <div class="detail-value">{{ currentTestCase.test_name }}</div>
        </div>
        <div class="detail-item">
          <div class="detail-label">测试类型：</div>
          <div class="detail-value">{{ currentTestCase.test_type }}</div>
        </div>
        <div class="detail-item">
          <div class="detail-label">测试端：</div>
          <div class="detail-value">{{ currentTestCase.test_side }}</div>
        </div>
        <div class="detail-item">
          <div class="detail-label">前提条件：</div>
          <div class="detail-value">{{ currentTestCase.precondition || '无' }}</div>
        </div>
        <div class="detail-item">
          <div class="detail-label">测试步骤：</div>
          <div class="detail-value">{{ currentTestCase.test_steps }}</div>
        </div>
        <div class="detail-item">
          <div class="detail-label">预期结果：</div>
          <div class="detail-value">{{ currentTestCase.expected_result }}</div>
        </div>
        <div class="detail-item">
          <div class="detail-label">关联代码：</div>
          <div class="detail-value">{{ currentTestCase.related_code }}</div>
        </div>
        <div class="detail-item">
          <div class="detail-label">测试状态：</div>
          <div class="detail-value">
            <el-tag :type="currentTestCase.test_status === 'PASS' ? 'success' : 'danger'">
              {{ currentTestCase.test_status }}
            </el-tag>
          </div>
        </div>
        <div class="detail-item">
          <div class="detail-label">创建人：</div>
          <div class="detail-value">{{ currentTestCase.creator_name }}</div>
        </div>
        <div class="detail-item">
          <div class="detail-label">创建时间：</div>
          <div class="detail-value">{{ formatDate(currentTestCase.created_at) }}</div>
        </div>
        <div class="detail-item" v-if="currentTestCase.updater_name">
          <div class="detail-label">最后修改人：</div>
          <div class="detail-value">{{ currentTestCase.updater_name }}</div>
        </div>
        <div class="detail-item" v-if="currentTestCase.updated_at">
          <div class="detail-label">最后修改时间：</div>
          <div class="detail-value">{{ formatDate(currentTestCase.updated_at) }}</div>
        </div>
      </div>
    </el-dialog>

    <!-- 修改测试用例对话框 -->
    <el-dialog
      v-model="editDialogVisible"
      title="修改测试用例"
      width="60%"
      :close-on-click-modal="false"
    >
      <test-case-form
        v-if="currentTestCase"
        :project-id="projectId"
        :modules="modules"
        :edit-mode="true"
        :test-case-data="currentTestCase"
        @submit-success="handleUpdateTestCase"
      />
    </el-dialog>
  </div>
</template>

<script>
import { ref, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import TestCaseForm from '@/components/TestCaseForm.vue'
import { getTestModules, getTestCases, createTestCase, updateTestCase } from '@/api/test_case'

export default {
  name: 'TestCaseList',
  components: {
    TestCaseForm
  },
  setup() {
    const route = useRoute()
    const router = useRouter()
    const projectId = computed(() => Number(route.params.projectId))
    const testCases = ref([])
    const modules = ref([])
    const loading = ref(false)
    const addDialogVisible = ref(false)
    const viewDialogVisible = ref(false)
    const editDialogVisible = ref(false)
    const currentTestCase = ref(null)

    // 分页相关
    const currentPage = ref(1)
    const pageSize = ref(10)
    const totalItems = ref(0)

    // 返回测试用例项目列表页面
    const goBack = () => {
      router.push({ name: 'Test-case-projects' })
    }

    // 获取测试功能模块列表
    const fetchModules = async () => {
      try {
        console.log('Fetching modules for project ID:', projectId.value)
        const res = await getTestModules(projectId.value)
        console.log('Modules API response:', res)

        if (res && res.data) {
          modules.value = res.data
          console.log('Modules set to:', modules.value)
        } else if (Array.isArray(res)) {
          modules.value = res
          console.log('Modules set to array response:', modules.value)
        } else {
          console.error('Unexpected API response format for modules:', res)
          modules.value = []
        }
      } catch (error) {
        console.error('获取测试功能模块列表失败:', error)
        ElMessage.error('获取测试功能模块列表失败')
        modules.value = []
      }
    }

    // 处理页码变化
    const handleCurrentChange = (page) => {
      currentPage.value = page
      fetchTestCases()
    }

    // 处理每页条数变化
    const handleSizeChange = (size) => {
      pageSize.value = size
      currentPage.value = 1 // 重置为第一页
      fetchTestCases()
    }

    // 获取测试用例列表
    const fetchTestCases = async () => {
      loading.value = true
      try {
        console.log('Fetching test cases for project ID:', projectId.value)
        const params = {
          project_id: projectId.value,
          page: currentPage.value,
          pageSize: pageSize.value
        }

        const res = await getTestCases(params)
        console.log('Test cases API response:', res)

        let processedTestCases = []
        let total = 0

        if (res && res.data) {
          processedTestCases = res.data
          total = res.total || res.data.length

          // 确保页码和每页条数与服务器返回的一致
          if (res.page) currentPage.value = res.page;
          if (res.pageSize) pageSize.value = res.pageSize;
        } else if (res && res.items) {
          // 如果返回的是分页对象
          processedTestCases = res.items
          total = res.total

          // 确保页码和每页条数与服务器返回的一致
          if (res.page) currentPage.value = res.page;
          if (res.pageSize) pageSize.value = res.pageSize;
        } else if (Array.isArray(res)) {
          processedTestCases = res
          total = res.length
        } else if (res) {
          // 兼容其他可能的返回格式
          processedTestCases = res
          total = processedTestCases.length
        }

        // 更新总条数
        totalItems.value = total
        testCases.value = processedTestCases

        console.log('Test cases set to:', testCases.value)
        console.log('Total items:', totalItems.value)
      } catch (error) {
        console.error('获取测试用例列表失败:', error)
        ElMessage.error('获取测试用例列表失败')
        testCases.value = []
      } finally {
        loading.value = false
      }
    }

    // 显示新增对话框
    const showAddDialog = () => {
      addDialogVisible.value = true
    }

    // 查看测试用例
    const viewTestCase = (testCase) => {
      currentTestCase.value = testCase
      viewDialogVisible.value = true
    }

    // 编辑测试用例
    const editTestCase = (testCase) => {
      currentTestCase.value = testCase
      editDialogVisible.value = true
    }

    // 处理新增测试用例
    const handleAddTestCase = async (formData) => {
      try {
        await createTestCase(formData)
        ElMessage.success('添加测试用例成功')
        addDialogVisible.value = false
        fetchTestCases()
      } catch (error) {
        console.error('添加测试用例失败:', error)
        ElMessage.error('添加测试用例失败')
      }
    }

    // 处理更新测试用例
    const handleUpdateTestCase = async (formData) => {
      try {
        await updateTestCase(currentTestCase.value.id, formData)
        ElMessage.success('更新测试用例成功')
        editDialogVisible.value = false
        fetchTestCases()
      } catch (error) {
        console.error('更新测试用例失败:', error)
        ElMessage.error('更新测试用例失败')
      }
    }

    // 格式化日期
    const formatDate = (dateString) => {
      if (!dateString) return ''
      const date = new Date(dateString)
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      })
    }

    onMounted(() => {
      fetchModules()
      fetchTestCases()
    })

    return {
      projectId,
      testCases,
      modules,
      loading,
      addDialogVisible,
      viewDialogVisible,
      editDialogVisible,
      currentTestCase,
      showAddDialog,
      viewTestCase,
      editTestCase,
      handleAddTestCase,
      handleUpdateTestCase,
      formatDate,
      goBack,

      // 分页相关
      currentPage,
      pageSize,
      totalItems,
      handleCurrentChange,
      handleSizeChange
    }
  }
}
</script>

<style scoped>
.test-case-list-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  font-family: Helvetica, Arial, sans-serif;
}

.test-case-list-content {
  flex: 1;
  background-color: #f0f2f5;
  padding: 16px;
}

.content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.content-header h2 {
  font-size: 28px;
  font-weight: 500;
  color: #1d2129;
  line-height: 32px;
  margin: 0;
}

.wp-style-table-wrap {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  overflow: hidden;
}

.wp-style-table {
  width: 100%;
  border: none;
}

.wp-style-table :deep(.el-table__header-wrapper) {
  background-color: #f8f9fa;
}

.wp-style-table :deep(.el-table__header) {
  border-bottom: 1px solid #e9ecef;
}

.wp-style-table :deep(.el-table__row) {
  border-bottom: 1px solid #e9ecef;
  transition: background-color 0.2s;
}

.wp-style-table :deep(.el-table__row:hover) {
  background-color: #f8f9fa;
}

.table-footer {
  display: flex;
  justify-content: flex-end;
  padding: 12px 16px;
  background-color: #f8f9fa;
  border-top: 1px solid #e9ecef;
}

.pagination {
  width: 100%;
  display: flex;
  justify-content: flex-end;
}

.test-case-detail {
  padding: 16px;
}

.detail-item {
  display: flex;
  margin-bottom: 15px;
}

.detail-label {
  width: 120px;
  font-weight: bold;
  color: #606266;
}

.detail-value {
  flex: 1;
  word-break: break-all;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
}
</style>
