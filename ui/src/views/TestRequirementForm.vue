<template>
  <div class="test-container">
    <h2>测试统一需求/漏洞表单组件</h2>

    <div class="button-group">
      <el-button type="primary" @click="showBugDialog = true">
        测试上报漏洞
      </el-button>
      <el-button type="success" @click="showRequirementDialog = true">
        测试添加需求
      </el-button>
    </div>

    <div class="info-section">
      <h3>🎨 统一布局设计特性：</h3>
      <div class="feature-grid">
        <div class="feature-item">
          <h4>📋 分区域布局</h4>
          <p>表单按功能分为基本信息、详细内容、分配信息、分支信息、时间计划五个区域</p>
        </div>
        <div class="feature-item">
          <h4>🎯 统一字段样式</h4>
          <p>相同字段在两种模式下保持一致的外观、大小和交互方式</p>
        </div>
        <div class="feature-item">
          <h4>🔄 智能模式切换</h4>
          <p>根据模式自动显示/隐藏字段，并提供清晰的自动设置提示</p>
        </div>
        <div class="feature-item">
          <h4>📱 响应式设计</h4>
          <p>支持移动端适配，在小屏幕设备上自动调整布局</p>
        </div>
        <div class="feature-item">
          <h4>🎨 现代化UI</h4>
          <p>采用卡片式设计、圆角边框、阴影效果，提升视觉体验</p>
        </div>
        <div class="feature-item">
          <h4>⚡ 交互优化</h4>
          <p>悬停效果、焦点状态、过渡动画，提供流畅的用户体验</p>
        </div>
      </div>

      <div class="comparison-section">
        <h4>🔍 布局对比：</h4>
        <div class="comparison-grid">
          <div class="comparison-item before">
            <h5>❌ 之前的问题</h5>
            <ul>
              <li>两个独立对话框，布局不一致</li>
              <li>相同字段样式差异明显</li>
              <li>表单结构混乱，缺乏逻辑分组</li>
              <li>视觉层次不清晰</li>
            </ul>
          </div>
          <div class="comparison-item after">
            <h5>✅ 现在的优势</h5>
            <ul>
              <li>统一的组件，一致的布局结构</li>
              <li>相同字段完全统一的外观</li>
              <li>清晰的功能分区和视觉层次</li>
              <li>现代化的设计语言</li>
            </ul>
          </div>
        </div>
      </div>
    </div>

    <!-- 上报漏洞测试 -->
    <RequirementFormDialog
      v-model:visible="showBugDialog"
      mode="bug"
      :projects-list="projectsList"
      :project-developers="projectDevelopers"
      :project-branches="projectBranches"
      @submit="handleBugSubmit"
      @project-change="handleProjectChange"
    />

    <!-- 添加需求测试 -->
    <RequirementFormDialog
      v-model:visible="showRequirementDialog"
      mode="requirement"
      :project-id="1"
      :project-developers="projectDevelopers"
      :project-branches="projectBranches"
      @submit="handleRequirementSubmit"
    />
  </div>
</template>

<script>
import { ref } from 'vue';
import { ElMessage } from 'element-plus';
import RequirementFormDialog from '@/components/RequirementFormDialog.vue';

export default {
  name: 'TestRequirementForm',
  components: {
    RequirementFormDialog
  },
  setup() {
    const showBugDialog = ref(false);
    const showRequirementDialog = ref(false);

    // 模拟数据
    const projectsList = ref([
      { id: 1, name: '测试项目1' },
      { id: 2, name: '测试项目2' }
    ]);

    const projectDevelopers = ref([
      { id: 1, name: '张三', username: 'zhangsan' },
      { id: 2, name: '李四', username: 'lisi' }
    ]);

    const projectBranches = ref([
      { name: 'main' },
      { name: 'develop' },
      { name: 'feature/test' }
    ]);

    const handleBugSubmit = (formData) => {
      console.log('漏洞提交数据:', formData);
      ElMessage.success('漏洞提交成功（测试）');
      showBugDialog.value = false;
    };

    const handleRequirementSubmit = (formData) => {
      console.log('需求提交数据:', formData);
      ElMessage.success('需求提交成功（测试）');
      showRequirementDialog.value = false;
    };

    const handleProjectChange = (projectId) => {
      console.log('项目变更:', projectId);
      // 这里可以根据项目ID更新开发人员和分支列表
    };

    return {
      showBugDialog,
      showRequirementDialog,
      projectsList,
      projectDevelopers,
      projectBranches,
      handleBugSubmit,
      handleRequirementSubmit,
      handleProjectChange
    };
  }
};
</script>

<style scoped>
.test-container {
  padding: 20px;
}

.button-group {
  margin: 20px 0;
  display: flex;
  gap: 16px;
}

.info-section {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 24px;
  border-radius: 12px;
  margin: 24px 0;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.info-section h3 {
  margin-top: 0;
  margin-bottom: 20px;
  color: #2c3e50;
  font-size: 20px;
  font-weight: 600;
}

.feature-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.feature-item {
  background: white;
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease;
}

.feature-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.feature-item h4 {
  margin: 0 0 8px 0;
  color: #409eff;
  font-size: 14px;
  font-weight: 600;
}

.feature-item p {
  margin: 0;
  color: #606266;
  font-size: 13px;
  line-height: 1.5;
}

.comparison-section {
  margin-top: 24px;
  padding-top: 20px;
  border-top: 2px solid rgba(255, 255, 255, 0.3);
}

.comparison-section h4 {
  margin: 0 0 16px 0;
  color: #2c3e50;
  font-size: 16px;
  font-weight: 600;
}

.comparison-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.comparison-item {
  background: white;
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.comparison-item.before {
  border-left: 4px solid #f56c6c;
}

.comparison-item.after {
  border-left: 4px solid #67c23a;
}

.comparison-item h5 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
}

.comparison-item ul {
  margin: 0;
  padding-left: 16px;
}

.comparison-item li {
  margin: 6px 0;
  font-size: 13px;
  line-height: 1.4;
  color: #606266;
}

@media (max-width: 768px) {
  .feature-grid {
    grid-template-columns: 1fr;
  }

  .comparison-grid {
    grid-template-columns: 1fr;
  }

  .info-section {
    padding: 16px;
  }
}
</style>
