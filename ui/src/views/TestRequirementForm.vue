<template>
  <div class="test-container">
    <h2>测试统一需求/漏洞表单组件</h2>

    <div class="button-group">
      <el-button type="primary" @click="showBugDialog = true">
        测试上报漏洞
      </el-button>
      <el-button type="success" @click="showRequirementDialog = true">
        测试添加需求
      </el-button>
    </div>

    <div class="info-section">
      <h3>组件特性说明：</h3>
      <ul>
        <li><strong>统一组件</strong>：一个组件处理两种业务场景</li>
        <li><strong>模式切换</strong>：通过 mode 属性控制显示内容</li>
        <li><strong>动态字段</strong>：根据模式显示/隐藏特定字段</li>
        <li><strong>智能验证</strong>：根据模式应用不同的验证规则</li>
        <li><strong>图片处理</strong>：支持图片上传并转换为HTML标签</li>
        <li><strong>分支管理</strong>：智能处理主目标分支和其他目标分支的关系</li>
      </ul>
    </div>

    <!-- 上报漏洞测试 -->
    <RequirementFormDialog
      v-model:visible="showBugDialog"
      mode="bug"
      :projects-list="projectsList"
      :project-developers="projectDevelopers"
      :project-branches="projectBranches"
      @submit="handleBugSubmit"
      @project-change="handleProjectChange"
    />

    <!-- 添加需求测试 -->
    <RequirementFormDialog
      v-model:visible="showRequirementDialog"
      mode="requirement"
      :project-id="1"
      :project-developers="projectDevelopers"
      :project-branches="projectBranches"
      @submit="handleRequirementSubmit"
    />
  </div>
</template>

<script>
import { ref } from 'vue';
import { ElMessage } from 'element-plus';
import RequirementFormDialog from '@/components/RequirementFormDialog.vue';

export default {
  name: 'TestRequirementForm',
  components: {
    RequirementFormDialog
  },
  setup() {
    const showBugDialog = ref(false);
    const showRequirementDialog = ref(false);

    // 模拟数据
    const projectsList = ref([
      { id: 1, name: '测试项目1' },
      { id: 2, name: '测试项目2' }
    ]);

    const projectDevelopers = ref([
      { id: 1, name: '张三', username: 'zhangsan' },
      { id: 2, name: '李四', username: 'lisi' }
    ]);

    const projectBranches = ref([
      { name: 'main' },
      { name: 'develop' },
      { name: 'feature/test' }
    ]);

    const handleBugSubmit = (formData) => {
      console.log('漏洞提交数据:', formData);
      ElMessage.success('漏洞提交成功（测试）');
      showBugDialog.value = false;
    };

    const handleRequirementSubmit = (formData) => {
      console.log('需求提交数据:', formData);
      ElMessage.success('需求提交成功（测试）');
      showRequirementDialog.value = false;
    };

    const handleProjectChange = (projectId) => {
      console.log('项目变更:', projectId);
      // 这里可以根据项目ID更新开发人员和分支列表
    };

    return {
      showBugDialog,
      showRequirementDialog,
      projectsList,
      projectDevelopers,
      projectBranches,
      handleBugSubmit,
      handleRequirementSubmit,
      handleProjectChange
    };
  }
};
</script>

<style scoped>
.test-container {
  padding: 20px;
}

.button-group {
  margin: 20px 0;
  display: flex;
  gap: 16px;
}

.info-section {
  background-color: #f5f7fa;
  padding: 20px;
  border-radius: 8px;
  margin: 20px 0;
}

.info-section h3 {
  margin-top: 0;
  color: #409eff;
}

.info-section ul {
  margin: 10px 0;
  padding-left: 20px;
}

.info-section li {
  margin: 8px 0;
  line-height: 1.5;
}
</style>
