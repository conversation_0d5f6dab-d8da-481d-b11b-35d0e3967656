<template>
  <div class="test-container">
    <h2>测试统一需求/漏洞表单组件</h2>

    <div class="button-group">
      <el-button type="primary" @click="showBugDialog = true">
        测试上报漏洞
      </el-button>
      <el-button type="success" @click="showRequirementDialog = true">
        测试添加需求
      </el-button>
    </div>

    <div class="info-section">
      <h3>📋 统一表单组件 - HTML布局风格</h3>
      <div class="feature-grid">
        <div class="feature-item">
          <h4>🎯 完全统一</h4>
          <p>一个组件处理两种业务场景，相同字段在不同模式下保持完全一致的外观和交互</p>
        </div>
        <div class="feature-item">
          <h4>📐 HTML布局风格</h4>
          <p>参照传统HTML表单布局，使用div+label+input的经典结构，更加直观易懂</p>
        </div>
        <div class="feature-item">
          <h4>📏 UI规范遵循</h4>
          <p>严格遵循项目UI规范文档，使用标准的颜色、字体、间距和组件样式</p>
        </div>
        <div class="feature-item">
          <h4>🔄 智能模式切换</h4>
          <p>根据模式自动显示/隐藏字段，漏洞模式自动设置优先级和时间</p>
        </div>
      </div>

      <div class="comparison-section">
        <h4>🔍 设计对比</h4>
        <div class="comparison-grid">
          <div class="comparison-item before">
            <h5>❌ 之前的问题</h5>
            <ul>
              <li>两个独立对话框，布局不一致</li>
              <li>相同字段在不同场景下样式差异明显</li>
              <li>Element Plus表单布局复杂</li>
              <li>维护成本高，需要同步两套代码</li>
            </ul>
          </div>
          <div class="comparison-item after">
            <h5>✅ 现在的优势</h5>
            <ul>
              <li>统一组件，完全一致的布局和样式</li>
              <li>HTML风格布局，简洁直观</li>
              <li>严格遵循UI规范文档的设计标准</li>
              <li>单一组件，易于维护和扩展</li>
            </ul>
          </div>
        </div>
      </div>

      <div class="ui-specs">
        <h4>📏 设计规范</h4>
        <div class="specs-grid">
          <div class="spec-item">
            <h5>📐 布局规范</h5>
            <ul>
              <li><strong>对话框宽度</strong>：800px（参照HTML示例）</li>
              <li><strong>表单字段间距</strong>：15px</li>
              <li><strong>标签下边距</strong>：8px</li>
              <li><strong>按钮区域上边距</strong>：20px</li>
            </ul>
          </div>
          <div class="spec-item">
            <h5>🎨 样式规范</h5>
            <ul>
              <li><strong>圆角</strong>：10px（对话框）、6px（输入框）</li>
              <li><strong>输入框高度</strong>：40px</li>
              <li><strong>边框颜色</strong>：#e5e6eb</li>
              <li><strong>主色调</strong>：#1877f2（品牌蓝）</li>
            </ul>
          </div>
          <div class="spec-item">
            <h5>🔤 文字规范</h5>
            <ul>
              <li><strong>标题字体</strong>：18px，500权重</li>
              <li><strong>标签字体</strong>：14px，500权重</li>
              <li><strong>输入框字体</strong>：14px</li>
              <li><strong>按钮字体</strong>：15px，500权重</li>
            </ul>
          </div>
        </div>
      </div>
    </div>

    <!-- 上报漏洞测试 -->
    <RequirementFormDialog
      v-model:visible="showBugDialog"
      mode="bug"
      :projects-list="projectsList"
      :project-developers="projectDevelopers"
      :project-branches="projectBranches"
      @submit="handleBugSubmit"
      @project-change="handleProjectChange"
    />

    <!-- 添加需求测试 -->
    <RequirementFormDialog
      v-model:visible="showRequirementDialog"
      mode="requirement"
      :project-id="1"
      :project-developers="projectDevelopers"
      :project-branches="projectBranches"
      @submit="handleRequirementSubmit"
    />
  </div>
</template>

<script>
import { ref } from 'vue';
import { ElMessage } from 'element-plus';
import RequirementFormDialog from '@/components/RequirementFormDialog.vue';

export default {
  name: 'TestRequirementForm',
  components: {
    RequirementFormDialog
  },
  setup() {
    const showBugDialog = ref(false);
    const showRequirementDialog = ref(false);

    // 模拟数据
    const projectsList = ref([
      { id: 1, name: '测试项目1' },
      { id: 2, name: '测试项目2' }
    ]);

    const projectDevelopers = ref([
      { id: 1, name: '张三', username: 'zhangsan' },
      { id: 2, name: '李四', username: 'lisi' }
    ]);

    const projectBranches = ref([
      { name: 'main' },
      { name: 'develop' },
      { name: 'feature/test' }
    ]);

    const handleBugSubmit = (formData) => {
      console.log('漏洞提交数据:', formData);
      ElMessage.success('漏洞提交成功（测试）');
      showBugDialog.value = false;
    };

    const handleRequirementSubmit = (formData) => {
      console.log('需求提交数据:', formData);
      ElMessage.success('需求提交成功（测试）');
      showRequirementDialog.value = false;
    };

    const handleProjectChange = (projectId) => {
      console.log('项目变更:', projectId);
      // 这里可以根据项目ID更新开发人员和分支列表
    };

    return {
      showBugDialog,
      showRequirementDialog,
      projectsList,
      projectDevelopers,
      projectBranches,
      handleBugSubmit,
      handleRequirementSubmit,
      handleProjectChange
    };
  }
};
</script>

<style scoped>
.test-container {
  padding: 20px;
}

.button-group {
  margin: 20px 0;
  display: flex;
  gap: 16px;
}

.info-section {
  background: #fff;
  padding: 24px;
  border-radius: 10px;
  margin: 24px 0;
  box-shadow: 0 2px 12px rgba(0,0,0,0.04);
  border: 1px solid #e5e6eb;
}

.info-section h3 {
  margin-top: 0;
  margin-bottom: 20px;
  color: #222;
  font-size: 18px;
  font-weight: 500;
  border-bottom: 1px solid #e5e6eb;
  padding-bottom: 8px;
}

.feature-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.feature-item {
  background: #fafafa;
  padding: 16px;
  border-radius: 6px;
  border: 1px solid #e5e6eb;
}

.feature-item h4 {
  margin: 0 0 8px 0;
  color: #1877f2;
  font-size: 14px;
  font-weight: 500;
}

.feature-item p {
  margin: 0;
  color: #666;
  font-size: 14px;
  line-height: 1.5;
}

.comparison-section {
  margin-top: 24px;
  padding-top: 20px;
  border-top: 1px solid #e5e6eb;
}

.comparison-section h4 {
  margin: 0 0 16px 0;
  color: #222;
  font-size: 16px;
  font-weight: 500;
}

.comparison-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-bottom: 24px;
}

.comparison-item {
  background: #fafafa;
  padding: 16px;
  border-radius: 6px;
  border: 1px solid #e5e6eb;
}

.comparison-item.before {
  border-left: 3px solid #ff4d4f;
}

.comparison-item.after {
  border-left: 3px solid #52c41a;
}

.comparison-item h5 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 500;
  color: #222;
}

.comparison-item ul {
  margin: 0;
  padding-left: 16px;
}

.comparison-item li {
  margin: 6px 0;
  font-size: 14px;
  line-height: 1.4;
  color: #666;
}

.ui-specs {
  margin-top: 24px;
  padding-top: 20px;
  border-top: 1px solid #e5e6eb;
}

.ui-specs h4 {
  margin: 0 0 16px 0;
  color: #222;
  font-size: 16px;
  font-weight: 500;
}

.ui-specs ul {
  margin: 0;
  padding-left: 16px;
  background: #fafafa;
  padding: 16px;
  border-radius: 6px;
  border: 1px solid #e5e6eb;
}

.ui-specs li {
  margin: 8px 0;
  font-size: 14px;
  line-height: 1.4;
  color: #666;
}

.ui-specs strong {
  color: #1877f2;
}

@media (max-width: 768px) {
  .feature-grid {
    grid-template-columns: 1fr;
  }

  .comparison-grid {
    grid-template-columns: 1fr;
  }

  .info-section {
    padding: 16px;
  }
}
</style>
