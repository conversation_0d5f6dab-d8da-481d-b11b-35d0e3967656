# 系统前端

基于 Vue3 + Vite + Element Plus + Axios 的前端项目

## 技术栈

- Vue 3
- Vite
- Vue Router
- Element Plus
- Axios

## 项目结构

```
src/
  ├── assets/        # 静态资源
  ├── components/    # 公共组件
  ├── router/        # 路由配置
  ├── utils/         # 工具函数
  ├── views/         # 页面组件
  ├── App.vue        # 根组件
  ├── main.js        # 入口文件
  └── style.css      # 全局样式
```

## API 请求规范

### 请求格式

所有请求都使用 JSON 格式，请求头包含：
```
Content-Type: application/json
Authorization: Bearer ${token}  // 需要认证的接口
```

### 响应格式

所有接口响应统一使用以下格式：

```json
{
  "code": 200,           // 状态码：200成功，其他表示失败
  "message": "success",  // 响应消息
  "data": {             // 响应数据
    // 具体数据字段
  }
}
```

### 状态码说明

- 200: 请求成功
- 400: 请求参数错误
- 401: 未授权或token过期
- 403: 权限不足
- 404: 资源不存在
- 500: 服务器内部错误

## 功能特性

- 登录页面
- 忘记密码入口
- 路由权限控制
- Axios 请求/响应拦截

## 开发环境启动

```bash
# 安装依赖
npm install

# 开发环境启动
npm run dev
```

## 生产环境构建

```bash
# 构建生产环境代码
npm run build
```
